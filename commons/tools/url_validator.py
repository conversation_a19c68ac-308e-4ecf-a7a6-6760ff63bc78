# coding: utf-8
import re
import logging

URL_PATTERN = re.compile(
    r'^https?://'  # http:// 或 https://
    r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # 域名
    r'localhost|'  # 本地地址
    r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # IPv4地址
    r'(?::\d+)?'  # 端口号
    r'(?:/?|[/?]\S+)$', re.IGNORECASE)

def validate_http_url(url: str) -> bool:
    if not URL_PATTERN.match(url):
        logging.error(f"Invalid URL format: {url}")
        return False
    return True

if __name__ == '__main__':
    # url = validate_http_url("http:///office_exporter/2354546546757/asd232RERT23?Expires=214efewgfewgWERWE3&KSSAccess=dsakdgbj235EWR")
    url = validate_http_url("http://kna-model.ks3-cn-beijing.ksyuncs.com/insight-ai/doc-parser/edd25bc355d343cc8413c0f73d658d66.png?Signature=DY4Wqw%2F5d7aM12aSduuTydSWzK4%3D&Expires=3235722162&KSSAccessKeyId=AKLT3OWV80e4QWe6KQ5x8Viz")
    print(url)