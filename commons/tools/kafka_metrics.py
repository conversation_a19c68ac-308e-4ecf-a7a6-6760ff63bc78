import asyncio
from aiokafka import AIOKafkaConsumer
from aiokafka.admin import AIOKafkaAdminClient
from aiokafka.structs import TopicPartition
import logging
from typing import List

from commons.tools.utils import Singleton
from commons.db.kafkadao import KafkaAdminClient, KafkaConsumerDao


class KafkaConsumerMetrics(object, metaclass=Singleton):
    def __init__(self):
        self.bootstrap_servers = None

    def init(self, bootstrap_servers: List[str]):
        """
        Kafka 消费者指标监控类

        Args:
            bootstrap_servers: Kafka服务器地址(例如：'localhost:9092')
        """
        self.bootstrap_servers = bootstrap_servers


    async def get_lag(self, topic_name: str, group_id: str) -> int:
        """
        异步获取Kafka指定topic和consumer group的消息堆积数量

        Args:
            topic_name: 要检查的topic名称
            group_id: 要监控的consumer group ID

        Returns:
            tuple: 对应topic和消费者组的堆积量

        Raises:
            Exception: 当执行过程中发生错误时抛出
        """
        consumer = None
        admin_client = KafkaAdminClient().get_client()
        try:
            consumer = await KafkaConsumerDao(
                topics=[],
                bootstrap_servers=self.bootstrap_servers,
                group_id=group_id,
                auto_offset_reset="latest"
            ).get_consumer()
            partition_ids = await self._get_partition_ids(admin_client, topic_name)
            topic_partitions = self._create_topic_partitions(partition_ids, topic_name)

            consumer.assign(topic_partitions)
            committed = await self._get_committed_offsets(consumer, topic_partitions)
            end_offsets = await self._get_end_offsets(consumer, topic_partitions)

            total_lag = 0
            for pid in partition_ids:
                lag = end_offsets[pid] - committed[pid]
                total_lag += lag

            return total_lag
        except Exception as e:
            logging.error(f"获取Kafka消息堆积数量失败: {str(e)}")
            raise
        finally:
            await self._close_consumer(consumer)

    async def get_throughput(
            self,
            parse_config: List[dict],
            interval_seconds: float = 300.0
    ) -> List[dict]:
        """
        批量计算多个消费者组的消息消费速率

        Args:
            parse_config: Kafka配置列表，每个元素需包含 "topic" 和 "consumer_group_id"
            interval_seconds: 采样间隔时间（默认300秒）

        Returns:
            List[dict]: 每个消费者组的吞吐量数据，格式：
                [{"topic": "t1", "group_id": "g1", "throughput": 100.0}, ...]

        Raises:
            ValueError: 当参数不合法时抛出
        """
        if interval_seconds <= 0.0:
            raise ValueError("采样间隔时间必须大于0")

        # 阶段1: 初始化所有消费者并记录起始偏移量
        clients = []  # 存储 (consumer, topic, group_id, partitions)
        start_offsets = {}
        try:
            admin_client = KafkaAdminClient().get_client()
            for config in parse_config:
                topic = config["topic"]
                group_id = config["consumer_group_id"]

                # 创建消费者
                consumer = await KafkaConsumerDao(
                    topics=[],
                    bootstrap_servers=self.bootstrap_servers,
                    group_id=group_id,
                    auto_offset_reset="latest"
                ).get_consumer()
                # 获取分区信息
                partition_ids = await self._get_partition_ids(admin_client, topic)
                partitions = self._create_topic_partitions(partition_ids, topic)

                # 分配分区并记录起始偏移量
                consumer.assign(partitions)
                committed = await self._get_committed_offsets(consumer, partitions)

                # 保存关键数据
                clients.append((consumer, topic, group_id, partitions))
                start_offsets[(topic, group_id)] = committed

                # 解除分区分配以准备等待
                consumer.assign([])

            await asyncio.sleep(interval_seconds)

            results = []
            for consumer, topic, group_id, partitions in clients:
                try:
                    # 重新分配分区
                    consumer.assign(partitions)
                    # 获取结束偏移量
                    end_offsets = await self._get_committed_offsets(consumer, partitions)
                    # 计算吞吐量
                    throughput = self._calculate_throughput(
                        start_offsets[(topic, group_id)],
                        end_offsets,
                        interval_seconds
                    )
                    results.append({
                        "topic": topic,
                        "group_id": group_id,
                        "throughput": throughput
                    })
                except Exception as e:
                    logging.error(f"计算 {topic}/{group_id} 吞吐量失败: {str(e)}")
                    continue

            return results

        finally:
            # 确保所有客户端连接关闭
            for consumer, _, _, _ in clients:
                await consumer.stop()

    async def _get_partition_ids(self, admin_client, topic_name: str):
        """获取分区ID列表"""
        cluster_info = await admin_client.describe_topics([topic_name])
        return [p["partition"] for p in cluster_info[0]["partitions"]]

    def _create_topic_partitions(self, partition_ids, topic_name: str):
        """创建 TopicPartition 对象列表"""
        return [TopicPartition(topic_name, pid) for pid in partition_ids]

    async def _get_committed_offsets(self, consumer, partitions):
        """获取已提交offset"""
        offsets = {}
        for tp in partitions:
            try:
                committed = await consumer.committed(tp)
                if committed is None:
                    start_offsets = await consumer.beginning_offsets([tp])
                    committed = start_offsets[tp]
                offsets[tp.partition] = committed
            except Exception as e:
                logging.warning(f"获取分区{tp.partition}提交offset失败: {str(e)}")
                offsets[tp.partition] = 0
        return offsets

    async def _get_end_offsets(self, consumer, partitions):
        """获取最新offset"""
        await consumer.seek_to_end()
        return {tp.partition: await consumer.position(tp) for tp in partitions}

    def _calculate_throughput(self, start, end, interval):
        """计算消费速率"""
        if not start or not end:
            raise RuntimeError("无法获取有效偏移量数据")

        total_throughput = 0.0
        for pid in start:
            if pid not in end:
                raise RuntimeError(f"分区 {pid} 在采样期间消失")

            delta = end[pid] - start[pid]
            if delta < 0:
                logging.warning(f"分区 {pid} 偏移量减少，可能发生重置")
                delta = 0

            rate = delta / interval
            total_throughput += rate
        return round(total_throughput, 3)

    async def _close_consumer(self, consumer):
        """关闭客户端连接"""
        if consumer is not None:
            await consumer.stop()



if __name__ == "__main__":
    async def main():
        # 示例：使用方法
        bootstrap_servers = "localhost:9092"  # 替换为你的Kafka服务器地址
        topic_name = "aidocs_parse_server_topic_dev_normal"  # 替换为你要检查的topic名称
        group_id = "aidocs_parse_server_group_id_dev_normal"  # 替换为你要检查的consumer group ID

        KafkaConsumerMetrics().init(bootstrap_servers=[bootstrap_servers])
        try:
            # 获取堆积量
            total_lag = await KafkaConsumerMetrics().get_lag(topic_name, group_id)
            print(f"Topic '{topic_name}' 在消费者组 '{group_id}' 的消息堆积情况:")
            print(f"总堆积消息数: {total_lag}")


            # 获取消费速率
            total_tp = await KafkaConsumerMetrics().get_throughput(topic_name, group_id, 60.0)
            print(f"\n消费速率: {total_tp:.2f} 条/秒")


            total_lag = await KafkaConsumerMetrics().get_lag(topic_name, group_id)
            print(f"Topic '{topic_name}' 在消费者组 '{group_id}' 的消息堆积情况:")
            print(f"总堆积消息数: {total_lag}")



        except Exception as e:
            print(f"执行过程中发生错误: {e}")
    # 运行异步函数
    asyncio.run(main())