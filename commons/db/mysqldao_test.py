# coding: utf-8
# <AUTHOR> wangziyang1
# @date    : 2025/4/2 14:12

import unittest

from commons.db.mysqldao import MysqlDao, sqlsession, SQLBase
from sqlalchemy import Column, BigInteger, DateTime, String, TEXT
import datetime
from conf import ConfDB


class TestTableBase(SQLBase):
    __tablename__ = "test_table"
    key_id = Column("id", BigInteger, primary_key=True, autoincrement=True)
    token = Column(String(100))
    text = Column(TEXT)


class TestTable(TestTableBase):
    ctime = Column(DateTime, default=None)
    mtime = Column(DateTime, default=None)

    def to_dict(self):
        return {
            "key_id": self.key_id,
            "token": self.token,
            "text": self.text,
            "ctime": self.ctime,
            "mtime": self.mtime
        }


class MysqlDaoTest(unittest.TestCase):
    def setUp(self):
        ConfDB.load()
        MysqlDao().init(
            host=ConfDB.host,
            port=ConfDB.port,
            user=ConfDB.user,
            pwd=ConfDB.pwd,
            db="test_wzy",
            env=ConfDB.env,
            pools=ConfDB.pools,
            is_debug=True
        )
        self.db = MysqlDao().sess()

    def tearDown(self):
        MysqlDao().close()

    def test_query(self):
        res = self.db.query(TestTable).filter(TestTable.token == "token").first()
        if res is not None:
            print(res.to_dict())
        else:
            print("query result is None")

    def test_insert(self):
        self.db.add(TestTableBase(token="token2", text="text"))
        self.db.commit()
        # self.db.flush()

    def test_update(self):
        self.db.query(TestTable).filter(TestTable.token == "token").update({"text": "text2"})
        self.db.commit()

    def test_delete(self):
        self.db.query(TestTable).filter(TestTable.token == "token").delete()
        self.db.commit()
