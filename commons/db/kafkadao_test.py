import asyncio
import unittest
from commons.db.kafkadao import KafkaProducerDao, KafkaConsumerDao, KafkaAdminClient
from commons.logger.logger import init_logger


class ModulesTest(unittest.IsolatedAsyncioTestCase):

    async def asyncSetUp(self):
        init_logger("", "DEBUG", "err", True)
        self.hosts = ["127.0.0.1:9092"]
        self.level = "low"
        self.topic_name = "test-topic9"
        self.group_id = "test-group7"
        await KafkaProducerDao().init({self.level: self.topic_name}, self.hosts)
        await KafkaAdminClient().init(self.hosts)

    async def asyncTearDown(self):
        await KafkaProducerDao().close()
        await KafkaAdminClient().close()

    async def test_create_topic(self):
        res = await KafkaAdminClient().create_topic(self.topic_name, 6, 1, topic_config={"retention.ms": "3600000"})
        print("create_topic:", res)

    async def test_update_topic(self):
        res = await KafkaAdminClient().update_topic_config(self.topic_name,topic_config={"retention.ms": "3600000"})
        print("update_topic_config:", res)
        res = await KafkaAdminClient().update_topic_partitions(self.topic_name, 5)
        print("update_topic_partitions:", res)
        res = await KafkaAdminClient().update_topic_partitions(self.topic_name, 4)
        print("update_topic_partitions:", res)

    async def test_producer(self):
        await KafkaProducerDao().asend_message(self.level, {"test": "test"})

    async def test_consumer(self):
        kd = KafkaConsumerDao(self.hosts, [self.topic_name], self.group_id)
        i = 0
        async for m in kd.test_consume_messages():
            if i == 10:
                break
            print(m)
            i += 1

    async def test_consumer2(self):
        kd = KafkaConsumerDao(self.hosts, [self.topic_name], self.group_id)
        consumer = await kd.get_consumer()
        async for m in consumer:
            print(m)
            await consumer.commit()
            break

    async def test_del_topic(self):
        res = await KafkaAdminClient().del_topic(self.topic_name)
        print("del_topic:", res)