# coding: utf-8
# <AUTHOR> wangziyang1
# @date    : 2024/4/28 17:02

import logging
import minio
import io
from datetime import timedelta

from commons.tools.utils import Singleton


class MinioDao(object, metaclass=Singleton):
    def __init__(self):
        self.prefix = ""
        self.conn: minio.Minio = None

    def init(self, host: str, ak: str, sk: str, bucket: str):
        self.bucket = bucket
        self.conn = minio.Minio(endpoint=host, access_key=ak, secret_key=sk, secure=False, region="us-east-1")

    def upload_from_bytes(self, mpath: str, data: bytes) -> bool:
        try:
            self.conn.put_object(bucket_name=self.bucket, object_name=mpath, data=io.BytesIO(data), length=len(data))
            return True
        except Exception as e:
            logging.error(e)
            return False

    def upload_from_text(self, mpath: str, text: str) -> bool:
        return self.upload_from_bytes(mpath, text.encode("utf-8"))

    def upload_from_file(self, mpath: str, fpath: str) -> bool:
        try:
            self.conn.fput_object(bucket_name=self.bucket, object_name=mpath, file_path=fpath)
            return True
        except Exception as e:
            logging.error(e)
            return False

    def download_to_text(self, mpath: str) -> (bool, str):
        try:
            data = self.conn.get_object(bucket_name=self.bucket, object_name=mpath)
            return True, data.data.decode("utf-8")
        except Exception as e:
            logging.error(mpath)
            logging.exception(e)
            return False, ""

    def download_to_file(self, mpath: str, fpath: str) -> bool:
        try:
            data = self.conn.get_object(bucket_name=self.bucket, object_name=mpath)
            with open(fpath, "wb") as f:
                for d in data.stream(32 * 1024):
                    f.write(d)
            return True
        except Exception as e:
            logging.exception(e)
            return False

    def generate_url(self, mpath: str, timeout: int) -> str:
        try:
            url = self.conn.get_presigned_url("GET", bucket_name=self.bucket, object_name=mpath,
                                              expires=timedelta(days=timeout))
            return url
        except Exception as e:
            logging.error(e)
            return ""

    def delete_by_key(self, mpath: str) -> bool:
        try:
            self.conn.remove_object(bucket_name=self.bucket, object_name=mpath)
            return True
        except Exception as e:
            logging.exception(e)
            return False


if __name__ == '__main__':
    MinioDao().init("127.0.0.1:8000", "ak", "sk", "bucket")
    prifix = "encs/cams/m/kna_insightai/0"
    mpath = f"{prifix}/test"
    res = MinioDao().upload_from_text(mpath=mpath, text="test")
    print(res)
    res = MinioDao().download_to_text(mpath=mpath)
    print(res)
    MinioDao().generate_url(mpath, timeout=1)
    print(res)
    MinioDao().download_to_file(mpath=mpath, fpath="t")
    print(res)
    MinioDao().delete_by_key(mpath=mpath)
    print(res)
    MinioDao().upload_from_file(mpath=mpath, fpath="t")
    print(res)
