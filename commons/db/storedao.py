# coding: utf-8
# <AUTHOR> wang<PERSON><PERSON>1
# @date    : 2024/4/28 17:29


from enum import Enum
from typing import Optional
from commons.tools.utils import Singleton
from commons.db.ks3dao import KS3Tool
from commons.db.miniodao import MinioDao


class StoreType(str, Enum):
    Ks3 = "ks3"
    Minio = "minio"


class StoreDao(object, metaclass=Singleton):
    def __init__(self):
        self.store_type: Optional[StoreType] = None

    def init(self, host: str, ak: str, sk: str, bucket: str, store_type: StoreType):
        self.store_type = store_type
        if self.store_type == StoreType.Ks3:
            KS3Tool().init(host, ak, sk, bucket)
        elif self.store_type == StoreType.Minio:
            MinioDao().init(host, ak, sk, bucket)
        else:
            raise Exception(f"store_type: {store_type} not support")
        
    
    def _get_tool(self):
        if self.store_type == StoreType.Ks3:
            return KS3Tool()
        elif self.store_type == StoreType.Minio:
            return MinioDao()
        else:
            raise Exception(f"store_type: {self.store_type} not support")

    def upload_from_bytes(self, store_path: str, data: bytes) -> bool:
        """
        用字节流方式上传
        """
        try:
            return self._get_tool().upload_from_bytes(store_path, data)
        except Exception as e:
            # 可加日志
            return False

    async def async_upload_from_bytes(self, store_path: str, data: bytes) -> bool:
        """
        字节流异步上传
        """
        try:
            tool = self._get_tool()
            if hasattr(tool, "async_upload_from_bytes"):
                return await tool.async_upload_from_bytes(store_path, data)
            else:
                # 同步
                return tool.upload_from_bytes(store_path, data)
        except Exception as e:
            # 可加日志
            return False


    def upload_from_text(self, store_path: str, text: str) -> bool:
        return self.upload_from_bytes(store_path, text.encode("utf-8"))

    def upload_from_file(self, store_path: str, fpath: str) -> bool:
        """ 
        文件上传
        """
        try:
            return self._get_tool().upload_from_file(store_path, fpath)
        except Exception as e:
            # 可加日志
            return False

    def download_to_text(self, store_path: str) -> (bool, str):
        """
        文本下载
        """
        try:
            return self._get_tool().download_to_text(store_path)
        except Exception as e:
            # 可加日志
            return False, ""

    def download_to_file(self, store_path: str, fpath: str) -> bool:
        """
        文件下载
        """
        try:
            return self._get_tool().download_to_file(store_path, fpath)
        except Exception as e:
            # 可加日志
            return False


    def generate_url(self, store_path: str, timeout: int) -> str:
        """
        生成url
        """
        try:
            return self._get_tool().generate_url(store_path, timeout)
        except Exception as e:
            # 可加日志
            return ""

    async def async_generate_url(self, store_path: str, timeout: int) -> str:
        try:
            tool = self._get_tool()
            if hasattr(tool, "async_generate_url"):
                # 异步
                return await tool.async_generate_url(store_path, timeout)
            else:
                # 同步
                return tool.generate_url(store_path, timeout)
        except Exception as e:
            # 可加日志
            return ""

    def delete_by_key(self, store_path: str) -> bool:
        """
        删除文件
        """
        try:
            return self._get_tool().delete_by_key(store_path)
        except Exception as e:
            # 可加日志
            return False

    def list_dirs(self, store_path: str) -> list:
        try:
            if self.store_type == StoreType.Ks3:
                return KS3Tool().list_dir(store_path)
            else:
                return []
        except Exception as e:
            return []

    def list_files(self, store_path: str) -> list:
        try:
            if self.store_type == StoreType.Ks3:
                return KS3Tool().list_file(store_path)
            else:
                return []
        except Exception as e:
            return []
