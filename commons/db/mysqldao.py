# coding:utf-8
# @Author:huangzhishou
# @date:2022/10/12 16:25

from commons.tools.utils import Singleton

from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from urllib.parse import quote_plus
from contextlib import contextmanager
SQLBase = declarative_base()


class MysqlDao(object, metaclass=Singleton):
    def __init__(self):
        self.sess: sessionmaker = None
        self.engine = None
        self.env = None

    def is_init(self):
        return self.sess is not None

    def init(self, host, port, user, pwd, db, pools, env, is_debug=False):
        self.env = env
        encoded_password = quote_plus(pwd)
        conn_sql = f"mysql+pymysql://{user}:{encoded_password}@{host}:{port}/{db}?charset=utf8mb4"
        self.engine = create_engine(
            conn_sql,
            pool_size=pools,
            max_overflow=pools,
            pool_pre_ping=True,
            echo=is_debug)
        self.engine.connect()
        self.sess = sessionmaker(bind=self.engine, autocommit=False, autoflush=True)

    def close(self):
        if self.engine:
            self.engine.dispose()

@contextmanager
def sqlsession() -> Session:
    sess = MysqlDao().sess()
    try:
        yield sess
        sess.commit()
    except Exception as e:
        sess.rollback()
        raise e
    finally:
        sess.expire_all()
        sess.close()

