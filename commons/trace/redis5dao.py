# coding:utf-8
# @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
# @date:   2019/07/05 12:09:27

import logging
import aiohttp
import redis
from redis import asyncio as aredis
from redis.cluster import ClusterNode
from typing import List, Optional, Union, Tuple
import json
import time
import requests
from fastapi import status
from pydantic import BaseModel
from enum import Enum

from commons.tools.utils import Singleton
from commons.auth.auth_rpc import sig_wps4

class CommonData(BaseModel):
    val: Optional[str] = None


class CommonResp(BaseModel):
    code: int
    msg: str
    data: Optional[CommonData] = None


class RespCode(int, Enum):
    OK = 0


class TraceRedis5Dao(object, metaclass=Singleton):
    """
    redis连接类，支持单机/集群版，同步/异步接口，公网/私有化连接，ipv4/ipv6
    """

    def __init__(self):
        self._hosts = []
        self._password = None
        self.rd: Union[redis.Redis, redis.RedisCluster, None] = None
        self.ard: Union[aredis.Redis, aredis.RedisCluster, None] = None
        self._prefix = ""
        self._cluster = True
        # 私有化二开配置
        self._cams_ak = None
        self._cams_sk = None
        self._cams_host = None
        self.is_init = False

    def init(self, hosts: List, password, prefix, cluster):
        self._hosts = hosts
        self._password = password
        self._cluster = cluster
        self.rd = self._connect()
        # self.ard = await self._aconnect()
        # 私有化redis必须有统一的前缀
        self._prefix = prefix
        self.is_init = True

    def _connect(self):
        cluster_nodes = []
        for h in self._hosts:
            # 兼容ipv6
            node = h.rsplit(":", 1)
            host = node[0].replace("[", "").replace("]", "")
            cluster_nodes.append(ClusterNode(host=host, port=int(node[1])))
        if self._cluster:
            logging.info(f"cluster:{self._cluster}")
            return redis.RedisCluster(startup_nodes=cluster_nodes, password=self._password)
        else:
            rdp = redis.ConnectionPool(host=cluster_nodes[0].host, port=cluster_nodes[0].port, password=self._password)
            return redis.Redis(connection_pool=rdp)

    async def _aconnect(self):
        cluster_nodes = []
        for h in self._hosts:
            # 兼容ipv6
            node = h.rsplit(":", 1)
            host = node[0].replace("[", "").replace("]", "")
            cluster_nodes.append(ClusterNode(host=host, port=int(node[1])))
        if self._cluster:
            return await aredis.RedisCluster(startup_nodes=cluster_nodes, password=self._password)
        else:
            rdp = aredis.ConnectionPool(host=cluster_nodes[0].host, port=cluster_nodes[0].port, password=self._password)
            return await aredis.Redis(connection_pool=rdp)

    async def close(self):
        if self.rd:
            self.rd.close()
        if self.ard:
            if self._cluster:
                await self.ard.aclose()
            else:
                await self.ard.aclose(True)

    # 私有化二开的redis接口
    def init_cams(self, ak: str, sk: str, host: str, prefix: str):
        self._cams_ak = ak
        self._cams_sk = sk
        # http://{{inner_host}}/i/cams
        self._cams_host = host
        # 私有化redis必须有统一的前缀
        self._prefix = prefix

    def _sig_cams(self, method: str, uri: str, body: dict = None) -> dict:
        if body:
            body = json.dumps(body).encode("utf-8")
        date = time.strftime("%a, %d %b %Y %H:%M:%S GMT", time.gmtime())
        return sig_wps4(method, uri, body, self._cams_ak, self._cams_sk, date)

    def _call_cams(self, uri: str, body: dict = None) -> CommonResp:
        headers = self._sig_cams("POST", uri, body)
        url = f"{self._cams_host}{uri}"
        r = requests.post(url, json=body, headers=headers)
        if r.status_code != status.HTTP_200_OK:
            err_text = f"cams redis rpc request fail, url: {url}, status_code: {r.status_code}, res_text: {r.text}"
            logging.error(err_text)
            raise Exception(err_text)
        resp: CommonResp = CommonResp.parse_raw(r.text)
        if resp.code != RespCode.OK:
            err_text = f"cams redis rpc request fail, url: {url}, status_code: {r.status_code}, res_text: {r.text}"
            logging.error(err_text)
            raise Exception(err_text)
        return resp

    async def _acall_cams(self, uri: str, body: dict = None) -> CommonResp:
        headers = self._sig_cams("POST", uri, body)
        url = f"{self._cams_host}{uri}"
        async with aiohttp.ClientSession() as sess:
            async with sess.post(url, json=body, headers=headers) as r:
                code = r.status
                text = await r.text(encoding="utf-8")
                if code != status.HTTP_200_OK:
                    err_text = f"cams redis rpc request fail, url: {url}, status_code: {code}, res_text: {text}"
                    logging.error(err_text)
                    raise Exception(err_text)
                resp: CommonResp = CommonResp.parse_raw(text)
                if resp.code != RespCode.OK:
                    err_text = f"cams redis rpc request fail, url: {url}, status_code: {code}, res_text: {text}"
                    logging.error(err_text)
                    raise Exception(err_text)
                return resp

    def _set(self, name, value, ex=None, nx=False):
        return self.rd.set(f"{self._prefix}{name}", value, ex=ex, nx=nx)

    async def _aset(self, name, value, ex=None, nx=False):
        return await self.ard.set(f"{self._prefix}{name}", value, ex=ex, nx=nx)

    def _set_cams(self, name: str, value, ex: int = None):
        uri = "/sdk/api/v1/cache/set"
        body = {
            "key": f"{self._prefix}{name}",
            "val": str(value)
        }
        if ex:
            body.update({"expire": ex})
        res = self._call_cams(uri, body)
        if res.code == RespCode.OK:
            return True
        else:
            return False

    async def _aset_cams(self, name: str, value, ex: int = None):
        uri = "/sdk/api/v1/cache/set"
        body = {
            "key": f"{self._prefix}{name}",
            "val": str(value)
        }
        if ex:
            body.update({"expire": ex})
        res = await self._acall_cams(uri, body)
        if res.code == RespCode.OK:
            return True
        else:
            return False

    def set(self, name, value, ex=None, nx=False):
        if not self._cams_host:
            try:
                return self._set(name, value, ex, nx)
            except (ConnectionError, TimeoutError) as e:
                logging.warning(e)
                self.rd = self._connect()
                try:
                    return self._set(name, value, ex, nx)
                except Exception as e:
                    logging.exception(e)
                    raise e
            except Exception as e:
                logging.exception(e)
                raise e
        else:
            # 私有化二开的redis接口不支持nx
            return self._set_cams(name, value, ex)

    async def aset(self, name, value, ex=None, nx=False):
        if not self._cams_host:
            try:
                return await self._aset(name, value, ex, nx)
            except (ConnectionError, TimeoutError) as e:
                logging.warning(e)
                self.ard = await self._aconnect()
                try:
                    return await self._aset(name, value, ex, nx)
                except Exception as e:
                    logging.exception(e)
                    raise e
            except Exception as e:
                logging.exception(e)
                raise e
        else:
            # 私有化二开的redis接口不支持nx
            return await self._aset_cams(name, value, ex)

    def _get(self, name, defvalue):
        # 返回val为bytes或None
        val = self.rd.get(f"{self._prefix}{name}")
        return val if val else defvalue

    async def _aget(self, name, defvalue):
        # 返回val为bytes或None
        val = await self.ard.get(f"{self._prefix}{name}")
        return val if val else defvalue

    def _get_cams(self, name: str, defvalue):
        uri = "/sdk/api/v1/cache/get"
        body = {
            "key": f"{self._prefix}{name}"
        }
        res = self._call_cams(uri, body)
        return res.data.val if res.data and res.data.val else defvalue

    async def _aget_cams(self, name: str, defvalue):
        uri = "/sdk/api/v1/cache/get"
        body = {
            "key": f"{self._prefix}{name}"
        }
        res = await self._acall_cams(uri, body)
        return res.data.val if res.data and res.data.val else defvalue

    def get(self, name, defvalue=None):
        if not self._cams_host:
            try:
                return self._get(name, defvalue)
            except (ConnectionError, TimeoutError) as e:
                logging.warning(e)
                self.rd = self._connect()
                try:
                    return self._get(name, defvalue)
                except Exception as e:
                    logging.exception(e)
                    raise e
            except Exception as e:
                logging.exception(e)
                raise e
        else:
            return self._get_cams(name, defvalue)

    async def aget(self, name, defvalue=None):
        if not self._cams_host:
            try:
                return await self._aget(name, defvalue)
            except (ConnectionError, TimeoutError) as e:
                logging.warning(e)
                self.ard = await self._aconnect()
                try:
                    return await self._aget(name, defvalue)
                except Exception as e:
                    logging.exception(e)
                    raise e
            except Exception as e:
                logging.exception(e)
                raise e
        else:
            return await self._aget_cams(name, defvalue)

    def getint(self, name, defvalue=0):
        return int(self.get(name, defvalue))

    async def agetint(self, name, defvalue=0):
        return int(await self.aget(name, defvalue))

    def getstring(self, name, defvalue=""):
        val = self.get(name, defvalue)
        if isinstance(val, bytes):
            return val.decode('utf-8')
        else:
            return val

    async def agetstring(self, name, defvalue=""):
        val = await self.aget(name, defvalue)
        if isinstance(val, bytes):
            return val.decode('utf-8')
        else:
            return val

    def getbool(self, name, defvalue=False):
        return bool(self.get(name, defvalue))

    async def agetbool(self, name, defvalue=False):
        return bool(await self.aget(name, defvalue))

    def _remove(self, name):
        return self.rd.delete(f"{self._prefix}{name}")

    async def _aremove(self, name):
        return await self.ard.delete(f"{self._prefix}{name}")

    def _remove_cams(self, name: str):
        uri = "/sdk/api/v1/cache/del"
        body = {
            "key": f"{self._prefix}{name}"
        }
        res = self._call_cams(uri, body)
        if res.code == RespCode.OK:
            return True
        else:
            return False

    async def _aremove_cams(self, name: str):
        uri = "/sdk/api/v1/cache/del"
        body = {
            "key": f"{self._prefix}{name}"
        }
        res = await self._acall_cams(uri, body)
        if res.code == RespCode.OK:
            return True
        else:
            return False

    def remove(self, name):
        if not self._cams_host:
            return self._remove(name)
        else:
            return self._remove_cams(name)

    async def aremove(self, name):
        if not self._cams_host:
            return await self._aremove(name)
        else:
            return await self._aremove_cams(name)

    def incrby(self, name: str, count: int) -> int:
        if not self._cams_host:
            return self.rd.incrby(f"{self._prefix}{name}", count)
        else:
            v = int(self._get_cams(name, 0)) + count
            if self._set_cams(name, v):
                return v
            else:
                return 0

    async def aincrby(self, name: str, count: int) -> int:
        if not self._cams_host:
            return await self.ard.incrby(f"{self._prefix}{name}", count)
        else:
            v = int(await self._aget_cams(name, 0)) + count
            if await self._aset_cams(name, v):
                return v
            else:
                return 0

    def expire(self, name: str, time: int) -> bool:
        if not self._cams_host:
            return self.rd.expire(f"{self._prefix}{name}", time)
        else:
            v = int(self._get_cams(name, 0))
            return self._set_cams(name, v, ex=time)

    async def aexpire(self, name: str, time: int) -> bool:
        if not self._cams_host:
            return await self.ard.expire(f"{self._prefix}{name}", time)
        else:
            v = int(await self._aget_cams(name, 0))
            return await self._aset_cams(name, v, ex=time)

    def zadd(self, name: str, mapping: dict, nx: bool = False):
        # cams 不支持
        self.rd.zadd(f"{self._prefix}{name}", mapping=mapping, nx=nx)

    async def azadd(self, name: str, mapping: dict, nx: bool = False):
        # cams 不支持
        await self.ard.zadd(f"{self._prefix}{name}", mapping=mapping, nx=nx)

    def zrem(self, name: str, value: any):
        self.rd.zrem(f"{self._prefix}{name}", value)

    async def azrem(self, name: str, value: any):
        await self.ard.zrem(f"{self._prefix}{name}", value)

    def zpopmin(self, name: str, count: int) -> List[Tuple[bytes, float]]:
        return self.rd.zpopmin(f"{self._prefix}{name}", count)

    async def azpopmin(self, name: str, count: int) -> List[Tuple[bytes, float]]:
        return await self.ard.zpopmin(f"{self._prefix}{name}", count)

    def zrang(
            self,
            name: str,
            start: int,
            end: int,
            withscores: bool = False,
            rev: bool = False
    ) -> Union[List[bytes], List[Tuple[bytes, float]]]:
        if rev:
            return self.rd.zrevrange(name=f"{self._prefix}{name}", start=start, end=end, withscores=withscores)
        else:
            return self.rd.zrange(name=f"{self._prefix}{name}", start=start, end=end, withscores=withscores)

    async def azrang(
            self,
            name: str,
            start: int,
            end: int,
            withscores: bool = False,
            rev: bool = False
    ) -> Union[List[bytes], List[Tuple[bytes, float]]]:
        if rev:
            return await self.ard.zrevrange(name=f"{self._prefix}{name}", start=start, end=end, withscores=withscores)
        else:
            return await self.ard.zrange(name=f"{self._prefix}{name}", start=start, end=end, withscores=withscores)

    def zcard(self, name: str) -> int:
        return self.rd.zcard(f"{self._prefix}{name}")

    async def azcard(self, name: str) -> int:
        return await self.ard.zcard(f"{self._prefix}{name}")
