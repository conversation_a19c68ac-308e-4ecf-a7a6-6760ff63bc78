from opentelemetry import trace
import functools
import os
import logging
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from opentelemetry.exporter.otlp.proto.http.trace_exporter import OTLPSpanExporter
from opentelemetry.sdk.trace import TracerProvider as SDKTracerProvider
from opentelemetry.propagate import set_global_textmap
from opentelemetry.sdk.resources import Resource
from opentelemetry.context import Context
import time
import datetime
import inspect
import json
import enum
from contextlib import asynccontextmanager, contextmanager
from opentelemetry.propagators.composite import CompositePropagator
from opentelemetry.propagators.b3 import B3MultiFormat
from pydantic import BaseModel
from sympy import false

from conf import ConfTraceRedis

import os
from commons.trace.redis5dao import TraceRedis5Dao
from commons.logger.logger import (
    trace_id_context,
    span_id_context,
request_id_context,
)

MAX_SPAN_VALUE_LENGTH = 4096
MAX_VALUE_LEN = 2000


class ExtraInfoModel(BaseModel):
    pass

class GlobalTracer:
    _tracer = None
    _verbose = False

    @classmethod
    def init_tracer(cls, service_name="unknown", verbose=False):
        if  verbose:
            ConfTraceRedis.load()
            TraceRedis5Dao().init( ConfTraceRedis.hosts,ConfTraceRedis.password,"trace_dst", ConfTraceRedis.cluster)
        service_name = os.getenv("JAEGER_SERVICE_NAME", service_name)
        endpoint =  "http://jaeger-collector.istio-system:4318/v1/traces"
        if not service_name:
            logging.warning("JAEGER_SERVICE_NAME is not set, skipping OTEL tracer initialization")
            return
        try:
            otlp_endpoint = os.getenv("JAEGER_ENDPOINT", endpoint)

            if not otlp_endpoint:
                logging.error("OTLP endpoint is not configured")
                return

            exporter = OTLPSpanExporter(endpoint=otlp_endpoint)

            resource = Resource.create({
                "service.name": service_name,
                "podName": os.getenv("POD_NAME", ""),
                "podNameSpace": os.getenv("POD_NAMESPACE", ""),
                "nodeIP": os.getenv("NODE_IP", ""),
                "nodeName": os.getenv("NODE_NAME", ""),
                "kaeId": os.getenv("KAE_APP_ID", "")
            })
            provider = SDKTracerProvider(resource=resource)
            processor = BatchSpanProcessor(exporter)
            provider.add_span_processor(processor)

            trace.set_tracer_provider(provider)

            # 设置 B3 header 格式
            set_global_textmap(CompositePropagator([B3MultiFormat()]))

            cls._tracer = trace.get_tracer(service_name)
            cls._initialized = True
            cls._verbose = verbose
            logging.info(f"OpenTelemetry OTLP tracer initialized: service={service_name}, endpoint={otlp_endpoint}")
        except Exception as e:
            logging.exception(f"Failed to initialize OpenTelemetry OTLP tracer: {e}")

    @classmethod
    def get_tracer(cls):
        # 确保初始化已经完成
        if cls._tracer is None:
            logging.warning("Tracer 尚未初始化，返回 NoOpTracer。")
            return trace.NoOpTracer()
        return cls._tracer
    
    @classmethod
    def verbose(cls):
        return cls._verbose

class TraceContextMgr:
    def __init__(self, func, *args, **kwargs):
        self._spread_error = kwargs.pop("__trace_spread_error__", True)
        self._tracer = GlobalTracer.get_tracer()
        self._func = func
        trace_id = trace_id_context.get() or "0"
        span_id = span_id_context.get() or "0"
        self._span_id = span_id
        span_context = trace.SpanContext(
            trace_id=int(trace_id, 16),
            span_id=int(span_id, 16),
            is_remote=True,
            trace_flags=trace.TraceFlags(0x01),
        )
        self._current_context = trace.set_span_in_context(
            trace.NonRecordingSpan(span_context), Context()
        )
        if isinstance(func, str):
            self._full_name = func
        else:
            module_name = func.__module__  # 完整模块路径
            func_name = func.__qualname__
            self._full_name = f"{module_name}.{func_name}"
        trace_name = kwargs.pop("__trace_name__", "")
        if trace_name:
            self._full_name = self._full_name + f" ({trace_name})"
        self._args = args
        self._kwargs = kwargs
        self._extra_infos = []
        self._result = []

    def start(self):
        self._span = self._tracer.start_span(
            self._full_name,
            context = self._current_context,
            start_time = int(time.time() * 1e9),
        )
        span_id_context.set(format(self._span.get_span_context().span_id,"016x"))

    def cleared_args(self):
        return self._args, self._kwargs

    def pick_info(self, result):
        if isinstance(result, tuple):
            clear_result = []
            for res in result:
                if isinstance(res, ExtraInfoModel):
                    self._extra_infos.append(res)
                else:
                    clear_result.append(res)
            if len(clear_result) == 0:
                clear_result = None
            elif len(clear_result) == 1:
                clear_result = clear_result[0]
            else:
                clear_result = tuple(clear_result)
            self._result.append(clear_result)
            return clear_result
        else:
            self._result.append(result)
            return result

    def set_infos(self, **kwargs):
        for key, value in kwargs.items():
            self._span.set_attribute(key, value)

    def _obj_to_json(self, obj):
        obj_id = id(obj)
        if obj_id in self._serialized_obj:
            return f"Rec ref obj {type(obj)}"
        self._serialized_obj.add(obj_id)
        try:
            if isinstance(obj, BaseModel):
                return obj.model_dump()
            elif isinstance(obj, dict):
                return {k: self._obj_to_json(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [self._obj_to_json(item) for item in obj]
            elif isinstance(obj, set):
                return [self._obj_to_json(item) for item in obj]
            elif isinstance(obj, tuple):
                return tuple(self._obj_to_json(item) for item in obj)
            elif isinstance(obj, (int, float, str, bool, type(None))):
                return obj
            elif isinstance(obj, (datetime.datetime, datetime.date, datetime.time)):
                return obj.isoformat()
            elif isinstance(obj, enum.Enum):
                return obj.value
            else:
                return str(obj)
        except Exception as e:
            return f"<Unserializable: {e}>"

    def _verbose(self):
        if not GlobalTracer.verbose():
            return
        try:
            self._serialized_obj = set()
            params = self._kwargs.copy()
            if self._args:
                sig = inspect.signature(self._func)
                params_key = list(sig.parameters.keys())
                for idx, value in enumerate(self._args):
                    params[params_key[idx]] = value
            params_json = json.dumps(self._obj_to_json(params), ensure_ascii=False)
            return_json = json.dumps(self._obj_to_json(self._result), ensure_ascii=False)
            self._span.set_attribute("func.params", params_json[:MAX_VALUE_LEN])
            self._span.set_attribute("func.return", return_json[:MAX_VALUE_LEN])
            if self._full_name == "modules.rpc.kdc_rpc.KDCRpc.aget_content_by_url_or_file":
                import asyncio
                async def _write_to_redis(kwargs_data, return_data):
                    key1 = ":req:" + self._full_name + ":" + request_id_context.get()
                    key2 = ":res:" + self._full_name + ":" + request_id_context.get()
                    logging.debug(f"[TraceContextMgr] Writing to Redis: key1={key1}, key2={key2}")
                    TraceRedis5Dao().set(key1, kwargs_data, ConfTraceRedis.timeout * 3600 * 24)
                    TraceRedis5Dao().set(key2, return_data, ConfTraceRedis.timeout * 3600 * 24)
                asyncio.create_task(_write_to_redis(params_json, return_json))
        except Exception as e:
            logging.info(f"TraceContextMgr.verbose error: {e}")

    def on_exception(self, e:Exception):
        self._span.record_exception(e)
        self._span.set_status(trace.StatusCode.ERROR)
        logging.error(f"TraceContextMgr error: {e}")
        if self._spread_error:
            raise e

    def end(self):
        span_id_context.set(self._span_id)
        self._verbose()
        self._span.set_attribute("extra_infos", str(self._extra_infos))
        self._span.end(end_time=int(time.time() * 1e9))

@asynccontextmanager
async def async_use_span(func, *args, **kwargs):
    trace_context_mgr = TraceContextMgr(func, *args, **kwargs)
    trace_context_mgr.start()
    try:
        yield trace_context_mgr
    except Exception as e:
        trace_context_mgr.on_exception(e)
    finally:
        trace_context_mgr.end()

@contextmanager
def use_span(func, *args, **kwargs):
    trace_context_mgr = TraceContextMgr(func, *args, **kwargs)
    trace_context_mgr.start()
    try:
        yield trace_context_mgr
    except Exception as e:
        trace_context_mgr.on_exception(e)
    finally:
        trace_context_mgr.end()

def trace_span(func):
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        with use_span(func, *args, **kwargs) as trace_ctx_mgr:
            args, kwargs = trace_ctx_mgr.cleared_args()
            result = func(*args, **kwargs)
            result = trace_ctx_mgr.pick_info(result)
        return result
    return wrapper

def async_trace_span(func):
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        async with async_use_span(func, *args, **kwargs) as trace_ctx_mgr:
            args, kwargs = trace_ctx_mgr.cleared_args() 
            result = await func(*args, **kwargs)
            result = trace_ctx_mgr.pick_info(result)
        return result
    return wrapper

def trace_span_generator(func):
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        with use_span(func, *args, **kwargs) as trace_ctx_mgr:
            args, kwargs = trace_ctx_mgr.cleared_args()
            for res in func(*args, **kwargs):
                res = trace_ctx_mgr.pick_info(res)
                yield res
    return wrapper

def async_trace_span_generator(func):
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        async with async_use_span(func, *args, **kwargs) as trace_ctx_mgr:
            args, kwargs = trace_ctx_mgr.cleared_args()
            async for res in func(*args, **kwargs):
                res = trace_ctx_mgr.pick_info(res)
                yield res
    return wrapper