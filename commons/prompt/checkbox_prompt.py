import json
import logging

from commons.llm_gateway.llm import LLModelRpc
from commons.llm_gateway.models.chat_data import SftBaseModelType, Message, SftMultiModalImage, SftMultiModalText, \
    MultiModalType, SftMultiModalImageUrl


def load_json_object(text):
    start_index = text.find("```json")
    end_index = text.rfind("```")
    if start_index != -1 and end_index != -1:
        json_string = text[start_index + len("```json"):end_index].strip()
        try:
            json_object = json.loads(json_string)
            return json_object
        except json.JSONDecodeError:
            print("Error: Invalid JSON format.")
    return text


prompt = """你是一个文档解析纠错专家，主要的任务是根据原始图片与 OCR 解析结果的复选框内容块之间的差异，识别并修正 OCR 中复选框相关内容的错误。你需要找出两种类型的错误，并按照指定规则进行修正。

## 工作流程如下：
1. 结合原始图片和OCR识别结果，找出与图片不符合的内容块，具体定义请见"错误类型"。
2. 将满足下面两种类型中任一类型错误的内容块输出到`wrong`字段中，如果有多种错误类型，只需要输出一次复选框内容块到`wrong`字段中，输出内容要求与原始OCR结果一致，** 禁止修改原始OCR结果中的复选框内容块 **。
3. 按照不同类型错误的修正方法，修正之后输出到`refined`字段，如果有多个错误类型，需要结合多个修正方法修正所有错误，输出到`refined`字段中。
4. 你需要按顺序找到所有的错误，并将不符合内容块的行序号输出到`line`字段中。

## 错误类型
### 类型1
结合原始图片和OCR识别结果，根据上下文语义，先判断是否是复选框错误内容，然后找出OCR识别结果中复选框标识解析错误的内容块，输出到`wrong`字段中，禁止修改原始OCR结果中的复选框内容块。
* 判断规则如下：
1. ["冈","凶","区","图","因","囧"]等其他为错误勾选的复选框标识符号，需要对比语义信息和图片内容找出。
2. ["口","D", "O"]等其他为错误非勾选的复选框标识符号，需要对比语义信息和图片内容找出。
3. 注意["□","☐","☑","☒", "√","(√)","x","X"]为正确的复选框标识符号，不需要找出，例如：`性别：☐男，☑女`，则不需要找出`☐男`或`☑女`。
* 修正规则：
将原始OCR结果中错误勾选的复选框标识符号替换为"☑"或"☒"，错误非勾选的复选框标识符号替换为"☐"，输出到`refined`字段中。

### 类型2
结合原始图片和OCR识别结果，图片中出现复选框内容为勾选，而OCR结果中没有勾选的部分，输出到`wrong`字段中，禁止修改原始OCR结果中的复选框内容块。
* 判断规则如下：
1. ["□", "☐", "[ ]", "( )"]为非勾选的复选框标识符号。
2. ["☑", "[✓]", "[✔]", "☒", "(√)", "■"] 为勾选的复选框标识符号。
* 修正规则：
将图片中出现复选框内容为勾选，而OCR结果中复选框内容不一致的部分修正为"☑"或"☒"。

## 输出格式
你需要输出一个json，其中包含以下字段，请不要输出任何解释性的内容：
```json
[{
    "line": int,
    "wrong": str,
    "refined": str,
]}"""


def build_messages(text, image_path):
    messages = [
        Message(role="user", content=[
            SftMultiModalImage(
                type=MultiModalType.image_url,
                image_url=SftMultiModalImageUrl(url=image_path)
            ),
            SftMultiModalText(type=MultiModalType.text, text=f"{prompt}\n## OCR 识别结果\n```plaintext\n{text}\n```"),
        ])]

    return messages


async def preprocess_llm_v1(text, image_url):
    messages = build_messages(text, image_url)
    try:
        selector = LLModelRpc.ModelSelector(
            model="checkbox_refined",
        )
        output_data = await LLModelRpc().async_multimodal(gateway=LLModelRpc.Gateway.Sft, messages=messages,
                                                          sft_base_model=LLModelRpc.SftBaseModel(
                                                              base_model=SftBaseModelType.qwen25_vl_3b_aidocs),
                                                          temperature=0.0,
                                                          selector=selector)
        logging.debug(f"llm output:{output_data}")
        if not output_data or len(output_data) < 2:
            logging.debug("llm output is empty")
            return []
        output = load_json_object(output_data[1])
        return output
    except Exception as e:
        # 可能会存在一些解析json的问题，直接返回文本。
        logging.error("Error processing LLM response, returning raw text.", e)
        return []
