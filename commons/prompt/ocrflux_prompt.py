import json
import logging
from typing import Optional

from commons.llm_gateway.llm import LLModelRpc
from commons.llm_gateway.models.chat_data import SftBaseModelType, Message, SftMultiModalImage, SftMultiModalText, \
    SftMultiModalImageUrl
from commons.prompt.checkbox_prompt import load_json_object

prompt = "下面是一个文档的一页图像。只需返回该文档的纯文本表示形式，就像你正在自然阅读它一样。 所有表格都应该以 HTML 格式呈现。 将所有标题和标题显示为 H1 标题。 不要产生幻觉。"
prompt_pic = "下面是一张图片。只需返回该图片里的文字信息，以纯文本表示形式，就像你正在自然阅读它一样。 所有表格都应该以 HTML 格式呈现。 将所有标题和标题显示为 H1 标题。 不要产生幻觉。"


async def preprocess_ocrflux(image_url: str,prompt_str: str,time_out: int=60) -> (str,bool):
    messages = [
        Message(role="user", content=[
            SftMultiModalImage(type="image_url", image_url=SftMultiModalImageUrl(url=image_url)),
            SftMultiModalText(type="text", text=prompt_str)
            # {"type": "image_url", "image_url": {"url": image_url}},
            # {"type": "text", "text": prompt}
        ]),
    ]
    try:

        output_data = await LLModelRpc().async_chat_text(gateway=LLModelRpc.Gateway.Sft, messages=messages,temperature=0.3,max_tokens=4096,repetition_penalty=1.2,
                                                         sft_base_model=LLModelRpc.SftBaseModel(
                                                             base_model=SftBaseModelType.ocrflux_qwen25_vl_3b),time_out = time_out)
        logging.debug(f"llm output:{output_data}")
        if not output_data or len(output_data) < 2:
            logging.debug("llm output is empty")
            return "",True
        output = json.loads(output_data[1])
        return output.get("natural_text", ""),True
    except Exception as e:
        # 可能会存在一些解析json的问题，直接返回文本。
        logging.error("Error processing LLM response, returning raw text.", e)
        return "",False
