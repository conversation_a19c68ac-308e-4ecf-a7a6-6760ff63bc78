from fastapi import Request

from commons.auth.auth_rpc import sig_wps2, sig_wps4
from commons.tools.utils import Singleton

class CamsCheckAuth(object, metaclass=Singleton):
    def __init__(self):
        self._aksk_dict = {}

    def init(self, aksk_dict: dict):
        self._aksk_dict = aksk_dict

    async def authorization(self, request: Request) -> bool:
        is_sigwps4 = True
        uri = request.url.path
        header = request.headers
        content_type = header.get("Content-Type", "")
        if "multipart/form-data" in content_type:
            # 去除boundary
            content_type = "multipart/form-data"
        authstr = header.get("Wps-Docs-Authorization", "")
        if len(authstr) == 0:
            is_sigwps4 = False

            authstr = header.get("Authorization", "")
            if len(authstr) == 0:
                return False

        if is_sigwps4:
            items = authstr.strip().split(" ")
            if len(items) != 2:
                return False
            items = items[1].strip().split(":")
            if len(items) != 2:
                return False
            check_ak = items[0]
            check_sk = self._aksk_dict.get(check_ak, "")
            if len(check_sk) == 0:
                return False

            date = header.get("Wps-Docs-Date", None)
            if date is None:
                return False
            body = await request.body() if "POST" == request.method and "application/json" == content_type else None
            h = sig_wps4(request.method, uri, body, check_ak, check_sk, date, content_type)
            return h["Wps-Docs-Authorization"] == authstr
        else:
            items = authstr.strip().split(":")
            if len(items) != 3:
                return False

            check_ak = items[1]
            check_sk = self._aksk_dict.get(check_ak, "")
            if len(check_sk) == 0:
                return False
            body = await request.body() if "POST" == request.method and "application/json" == content_type else None

            date = header.get("Date", None)
            if date is None:
                return False

            h = sig_wps2(uri, body, check_ak, check_sk, date, content_type)
            return h["Authorization"] == authstr
