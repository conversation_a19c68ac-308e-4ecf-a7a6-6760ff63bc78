
from typing import Optional, List, Tuple, Union
from tritonclient.http.aio import InferenceServerClient, InferInput, InferRequestedOutput
from tritonclient.http import InferenceServerClient as SInferenceServerClient, InferInput as SInferInput, InferRequestedOutput as SInferRequestedOutput
from commons.tools.utils import Singleton, error_trace
from commons.tools.utils import calc_time
from conf import ConfTriton
import numpy as np
import cv2

class Yolox(object, metaclass=Singleton):
    '''
        Yolox不支持多个batch同时推理
    '''
    def __init__(self):
        # self.client: Optional[InferenceServerClient] = None
        # self.sclient: Optional[SInferenceServerClient] = None
        self.model_name = "yolox"

    def init(self, model_name: Optional[str] = None):
        if model_name:
            self.model_name = model_name
        # self.client = InferenceServerClient(host)
        # self.sclient = SInferenceServerClient(host)

    async def infer(self, image: np.ndarray):
        inputs = [
            InferInput("images", list(image.shape), "FP32")
        ]
        inputs[0].set_data_from_numpy(image)
        output = "output0"
        outputs = [
            InferRequestedOutput(output)
        ]
        async with InferenceServerClient(url=ConfTriton.host) as triton_client:
            resp = await triton_client.infer(model_name=self.model_name, inputs=inputs, outputs=outputs)
            # resp = await self.client.infer(model_name=self.model_name, inputs=inputs, outputs=outputs)
            return resp.as_numpy(output)

    @calc_time
    def sinfer(self, image: np.ndarray):
        inputs = [
            SInferInput("images", list(image.shape), "FP32")
        ]
        inputs[0].set_data_from_numpy(image)
        output = "output0"
        outputs = [
            SInferRequestedOutput(output)
        ]
        triton_client = SInferenceServerClient(url=ConfTriton.host)
        resp = triton_client.infer(model_name=self.model_name, inputs=inputs, outputs=outputs)
        # resp = self.sclient.infer(model_name=self.model_name, inputs=inputs, outputs=outputs)
        return resp.as_numpy(output)


if __name__ == "__main__":
    import asyncio

    Yolox().init("insight-model-dev.kna.wps.cn")

    image_path = r"C:\Users\<USER>\Downloads\test2.jpg"
    image = cv2.imread(image_path)
    image = cv2.resize(image, (640, 640))  # YOLOX的输入尺寸通常是640x640
    image = image.astype(np.float32)
    image = image / 255.0  # 归一化
    image = np.transpose(image, (2, 0, 1))  # 将HWC转换为CHW
    image = np.expand_dims(image, axis=0)  # 添加batch维度


    loop = asyncio.get_event_loop()
    print(loop.run_until_complete(Yolox().infer(image)))
    print("------------------------------------------------------------------------")
    print(Yolox().sinfer(image))
