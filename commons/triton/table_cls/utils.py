import traceback
from io import Bytes<PERSON>
from pathlib import Path
from typing import Union, List

import cv2
import numpy as np
from PIL import Image, UnidentifiedImageError
from onnxruntime import InferenceSession
from onnxruntime.capi.onnxruntime_pybind11_state import (
    SessionOptions,
    GraphOptimizationLevel,
)

InputType = Union[str, np.ndarray, bytes, Path, Image.Image]
from commons.triton.yolox import Yolox

class OrtInferSession:
    def __init__(self):
        # Yolox().init("insight-model-dev.kna.wps.cn")
        pass
        # self.verify_exist(model_path)
        #
        # self.num_threads = num_threads
        # self._init_sess_opt()
        #
        # cpu_ep = "CPUExecutionProvider"
        # cpu_provider_options = {
        #     "arena_extend_strategy": "kSameAsRequested",
        # }
        # EP_list = [(cpu_ep, cpu_provider_options)]
        # try:
        #     self.session = InferenceSession(
        #         str(model_path), sess_options=self.sess_opt, providers=EP_list
        #     )
        # except TypeError:
        #     # 这里兼容ort 1.5.2
        #     self.session = InferenceSession(str(model_path), sess_options=self.sess_opt)


    # def _init_sess_opt(self):
    #     self.sess_opt = SessionOptions()
    #     self.sess_opt.log_severity_level = 4
    #     self.sess_opt.enable_cpu_mem_arena = False
    #
    #     if self.num_threads != -1:
    #         self.sess_opt.intra_op_num_threads = self.num_threads
    #
    #     self.sess_opt.graph_optimization_level = GraphOptimizationLevel.ORT_ENABLE_ALL

    def __call__(self, input_content: List[np.ndarray]) -> np.ndarray:
        # input_dict = dict(zip(self.get_input_names(), input_content))
        try:
            # return self.session.run(None, input_dict)
            return [Yolox().sinfer(input_content[0])]
        except Exception as e:
            error_info = traceback.format_exc()
            raise ONNXRuntimeError(error_info) from e

    # def get_input_names(
    #     self,
    # ):
    #     return [v.name for v in self.session.get_inputs()]
    #
    # def get_output_name(self, output_idx=0):
    #     return self.session.get_outputs()[output_idx].name
    #
    # def get_metadata(self):
    #     meta_dict = self.session.get_modelmeta().custom_metadata_map
    #     return meta_dict
    #
    # @staticmethod
    # def verify_exist(model_path: Union[Path, str]):
    #     if not isinstance(model_path, Path):
    #         model_path = Path(model_path)
    #
    #     if not model_path.exists():
    #         raise FileNotFoundError(f"{model_path} does not exist!")
    #
    #     if not model_path.is_file():
    #         raise FileExistsError(f"{model_path} must be a file")


class ONNXRuntimeError(Exception):
    pass


class LoadImageError(Exception):
    pass


class LoadImage:
    def __init__(self):
        pass

    def __call__(self, img: InputType) -> np.ndarray:
        if not isinstance(img, InputType.__args__):
            raise LoadImageError(
                f"The img type {type(img)} does not in {InputType.__args__}"
            )

        origin_img_type = type(img)
        img = self.load_img(img)
        img = self.convert_img(img, origin_img_type)
        return img

    def load_img(self, img: InputType) -> np.ndarray:
        if isinstance(img, (str, Path)):
            self.verify_exist(img)
            try:
                img = np.array(Image.open(img))
            except UnidentifiedImageError as e:
                raise LoadImageError(f"cannot identify image file {img}") from e
            return img

        if isinstance(img, bytes):
            img = np.array(Image.open(BytesIO(img)))
            return img

        if isinstance(img, np.ndarray):
            return img

        if isinstance(img, Image.Image):
            return np.array(img)

        raise LoadImageError(f"{type(img)} is not supported!")

    def convert_img(self, img: np.ndarray, origin_img_type):
        if img.ndim == 2:
            return cv2.cvtColor(img, cv2.COLOR_GRAY2BGR)

        if img.ndim == 3:
            channel = img.shape[2]
            if channel == 1:
                return cv2.cvtColor(img, cv2.COLOR_GRAY2BGR)

            if channel == 2:
                return self.cvt_two_to_three(img)

            if channel == 3:
                if issubclass(origin_img_type, (str, Path, bytes, Image.Image)):
                    return cv2.cvtColor(img, cv2.COLOR_RGB2BGR)
                return img

            if channel == 4:
                return self.cvt_four_to_three(img)

            raise LoadImageError(
                f"The channel({channel}) of the img is not in [1, 2, 3, 4]"
            )

        raise LoadImageError(f"The ndim({img.ndim}) of the img is not in [2, 3]")

    @staticmethod
    def cvt_two_to_three(img: np.ndarray) -> np.ndarray:
        """gray + alpha → BGR"""
        img_gray = img[..., 0]
        img_bgr = cv2.cvtColor(img_gray, cv2.COLOR_GRAY2BGR)

        img_alpha = img[..., 1]
        not_a = cv2.bitwise_not(img_alpha)
        not_a = cv2.cvtColor(not_a, cv2.COLOR_GRAY2BGR)

        new_img = cv2.bitwise_and(img_bgr, img_bgr, mask=img_alpha)
        new_img = cv2.add(new_img, not_a)
        return new_img

    @staticmethod
    def cvt_four_to_three(img: np.ndarray) -> np.ndarray:
        """RGBA → BGR"""
        r, g, b, a = cv2.split(img)
        new_img = cv2.merge((b, g, r))

        not_a = cv2.bitwise_not(a)
        not_a = cv2.cvtColor(not_a, cv2.COLOR_GRAY2BGR)

        new_img = cv2.bitwise_and(new_img, new_img, mask=a)
        new_img = cv2.add(new_img, not_a)
        return new_img

    @staticmethod
    def verify_exist(file_path: Union[str, Path]):
        if not Path(file_path).exists():
            raise LoadImageError(f"{file_path} does not exist.")


def resize_and_center_crop(image, output_size=640):
    """
    将图片的最小边缩放到指定大小，并进行中心裁剪。

    :param image: 输入的图片数组 (H, W, C)
    :param output_size: 缩放和裁剪后的图片大小，默认为 640
    :return: 处理后的图片数组 (output_size, output_size, C)
    """
    # 获取图片的高度和宽度
    height, width = image.shape[:2]
    # 计算缩放比例
    if width < height:
        new_width = output_size
        new_height = int(output_size * height / width)
    else:
        new_width = int(output_size * width / height)
        new_height = output_size

    # 缩放图片
    image_resize = cv2.resize(
        image, (new_width, new_height), interpolation=cv2.INTER_LINEAR
    )

    # 计算中心裁剪的坐标
    left = (new_width - output_size) // 2
    top = (new_height - output_size) // 2
    right = left + output_size
    bottom = top + output_size

    # # 中心裁剪
    image_cropped = image_resize[top:bottom, left:right]
    return image_cropped
