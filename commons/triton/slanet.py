
from typing import Optional, List, Tuple, Union
from tritonclient.http.aio import InferenceServerClient, InferInput, InferRequestedOutput
from tritonclient.http import InferenceServerClient as SInferenceServerClient, InferInput as SInferInput, InferRequestedOutput as SInferRequestedOutput
from commons.tools.utils import Singleton, error_trace
from commons.tools.utils import calc_time
from conf import ConfTriton
import numpy as np
import cv2

class Slanet(object, metaclass=Singleton):
    '''
        Slanet支持多个batch同时推理，但是输入的batch必须是相同的尺寸
    '''
    def __init__(self):
        # self.client: Optional[InferenceServerClient] = None
        # self.sclient: Optional[SInferenceServerClient] = None
        self.model_name = "slanet"

    def init(self, model_name: Optional[str] = None):
        if model_name:
            self.model_name = model_name
        # self.client = InferenceServerClient(host)
        # self.sclient = SInferenceServerClient(host)

    async def infer(self, input: np.ndarray):
        inputs = [
            InferInput("x", list(input.shape), "FP32")
        ]
        inputs[0].set_data_from_numpy(input)
        output_0 = "save_infer_model/scale_0.tmp_0"
        output_1 = "save_infer_model/scale_1.tmp_0"
        outputs = [
            InferRequestedOutput(output_0),
            InferRequestedOutput(output_1)
        ]
        async with InferenceServerClient(url=ConfTriton.host) as triton_client:
            resp = await triton_client.infer(model_name=self.model_name, inputs=inputs, outputs=outputs)
            # resp = await self.client.infer(model_name=self.model_name, inputs=inputs, outputs=outputs)
            return resp.as_numpy(output_0), resp.as_numpy(output_1)

    @calc_time
    def sinfer(self, input: np.ndarray):
        inputs = [
            SInferInput("x", list(input.shape), "FP32")
        ]
        inputs[0].set_data_from_numpy(input)
        output_0 = "save_infer_model/scale_0.tmp_0"
        output_1 = "save_infer_model/scale_1.tmp_0"
        outputs = [
            SInferRequestedOutput(output_0),
            SInferRequestedOutput(output_1)
        ]
        triton_client = SInferenceServerClient(url=ConfTriton.host)
        resp = triton_client.infer(model_name=self.model_name, inputs=inputs, outputs=outputs)
        # resp = self.sclient.infer(model_name=self.model_name, inputs=inputs, outputs=outputs)
        return resp.as_numpy(output_0), resp.as_numpy(output_1)


if __name__ == "__main__":
    import asyncio

    Slanet().init("insight-model-dev.kna.wps.cn")

    # 读取并预处理两张图片
    image_paths = [r"C:\Users\<USER>\Downloads\test1.jpg", r"C:\Users\<USER>\Downloads\test2.jpg"]  # 两张图片的路径
    images = []

    for image_path in image_paths:
        image = cv2.imread(image_path)
        image = cv2.resize(image, (1266, 1783))  # 输入的图片尺寸需要一致
        image = image.astype(np.float32)  # 转换为 float32
        image = image / 255.0  # 归一化到 [0, 1]
        image = np.transpose(image, (2, 0, 1))  # 将 HWC 转换为 CHW
        images.append(image)
    # 将两张图片堆叠成一个 batch
    batch_images = np.stack(images, axis=0)  # 形状为 [2, 3, H, W]

    loop = asyncio.get_event_loop()
    # print(loop.run_until_complete(Slanet().infer(batch_images)))
    # print("------------------------------------------------------------------------")
    # print(Slanet().sinfer(batch_images))

    infer_output_0, infer_output_1 = loop.run_until_complete(Slanet().infer(batch_images))
    sinfer_output_0, sinfer_output_1 = Slanet().sinfer(batch_images)

    # 打印 infer 输出的维度大小
    print("Infer Output 0 Shape:", infer_output_0.shape)
    print("Infer Output 1 Shape:", infer_output_1.shape)

    # 打印 sinfer 输出的维度大小
    print("Sinfer Output 0 Shape:", sinfer_output_0.shape)
    print("Sinfer Output 1 Shape:", sinfer_output_1.shape)