#
# import numpy as np
# from typing import Optional, <PERSON>, Tuple, Union
# from transformers import AutoTokenizer
# from tritonclient.http.aio import InferenceServerClient, InferInput, InferRequestedOutput
# from tritonclient.http import InferenceServerClient as SInferenceServerClient, InferInput as SInferInput, InferRequestedOutput as SInferRequestedOutput
#
# from commons.logger.business_log import logger
# from commons.thread.multiprocess import MultiProcess
# from conf import ConfTriton
# from commons.tools.utils import Singleton, error_trace
#
# class BgeM3(object, metaclass=Singleton):
#     def __init__(self):
#         # self.client: Optional[InferenceServerClient] = None
#         self.model_name = "bge-m3"
#         self.tokenizer = None
#         self.batch_size: int = 4
#
#     def init(self, model_path: str, model_name: Optional[str] = None, batch_size: Optional[int] = None):
#         if model_name:
#             self.model_name = model_name
#         if batch_size:
#             self.batch_size = batch_size
#         # self.client = InferenceServerClient(host)
#         # self.sclient = SInferenceServerClient(host)
#         self.tokenizer = AutoTokenizer.from_pretrained(model_path)
#
#     async def _infer(self, input_ids: np.ndarray, attention_mask: np.ndarray):
#         assert input_ids.shape[0] <= self.batch_size, \
#             f"bge-m3 infer input request batch-size must be <= {self.batch_size}"
#         inputs = [
#             InferInput("input_ids", list(input_ids.shape), "INT64"),
#             InferInput("attention_mask", list(attention_mask.shape), "INT64")
#         ]
#         inputs[0].set_data_from_numpy(input_ids)
#         inputs[1].set_data_from_numpy(attention_mask)
#         output_dense_vecs = "dense_vecs"
#         output_colbert_vecs = "colbert_vecs"
#         outputs = [
#             InferRequestedOutput(output_dense_vecs),
#             InferRequestedOutput(output_colbert_vecs)
#         ]
#         async with InferenceServerClient(url=ConfTriton.host) as triton_client:
#             resp = await triton_client.infer(model_name=self.model_name, inputs=inputs, outputs=outputs)
#             # resp = await self.client.infer(model_name=self.model_name, inputs=inputs, outputs=outputs)
#             return resp.as_numpy(output_dense_vecs), resp.as_numpy(output_colbert_vecs)
#
#     def _sinfer(self, input_ids: np.ndarray, attention_mask: np.ndarray):
#         assert input_ids.shape[0] <= self.batch_size, \
#             f"bge-m3 infer input request batch-size must be <= {self.batch_size}"
#         inputs = [
#             SInferInput("input_ids", list(input_ids.shape), "INT64"),
#             SInferInput("attention_mask", list(attention_mask.shape), "INT64")
#         ]
#         inputs[0].set_data_from_numpy(input_ids)
#         inputs[1].set_data_from_numpy(attention_mask)
#         output_dense_vecs = "dense_vecs"
#         output_colbert_vecs = "colbert_vecs"
#         outputs = [
#             SInferRequestedOutput(output_dense_vecs),
#             SInferRequestedOutput(output_colbert_vecs)
#         ]
#         triton_client = SInferenceServerClient(url=ConfTriton.host)
#         resp = triton_client.infer(model_name=self.model_name, inputs=inputs, outputs=outputs)
#         # resp = self.sclient.infer(model_name=self.model_name, inputs=inputs, outputs=outputs)
#         return resp.as_numpy(output_dense_vecs), resp.as_numpy(output_colbert_vecs)
#
#     async def infer(self, text: str, max_length: int = 512, return_colbert=False) -> Union[List[List], Tuple[List[List], List[List], List[List]], None]:
#         res = await self.infer_batch([text], max_length, return_colbert)
#         if res is None:
#             return None
#         if not return_colbert:
#             return res[0]
#         return res[0][0], res[1][0], res[2][0]
#
#     def sinfer(self, text: str, max_length: int = 512, return_colbert=False) -> Union[List[List], Tuple[List[List], List[List], List[List]], None]:
#         res = self.sinfer_batch([text], max_length, return_colbert)
#         if res is None:
#             return None
#         if not return_colbert:
#             return res[0]
#         return res[0][0], res[1][0], res[2][0]
#
#     async def infer_batch(self, texts: List[str], max_length: int = 512, return_colbert=False, multi_process=True) -> Union[List[List], Tuple[List[List], List[List], List[List]], None]:
#         try:
#             # senlength, offset_mapping, encoded_input = await MultiProcess().arun(self._infer_batch_preprocess, texts, max_length)
#             senlength, offset_mapping, encoded_input = self._infer_batch_preprocess(texts, max_length)
#
#             dense_vecs = []
#             colbert_vecs_base = []
#             for i in range(0, len(encoded_input["input_ids"]), self.batch_size):
#                 input_ids = encoded_input["input_ids"][i:i+self.batch_size].astype(np.int64)
#                 attention_mask = encoded_input["attention_mask"][i:i+self.batch_size].astype(np.int64)
#                 result = await self._infer(input_ids, attention_mask)
#                 dense_vecs.extend(result[0].tolist())
#                 colbert_vecs_base.extend(result[1].tolist())
#             return self._infer_batch_postprocess(texts, dense_vecs, senlength, offset_mapping, colbert_vecs_base, return_colbert)
#             # return await MultiProcess().arun(self._infer_batch_postprocess, texts, dense_vecs, senlength, offset_mapping, colbert_vecs_base, return_colbert)
#
#         except Exception as e:
#             logger.error(e)
#             error_trace()
#             raise e
#
#     def _infer_batch_preprocess(self, texts: List[str], max_length: int = 512):
#         encoded_input = self.tokenizer(
#             texts,
#             max_length=max_length,
#             padding=True,
#             truncation=True,
#             return_tensors="np",
#             return_offsets_mapping=True)
#
#         senlength = np.sum(encoded_input["attention_mask"][:, 1:], axis=-1).tolist()
#         offset_mapping = encoded_input["offset_mapping"][:, 1:].tolist()
#         _ = encoded_input.pop("offset_mapping")
#         return senlength, offset_mapping, encoded_input
#
#     def _infer_batch_postprocess(self, texts: List[str], dense_vecs, senlength, offset_mapping, colbert_vecs_base, return_colbert=False):
#         if not return_colbert:
#             for index, text in enumerate(texts):
#                 if len(text) == 0:
#                     dense_vecs[index] = []
#             return dense_vecs
#
#         colbert_vecs = []
#         offset_mapping_new = []
#         for length, offset, colbert_vec in zip(senlength, offset_mapping, colbert_vecs_base):
#             colbert_vecs.append(colbert_vec[:length])
#             offset_mapping_new.append(offset[:length])
#         offset_mapping = offset_mapping_new
#         return dense_vecs, colbert_vecs, offset_mapping
#
#     def sinfer_batch(self, texts: List[str], max_length: int = 512, return_colbert=False) -> Union[List[List], Tuple[List[List], List[List], List[List]], None]:
#         try:
#             encoded_input = self.tokenizer(
#                 texts,
#                 max_length=max_length,
#                 padding=True,
#                 truncation=True,
#                 return_tensors="np",
#                 return_offsets_mapping=True)
#
#             senlength = np.sum(encoded_input["attention_mask"][:, 1:], axis=-1).tolist()
#             offset_mapping = encoded_input["offset_mapping"][:, 1:].tolist()
#             _ = encoded_input.pop("offset_mapping")
#
#             dense_vecs = []
#             colbert_vecs_base = []
#             for i in range(0, len(encoded_input["input_ids"]), self.batch_size):
#                 input_ids = encoded_input["input_ids"][i:i+self.batch_size].astype(np.int64)
#                 attention_mask = encoded_input["attention_mask"][i:i+self.batch_size].astype(np.int64)
#                 result = self._sinfer(input_ids, attention_mask)
#                 dense_vecs.extend(result[0].tolist())
#                 colbert_vecs_base.extend(result[1].tolist())
#             if not return_colbert:
#                 for index, text in enumerate(texts):
#                     if len(text) == 0:
#                         dense_vecs[index] = []
#                 return dense_vecs
#
#             colbert_vecs = []
#             offset_mapping_new = []
#             for length, offset, colbert_vec in zip(senlength, offset_mapping, colbert_vecs_base):
#                 colbert_vecs.append(colbert_vec[:length])
#                 offset_mapping_new.append(offset[:length])
#             offset_mapping = offset_mapping_new
#             return dense_vecs, colbert_vecs, offset_mapping
#
#         except Exception as e:
#             logger.error(e)
#             error_trace()
#             return None
#
#
# if __name__ == "__main__":
#     import asyncio
#
#     BgeM3().init(r"D:\project\insight-ai-server\tests\bge_20240509t_ckpt")
#
#     loop = asyncio.get_event_loop()
#     print(loop.run_until_complete(BgeM3().infer("帮我总结过去7天的邮件")))
#     print(BgeM3().sinfer("帮我总结过去7天的邮件"))
#     print(BgeM3().sinfer(""))
