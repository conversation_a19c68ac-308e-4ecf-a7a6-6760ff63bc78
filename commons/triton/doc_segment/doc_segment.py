import logging
from typing import Optional, List, Union, Dict, Any
from transformers import AutoConfig, AutoModelForTokenClassification
from transformers.configuration_utils import PretrainedConfig
from tritonclient.http.aio import InferenceServerClient, InferInput, InferRequestedOutput, InferAsyncRequest
import numpy as np
import re
from datasets import Dataset
import json
import os

from conf import ConfTriton
from commons.tools.utils import Singleton, error_trace
from commons.triton.doc_segment.preprocessor import TransformersPreprocessor
from commons.tools.utils import async_calc_time

class DocSegment(object, metaclass=Singleton):
    def __init__(self):
        # self.client: Optional[InferenceServerClient] = None
        self.model_name = "doc_segment"
        self.batch_size = 4
        self.model_cfg: Optional[dict] = None
        self.config: Optional[PretrainedConfig] = None
        self.preprocessor: Optional[TransformersPreprocessor] = None

    def init(self, model_path: str, model_name: Optional[str] = None, batch_size: int = 4):
        if model_name:
            self.model_name = model_name
        # self.client = InferenceServerClient(host)
        self.config = AutoConfig.from_pretrained(model_path)
        self.config.update({'num_labels': self.config.type_vocab_size})
        self.model_cfg = json.load(open(os.path.join(model_path, 'configuration.json'), 'r'))['model']['model_config']
        self.preprocessor = TransformersPreprocessor(model_path, self.config.max_position_embeddings)
        self.batch_size = batch_size

    async def _infer(self, input_ids: np.ndarray, token_type_ids: np.ndarray, attention_mask: np.ndarray):
        assert input_ids.shape[0] <= self.batch_size, \
            f"doc_segment infer input request batch-size must be <= {self.batch_size}"
        # 输入结构
        inputs = [
            InferInput("input_ids", list(input_ids.shape), "INT64"),
            InferInput("token_type_ids", list(token_type_ids.shape), "INT64"),
            InferInput("attention_mask", list(attention_mask.shape), "INT64")
        ]
        # 导入数据
        inputs[0].set_data_from_numpy(input_ids)
        inputs[1].set_data_from_numpy(token_type_ids)
        inputs[2].set_data_from_numpy(attention_mask)
        # 输出结构
        output_logits = "logits"
        outputs = [
            InferRequestedOutput(output_logits),
        ]
        async with InferenceServerClient(url=ConfTriton.host) as triton_client:
            resp = await triton_client.infer(model_name=self.model_name, inputs=inputs, outputs=outputs)
            # resp = await self.client.infer(model_name=self.model_name, inputs=inputs, outputs=outputs)
            return resp.as_numpy(output_logits)

    def cut_sentence(self, para):
        para = re.sub(r'([。！!？\?])([^”’])', r'\1\n\2', para)  # 单字符断句符
        para = re.sub(r'(\.{6})([^”’])', r'\1\n\2', para)  # 英文省略号
        para = re.sub(r'(\…{2})([^”’])', r'\1\n\2', para)  # 中文省略号
        # 如果双引号前有终止符，那么双引号才是句子的终点，把分句符\n放到双引号后，注意前面的几句都小心保留了双引号
        para = re.sub(r'([。！？\?][”’])([^，。！？\?])', r'\1\n\2', para)
        para = para.rstrip()  # 段尾如果有多余的\n就去掉它
        return [_ + '\n' for _ in para.split('\n') if _]

    def postprocess(self, inputs: List[Dict[str, Any]]) -> Union[List[str], str]:
        """process the prediction results

        Args:
            inputs (Dict[str, Any]): _description_

        Returns:
            Dict[str, str]: the prediction results
        """
        result = []
        res_preds = []
        list_count = len(inputs)

        if self.model_cfg['level'] == 'topic':
            for num in range(list_count):
                res = []
                pred = []
                for s, p, l in zip(inputs[num]['paragraphs'],
                                   inputs[num]['predictions'],
                                   inputs[num]['labels']):
                    s = s.strip()
                    if p == 'B-EOP':
                        s = ''.join([s, '\n\n\t'])
                        pred.append(1)
                    else:
                        s = ''.join([s, '\n\t'])
                        pred.append(0)
                    res.append(s)
                res_preds.append(pred)
                document = ('\t' + ''.join(res).strip())
                result.append(document)
        else:
            for num in range(list_count):
                res = []
                for s, p in zip(inputs[num]['sentences'],
                                inputs[num]['predictions']):
                    s = s.strip()
                    if p == 'B-EOP':
                        s = ''.join([s, '\n\t'])
                    res.append(s)

                document = ('\t' + ''.join(res))
                result.append(document)

        if list_count == 1:
            return result[0]
        else:
            return result

    def cut_documents(self, para: Union[List[List[str]], List[str], str]):
        document_list = para
        paragraphs = []
        sentences = []
        labels = []
        example_id = []
        id = 0

        if self.model_cfg['level'] == 'topic':
            if isinstance(para, str):
                document_list = [[para]]
            elif isinstance(para[0], str):
                document_list = [para]

            for document in document_list:
                sentence = []
                label = []
                for item in document:
                    sentence_of_current_paragraph = item
                    sentence.extend(sentence_of_current_paragraph)
                    label.extend(['-100']
                                 * (len(sentence_of_current_paragraph) - 1)
                                 + ['B-EOP'])
                paragraphs.append(document)
                sentences.append(sentence)
                labels.append(label)
                example_id.append(id)
                id += 1

            return {
                'example_id': example_id,
                'sentences': sentences,
                'paragraphs': paragraphs,
                'labels': labels
            }
        else:
            if isinstance(para, str):
                para = self.cut_sentence(para)

            if isinstance(para, list):
                document_list = [para]

            for document in document_list:
                sentence = document
                label = ['O'] * (len(sentence) - 1) + ['B-EOP']
                sentences.append(sentence)
                labels.append(label)
                example_id.append(id)
                id += 1

            return {
                'example_id': example_id,
                'sentences': sentences,
                'labels': labels
            }

    async def infer(self, documents: Union[List[List[str]], List[str], str]) -> Union[List[str], None]:
        try:
            pred_samples = self.cut_documents(documents)

            if self.model_cfg['level'] == 'topic':
                paragraphs = pred_samples.pop('paragraphs')

            predict_examples = Dataset.from_dict(pred_samples)

            # Predict Feature Creation
            predict_dataset = self.preprocessor(predict_examples, self.model_cfg)
            num_examples = len(
                predict_examples[self.preprocessor.context_column_name])
            num_samples = len(
                predict_dataset[self.preprocessor.context_column_name])

            if self.model_cfg['type'] == 'bert':
                predict_dataset.pop('segment_ids')

            labels = predict_dataset.pop('labels')
            sentences = predict_dataset.pop('sentences')
            example_ids = predict_dataset.pop(self.preprocessor.example_id_column_name)
            input_ids = np.array(predict_dataset["input_ids"], dtype=np.int64)
            token_type_ids = np.array(predict_dataset["token_type_ids"], dtype=np.int64)
            attention_mask = np.array(predict_dataset["attention_mask"], dtype=np.int64)
            # 调用triton
            # # 分批次执行，每批次4
            predictions = []
            for i in range(0, len(input_ids), self.batch_size):
                input_ids_batch = input_ids[i:i + self.batch_size]
                token_type_ids_batch = token_type_ids[i:i + self.batch_size]
                attention_mask_batch = attention_mask[i:i + self.batch_size]
                logit = await self._infer(input_ids_batch, token_type_ids_batch, attention_mask_batch)
                batch_predictions = logit.tolist()
                predictions.extend(batch_predictions)

            predictions = np.argmax(predictions, axis=2)

            assert len(sentences) == len(
                predictions), 'sample {}  infer_sample {} prediction {}'.format(
                num_samples, len(sentences), len(predictions))
            # Remove ignored index (special tokens)

            true_predictions = [
                [
                    self.preprocessor.label_list[p]
                    for (p, l) in zip(prediction, label) if l != -100  # noqa *
                ] for prediction, label in zip(predictions, labels)
            ]

            true_labels = [
                [
                    self.preprocessor.label_list[l]
                    for (p, l) in zip(prediction, label) if l != -100  # noqa *
                ] for prediction, label in zip(predictions, labels)
            ]
            out = []
            for i in range(num_examples):
                if self.model_cfg['level'] == 'topic':
                    out.append({
                        'sentences': [],
                        'labels': [],
                        'predictions': [],
                        'paragraphs': paragraphs[i]
                    })
                else:
                    out.append({'sentences': [], 'labels': [], 'predictions': []})

            for prediction, sentence_list, label, example_id in zip(
                    true_predictions, sentences, true_labels, example_ids):
                if self.model_cfg['level'] == 'doc':
                    if len(label) < len(sentence_list):
                        label.append('B-EOP')
                        prediction.append('B-EOP')
                    assert len(sentence_list) == len(prediction), '{} {}'.format(
                        len(sentence_list), len(prediction))
                    assert len(sentence_list) == len(label), '{} {}'.format(
                        len(sentence_list), len(label))

                out[example_id]['sentences'].extend(sentence_list)
                out[example_id]['labels'].extend(label)
                out[example_id]['predictions'].extend(prediction)

            if self.model_cfg['level'] == 'topic':
                for i in range(num_examples):
                    assert len(out[i]['predictions']) + 1 == len(
                        out[i]['paragraphs'])
                    out[i]['predictions'].append('B-EOP')
                    out[i]['labels'].append('B-EOP')
            out = self.postprocess(out)
            splits = [i.strip('\t') for i in out.split("\n\t") if i]
            return splits
        except Exception as e:
            logging.error(e)
            error_trace()
            return None


if __name__ == "__main__":
    import asyncio

    DocSegment().init("insight-model-dev.kna.wps.cn", r"D:\project\insight-ai-server\tests\doc_segment_ckpt")
    segs = ['五、相关联系人及邮箱: 5.1相关联系人: 北京行政:王梦伊(<EMAIL>) 珠海行政:付杨爽(<EMAIL>) 武汉行政:吴媛(<EMAIL>) 广州行政:张泽钿(<EMAIL>) 除京珠广汉以外地区行政:王梦伊(<EMAIL>) 薪酬组:刘文文(<EMAIL>) HRBP:发送给各业务对应HRBP 5.2各地行政组及薪酬组邮件组: 北京行政:<EMAIL> 珠海行政:<EMAIL> 武汉行政:<EMAIL> 广州行政:<EMAIL> 除京珠广汉以外地区行政:<EMAIL> 薪酬组:<EMAIL> 六、其它:',
            '1. 公司总裁室成员的住宿根据实际情况而定,不受以上限制。',
            '2. 对以上所有行为秉承诚信正直的原则,若发现瞒报虚报迟报等行为,将影响后续申请资格。']
    loop = asyncio.get_event_loop()
    res = loop.run_until_complete(DocSegment().infer(segs))
    print(res)
    print(len(res))
