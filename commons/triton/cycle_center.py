
from typing import Optional, List, Tuple, Union
from tritonclient.http.aio import InferenceServerClient, InferInput, InferRequestedOutput
from tritonclient.http import InferenceServerClient as SInferenceServerClient, InferInput as SInferInput, InferRequestedOutput as SInferRequestedOutput
from commons.tools.utils import Singleton, error_trace
from commons.tools.utils import calc_time
from conf import ConfTriton
import numpy as np
import cv2

class CycleCenter(object, metaclass=Singleton):
    '''
        CycleCenter支持多个batch同时推理，输入的batch必须是相同的尺寸。
        triton的config.pbtxt文件里设置的max_batch_size为 0，输出的第二个维度是batch，第一个维度固定为 1
    '''
    def __init__(self):
        # self.client: Optional[InferenceServerClient] = None
        # self.sclient: Optional[SInferenceServerClient] = None
        self.model_name = "cycle-center"

    def init(self, model_name: Optional[str] = None):
        if model_name:
            self.model_name = model_name
        # self.client = InferenceServerClient(host)
        # self.sclient = SInferenceServerClient(host)

    async def infer(self, input: np.ndarray):
        inputs = [
            InferInput("input", list(input.shape), "FP32")
        ]
        inputs[0].set_data_from_numpy(input)
        output = "output"
        outputs = [
            InferRequestedOutput(output)
        ]
        async with InferenceServerClient(url=ConfTriton.host) as triton_client:
            resp = await triton_client.infer(model_name=self.model_name, inputs=inputs, outputs=outputs)
            # resp = await self.client.infer(model_name=self.model_name, inputs=inputs, outputs=outputs)
            return resp.as_numpy(output)

    @calc_time
    def sinfer(self, input: np.ndarray):
        inputs = [
            SInferInput("input", list(input.shape), "FP32")
        ]
        inputs[0].set_data_from_numpy(input)
        output = "output"
        outputs = [
            SInferRequestedOutput(output)
        ]
        triton_client = SInferenceServerClient(url=ConfTriton.host)
        resp = triton_client.infer(model_name=self.model_name, inputs=inputs, outputs=outputs)
        # resp = self.sclient.infer(model_name=self.model_name, inputs=inputs, outputs=outputs)
        return resp.as_numpy(output)


if __name__ == "__main__":
    import asyncio

    CycleCenter().init("insight-model-dev.kna.wps.cn")

    # 读取并预处理两张图片
    image_paths = [r"C:\Users\<USER>\Downloads\test1.jpg", r"C:\Users\<USER>\Downloads\test2.jpg"]  # 两张图片的路径
    images = []

    for image_path in image_paths:
        image = cv2.imread(image_path)
        image = cv2.resize(image, (1266, 1783))  # 输入的图片尺寸需要一致
        image = image.astype(np.float32)  # 转换为 float32
        image = image / 255.0  # 归一化到 [0, 1]
        image = np.transpose(image, (2, 0, 1))  # 将 HWC 转换为 CHW
        images.append(image)
    # 将两张图片堆叠成一个 batch
    batch_images = np.stack(images, axis=0)  # 形状为 [2, 3, H, W]


    loop = asyncio.get_event_loop()
    # print(loop.run_until_complete(CycleCenter().infer(batch_images)))
    # print("------------------------------------------------------------------------")
    # print(CycleCenter().sinfer(batch_images))
    infer_output = loop.run_until_complete(CycleCenter().infer(batch_images))
    sinfer_output = CycleCenter().sinfer(batch_images)
    # 打印 infer 输出的维度大小
    print("Infer Output Shape:", infer_output.shape)
    # 打印 sinfer 输出的维度大小
    print("Sinfer Output  Shape:", sinfer_output.shape)

