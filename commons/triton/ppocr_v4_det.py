
from typing import Optional, <PERSON>, Tuple, Union
from tritonclient.http.aio import InferenceServerClient, InferInput, InferRequestedOutput
from tritonclient.http import InferenceServerClient as SInferenceServerClient, InferInput as SInferInput, InferRequestedOutput as SInferRequestedOutput
from commons.tools.utils import Singleton, error_trace
from commons.tools.utils import calc_time
from conf import ConfTriton
import numpy as np
import cv2

class PPOCRV4Det(object, metaclass=Singleton):
    '''
        PPOCRV4Det支持多个batch同时推理，输入的batch必须是相同的尺寸
    '''
    def __init__(self):
        # self.client: Optional[InferenceServerClient] = None
        # self.sclient: Optional[SInferenceServerClient] = None
        self.model_name = "ppocr-v4-det"
        self.batch_size: int = 4

    def init(self, model_name: Optional[str] = None, batch_size: Optional[int] = None):
        if model_name:
            self.model_name = model_name
        if batch_size:
            self.batch_size = batch_size
        # self.client = InferenceServerClient(host)
        # self.sclient = SInferenceServerClient(host)

    async def _infer(self, input: np.ndarray):
        inputs = [
            InferInput("x", list(input.shape), "FP32")
        ]
        inputs[0].set_data_from_numpy(input)
        output = "sigmoid_0.tmp_0"
        outputs = [
            InferRequestedOutput(output)
        ]
        async with InferenceServerClient(url=ConfTriton.host) as triton_client:
            resp = await triton_client.infer(model_name=self.model_name, inputs=inputs, outputs=outputs)
            # resp = await self.client.infer(model_name=self.model_name, inputs=inputs, outputs=outputs)
            return resp.as_numpy(output)

    def _sinfer(self, input: np.ndarray):
        inputs = [
            SInferInput("x", list(input.shape), "FP32")
        ]
        inputs[0].set_data_from_numpy(input)
        output = "sigmoid_0.tmp_0"
        outputs = [
            SInferRequestedOutput(output)
        ]
        triton_client = SInferenceServerClient(url=ConfTriton.host)
        resp = triton_client.infer(model_name=self.model_name, inputs=inputs, outputs=outputs)
        # resp = self.sclient.infer(model_name=self.model_name, inputs=inputs, outputs=outputs)
        return resp.as_numpy(output)

    async def infer_batch(self, input: np.ndarray):
        for i in range(0, len(input), self.batch_size):
            batch_input = input[i:i+self.batch_size]
            result = await self._infer(batch_input)
            if i == 0:
                results = result
            else:
                results = np.concatenate((results, result), axis=0)
        return results

    @calc_time
    def sinfer_batch(self, input: np.ndarray):
        for i in range(0, len(input), self.batch_size):
            batch_input = input[i:i+self.batch_size]
            result = self._sinfer(batch_input)
            if i == 0:
                results = result
            else:
                results = np.concatenate((results, result), axis=0)
        return results


if __name__ == "__main__":
    import asyncio

    PPOCRV4Det().init("insight-model-dev.kna.wps.cn")

    # 读取并预处理两张图片
    image_paths = [r"C:\Users\<USER>\Downloads\test1.jpg", r"C:\Users\<USER>\Downloads\test2.jpg",
                   r"C:\Users\<USER>\Downloads\test1.jpg", r"C:\Users\<USER>\Downloads\test2.jpg",
                   r"C:\Users\<USER>\Downloads\test1.jpg", r"C:\Users\<USER>\Downloads\test2.jpg",
                   r"C:\Users\<USER>\Downloads\test2.jpg"]
    images = []

    for image_path in image_paths:
        image = cv2.imread(image_path)
        image = cv2.resize(image, (320, 320))  # 输入的图片尺寸需要一致
        image = image.astype(np.float32)  # 转换为 float32
        image = image / 255.0  # 归一化到 [0, 1]
        image = np.transpose(image, (2, 0, 1))  # 将 HWC 转换为 CHW
        images.append(image)
    # 将两张图片堆叠成一个 batch
    batch_images = np.stack(images, axis=0)  # 形状为 [N, 3, H, W]

    loop = asyncio.get_event_loop()

    infer_output = loop.run_until_complete(PPOCRV4Det().infer_batch(batch_images))
    sinfer_output = PPOCRV4Det().sinfer_batch(batch_images)
    # 打印 infer 输出的维度大小
    print("Infer Output Shape:", infer_output.shape)
    # 打印 sinfer 输出的维度大小
    print("Sinfer Output  Shape:", sinfer_output.shape)
    print("Sinfer Output:", sinfer_output)
