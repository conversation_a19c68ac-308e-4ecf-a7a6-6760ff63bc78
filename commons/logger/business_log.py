import logging

from commons.logger.logger import common_format, BaseFormatter, LoggerFilter
from conf import ConfLogger


class BusinessLogger:
    def __init__(self, name: str):
        """
        初始化 BusinessLogger
        :param name: 日志名称
        :param level: 日志级别
        """

        self.logger = logging.getLogger(name)
        self.logger.setLevel(ConfLogger.log_level)
        self._business_fields = {}

        # # 设置日志格式
        # formatter = logging.Formatter(
        #     '[%(asctime)s] [%(levelname)s] [%(name)s] %(message)s'
        # )

        # 控制台输出
        if self.logger.hasHandlers():
            self.logger.handlers.clear()
        # if not self.logger.hasHandlers():
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(BaseFormatter())
        console_handler.addFilter(LoggerFilter())
        self.logger.addHandler(console_handler)

    def add_business_field(self, key: str, value: str):
        """
        添加业务字段
        :param key: 字段名
        :param value: 字段值
        """
        self._business_fields[key] = value

    def _get_extra(self):
        """
        获取包含业务字段的 extra
        :return: 包含业务字段的字典
        """
        return {"business_fields": self._business_fields}

    def info(self, message: str):
        self.logger.info(message, extra=self._get_extra())

    def debug(self, message: str):
        self.logger.debug(message, extra=self._get_extra())

    def warning(self, message: str):
        self.logger.warning(message, extra=self._get_extra())

    def error(self, message):
        self.logger.error(message, extra=self._get_extra())

    def critical(self, message: str):
        self.logger.critical(message, extra=self._get_extra())

    def exception(self, message):
        """
        记录异常日志
        :param message: 异常信息
        """
        self.logger.exception(message, extra=self._get_extra())


logger = BusinessLogger("")
