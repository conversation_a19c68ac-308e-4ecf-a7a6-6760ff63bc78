# coding: utf-8
# <AUTHOR> wangziyang1
# @date    : 2025/3/29 14:03

import unittest

from commons.thread.multicoroutine import MultiCoroutine
import asyncio


async def example_task(delay, result, should_fail=False, return_none=False):
    await asyncio.sleep(delay)
    print(f"Task {result} run")
    if should_fail:
        raise ValueError(f"Task {result} failed")
    if return_none:
        return
    return result


class TestMultiCoroutine(unittest.IsolatedAsyncioTestCase):
    async def test_run(self):
        pool = MultiCoroutine()
        # 添加任务
        pool.add_task('task1', example_task(1, 'success1'))
        pool.add_task('task2', example_task(1, 'success2', should_fail=True))
        pool.add_task('task3', example_task(1, 'success3'))
        pool.add_task('task4', example_task(1, 'success4', return_none=True))

        # 执行并获取结果
        results = await pool.run()
        print("执行结果:", results)

        # 处理异常结果
        for key, res in results.items():
            if isinstance(res, Exception):
                print(f"{key} 出错: {res}")
            else:
                print(f"{key} 成功: {res}")