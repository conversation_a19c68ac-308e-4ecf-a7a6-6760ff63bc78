# coding: utf-8
# <AUTHOR> wang<PERSON>yang1
# @date    : 2025/3/29 14:02

import asyncio
import logging
from typing import List, Any, Dict, Optional
from commons.tools.utils import async_calc_time


class MultiCoroutine:
    def __init__(self):
        self.coros: List[Any] = []
        self.keys: List[str|int] = []

    def add_task(self, key: str|int, coro: Any):
        """添加协程任务到池中"""
        if key in self.keys:
            raise ValueError(f"Key {key} already exists")
        self.coros.append(coro)
        self.keys.append(key)

    async def run(self) -> Dict[str|int, Any]:
        if len(self.keys) == 0:
            return {}
        # 并发执行所有任务并收集结果
        results = await asyncio.gather(*self.coros, return_exceptions=True)
        return dict(zip(self.keys, results))

    @classmethod
    async def worker(cls, queue: asyncio.Queue, results: asyncio.Queue):
        while True:
            key, coro = await queue.get()
            if coro is None:  # 结束信号
                queue.task_done()
                break
            # 执行协程任务
            result = await coro
            await results.put((key, result))
            queue.task_done()

    async def run_limit(self, limit: int) -> Dict[str|int, Any]:
        if len(self.keys) == 0:
            return {}

        queue = asyncio.Queue()
        queue_results = asyncio.Queue()
        # 创建limit个工作者协程，接入协程安全的任务队列来消费任务，实现同时只有limit个协程在运行
        workers = [asyncio.create_task(self.worker(queue, queue_results)) for _ in range(limit)]

        for index, coro in enumerate(self.coros):
            await queue.put((self.keys[index], coro))

        # 等待所有任务完成
        await queue.join()
        # 所有任务完成后，对工作者协程逐一发送结束信号
        for _ in range(limit):
            await queue.put((None, None))
        # 等待所有工作者完成
        await asyncio.gather(*workers)
        # 收集结果
        results = {}
        while not queue_results.empty():
            key, result = await queue_results.get()
            results[key] = result
        return results

    def clear(self):
        self.coros.clear()
        self.keys.clear()
