from pydantic import BaseModel
from typing import Optional, Any, Union, List
from enum import Enum
from commons.trace.tracer import ExtraInfoModel
from .sft_model_config import load_sft_model_config


class ChatType(str, Enum):
    chat = "chat"
    multimodal = "multimodal"

SftBaseModelType, chat_api, completion_api, host_map = load_sft_model_config()

class MultiModalType(str, Enum):
    image_url = "image_url"
    image = "image"
    text = "text"


class SftMultiModalImageDetail(str, Enum):
    auto = "auto"
    low = "low"
    high = "high"


class SftMultiModalImageUrl(BaseModel):
    url: str
    detail: Optional[SftMultiModalImageDetail] = None

# 多模态


class MultiModalContent(BaseModel):
    type: MultiModalType
    content: Optional[str] = None


class SftMultiModalText(BaseModel):
    type: MultiModalType
    text: Optional[str] = None


class SftMultiModalImage(BaseModel):
    type: MultiModalType
    image_url: Optional[SftMultiModalImageUrl] = None


class Message(BaseModel):
    # 消息内容
    content: Union[str, List[Union[MultiModalContent, SftMultiModalText, SftMultiModalImage]]] = ""
    # 消息角色（如：user）
    role: Optional[str] = None
    # 消息发言人, 如：小明，多人会话时使用
    name: Optional[str] = None
    tool_calls: Optional[Any] = None


class Usage(BaseModel):
    completion_tokens: int
    prompt_tokens: int
    total_tokens: int

class TraceInfo(ExtraInfoModel):
    usage: Optional[Usage] = None