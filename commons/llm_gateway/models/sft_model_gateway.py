import asyncio
import aiohttp
import requests
from requests.adapters import HTTPAdapter
from pydantic import BaseModel, <PERSON>
from typing import Optional, List, Generator, Union, Callable

from commons.llm_gateway.models.public_model_gateway import default_max_time_out
from .chat_data import (
    Message, 
    Usage, 
    ChatType, 
    SftBaseModelType, 
    TraceInfo,
    chat_api,
    completion_api,
    host_map,
)
import logging
import uuid
from commons.logger.logger import request_id_context, trace_id_context, span_id_context
from commons.monitor.prom import get_mcount
from commons.trace.tracer import (
    trace_span,
    trace_span_generator,
    async_trace_span,
    async_trace_span_generator
)

class SftChatParams(BaseModel):
    base_model: Optional["SftBaseModelType"] = None
    model: str = ""
    chat_type: Optional[ChatType] = ChatType.chat
    stream: bool = False
    max_tokens: int = 2000
    n: int = 1  # beam_search ，可在非贪婪解码下使用
    temperature: Optional[float] = None
    messages: List[Message]
    stop: Optional[List[str]] = None
    min_p: Optional[float] = None
    stop_token_ids: Optional[List[int]] = None
    prompt: Union[List[int], List[List[int]], str, List[str], None] = None
    guided_choice: Optional[List[str]] = None
    guided_regex: Optional[str] = None
    guided_json: Optional[Union[str, dict, BaseModel]] = None
    stream_options: Optional[dict] = None
    time_out: Optional[int] = None  # 超时时间，单位秒

class SftChoice(BaseModel):
    index: int
    finish_reason: Optional[str] = None
    message: Optional[Message] = None
    delta: Optional[Message] = None
    text: str = None


class SftChatResponse(BaseModel):
    id: Optional[str] = None
    object_: Optional[str] = Field(alias="object", default=None)
    created: Optional[int] = None
    model: Optional[str] = None
    usage: Optional[Usage] = None
    choices: Optional[List[SftChoice]] = None


class SftModelGateway(object):
    class Conf(BaseModel):
        host: str
        kas_host: str = "http://kmd-api.kas.wps.cn"
        # 普通模型和多模态模型共用token、uri
        token: str
        chat_api: dict = chat_api
        completion_api: dict = completion_api
        host_map: dict = host_map
        product_name: str = ""
        prom_token: bool = False
        record_token: bool = False
        record_token_key: str = "sft_token"
        record_token_callback: Optional[Callable[[str, int], None]] = None
        async_record_token_callback: Optional[Callable[[str, int], None]] = None
        get_tpm_callback: Optional[Callable[[str], int]] = None
        async_get_tpm_callback: Optional[Callable[[str], int]] = None

    def __init__(self, conf: Conf, pool_max=-1):
        self._conf = conf

        self._default_headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self._conf.token}"
        }
        self._sess = None
        self._pool_max = pool_max
        if pool_max > 0:
            self._sess = requests.Session()
            self._sess.mount(self._conf.host, HTTPAdapter(pool_connections=1, pool_maxsize=pool_max))
        if self._conf.prom_token:
            assert get_mcount() is not None
            self._prom_endpoint = "sft_model_gateway"
            self._prom_multimodal_endpoint = "sft_model_multimodal_gateway"

    def _record_token(self, key: str, token_count: int):
        if self._conf.record_token and self._conf.record_token_callback:
            self._conf.record_token_callback(f"{self._conf.record_token_key}_{key}", token_count)

    async def _async_record_token(self, key: str, token_count: int):
        if self._conf.record_token and self._conf.async_record_token_callback:
            await self._conf.async_record_token_callback(f"{self._conf.record_token_key}_{key}", token_count)

    def get_tpm(self, key: str) -> int:
        if self._conf.record_token and self._conf.get_tpm_callback:
            return self._conf.get_tpm_callback(f"{self._conf.record_token_key}_{key}")
        return 0

    async def async_get_tpm(self, key: str) -> int:
        if self._conf.record_token and self._conf.async_get_tpm_callback:
            return await self._conf.async_get_tpm_callback(f"{self._conf.record_token_key}_{key}")
        return 0
    
    def parse_params(self, params:SftChatParams):
        params = params.model_copy(deep=True)
        if params.chat_type == ChatType.chat:
            base_model = params.base_model or SftBaseModelType.default_chat_model
            if params.prompt is None:
                uri = self._conf.chat_api.get(base_model,
                                              self._conf.chat_api[SftBaseModelType.default_chat_model])
            else:
                uri = self._conf.completion_api.get(base_model,
                                                    self._conf.completion_api[SftBaseModelType.default_chat_model])
                params.messages = None
        else:
            base_model = params.base_model or SftBaseModelType.default_multimodal
            uri = self._conf.chat_api.get(base_model,
                                          self._conf.chat_api[SftBaseModelType.default_multimodal])

        post_url = f"{self._conf.host_map[base_model]}{uri}"
        match self._conf.host_map[base_model]:
            case "sre":
                post_url = f"{self._conf.host}{uri}"
            case "kas":
                post_url = f"{self._conf.kas_host}{uri}"
            case _:
                logging.error(f"unknown host: {self._conf.host_map[base_model]}, use default host: {self._conf.host}")
                post_url = f"{self._conf.host}{uri}"
        if params.stream:
            if params.stream_options:
                params.stream_options["include_usage"] = True
            else:
                params.stream_options = {"include_usage": True}
        else:
            params.stream_options = None

        params.base_model = None
        jdata = params.dict(by_alias=True, exclude_none=True, exclude={"chat_type"})
        params.base_model = base_model
        return params, post_url, jdata
    
    def create_header(self):
        sft_request_id = str(uuid.uuid4())
        headers = {
            **self._default_headers,
            "Client-Request-Id": request_id_context.get(),
            "X-Request-Id": sft_request_id,
            "X-Trace-Id": trace_id_context.get(),
            "X-Span-Id": span_id_context.get(),
        }
        logging.info(f"sft_request_id: {sft_request_id}")
        return headers
    
    def record_res(self, res: SftChatResponse, params: SftChatParams):
        if res.usage is not None and res.usage.total_tokens > 0:
            logging.info(
                f"base_model: {params.base_model}, total_tokens: {res.usage.total_tokens},"
                f" prompt_tokens: {res.usage.prompt_tokens}, completion_tokens: {res.usage.completion_tokens}")
            if self._conf.prom_token:
                get_mcount().record_token(self._prom_endpoint, res.usage.completion_tokens,
                                          res.usage.prompt_tokens, res.usage.total_tokens,
                                          model=params.base_model)
            if self._conf.record_token:
                self._record_token(params.chat_type, res.usage.total_tokens)

    @trace_span
    def chat(self, params: SftChatParams) -> Optional[SftChatResponse]:
        assert not params.stream
        try:
            params, post_url, jdata = self.parse_params(params)
            headers = self.create_header()
            if self._sess:
                r = self._sess.post(post_url, json=jdata, headers=headers)
            else:
                r = requests.post(post_url, json=jdata, headers=headers)
            if r.status_code != requests.codes.ok:
                errmsg = f"private model fail, url: {post_url}, status_code: {r.status_code}, res_text: {r.text}"
                logging.error(errmsg)
                return None
            res = SftChatResponse.parse_raw(r.text)
            self.record_res(res, params)
            return res, TraceInfo(usage=res.usage)
        except Exception as e:
            logging.error(e)
            return None

    @trace_span_generator
    def chat_stream(self, params: SftChatParams) -> Generator[Union[SftChatResponse, None], None, None]:
        assert params.stream
        try:
            params, post_url, jdata = self.parse_params(params)
            headers = self.create_header()
            if self._sess:
                r = self._sess.post(post_url, json=jdata, headers=headers, stream=True)
            else:
                r = requests.post(post_url, json=jdata, headers=headers, stream=True)
            if r.status_code != requests.codes.ok:
                errmsg = f"private model fail, url: {post_url}, status_code: {r.status_code}, res_text: {r.text}"
                logging.error(errmsg)
                yield None
            else:
                for line in r.iter_lines():
                    line = line.strip()
                    if line:
                        line = line.split(b"data:", 1)[1]
                        if line.strip() == b"[DONE]":
                            break
                        res = SftChatResponse.parse_raw(line)
                        self.record_res(res, params)
                        yield res, TraceInfo(usage=res.usage)
        except Exception as e:
            logging.error(e)
            yield None

    @async_trace_span
    async def async_chat(self, params: SftChatParams) -> Optional[SftChatResponse]:
        assert not params.stream
        try:
            params, post_url, jdata = self.parse_params(params)
            headers = self.create_header()
            conn = None
            timeout = aiohttp.ClientTimeout(total=params.time_out if params.time_out and params.time_out > 0 else default_max_time_out)
            if self._pool_max > 0:
                conn = aiohttp.TCPConnector(limit=self._pool_max)
            async with aiohttp.ClientSession(connector=conn) as sess:
                async with sess.post(post_url, json=jdata, headers=headers,timeout = timeout) as r:
                    code = r.status
                    text = await r.text(encoding="utf-8")
                    if code != 200:
                        errmsg = (f"ai-gateway fail, url: {post_url}, "
                                  f"status_code: {code},"
                                  f" res_text: {text}")
                        logging.error(errmsg)
                        return None
                    res = SftChatResponse.parse_raw(text)
                    self.record_res(res, params)
                    return res, TraceInfo(usage=res.usage)
        except Exception as e:
            logging.error(e)
            return None

    @async_trace_span_generator
    async def async_chat_stream(self, params: SftChatParams) -> Generator[Union[SftChatResponse, None], None, None]:
        assert params.stream
        try:
            params, post_url, jdata = self.parse_params(params)
            headers = self.create_header()
            conn = None
            if self._pool_max > 0:
                conn = aiohttp.TCPConnector(limit=self._pool_max)
            async with aiohttp.ClientSession(connector=conn) as sess:
                async with sess.post(post_url, json=jdata, headers=headers) as r:
                    code = r.status
                    if code != 200:
                        errmsg = (f"ai-gateway fail, url: {post_url}, "
                                  f"status_code: {code},"
                                  f" res_text: {await r.text(encoding='utf-8')}")
                        logging.error(errmsg)
                        yield None
                    else:
                        async for line in r.content:
                            line = line.strip()
                            if line:
                                line = line.split(b"data:", 1)[1]
                                if line.strip() == b"[DONE]":
                                    break
                                res = SftChatResponse.parse_raw(line)
                                self.record_res(res, params)
                                yield res, TraceInfo(usage=res.usage)
        except Exception as e:
            logging.error(e)
            yield None


if __name__ == "__main__":
    import asyncio
    import os
    import time


    async def _main():
        record_token = False
        _record_token_callback = None
        _arecord_token_callback = None
        _get_tpm_callback = None
        _aget_tpm_callback = None
        if record_token:
            from commons.db.redis5dao import Redis5Dao
            await Redis5Dao().init(hosts=["127.0.0.1:6379"], password="", prefix="llm_",
                                   cluster=False)

            def _record_token_callback(key: str, token_count: int):
                if Redis5Dao().is_init:
                    # 使用当前分钟作为键
                    minute_key = f"{key}_{int(time.time()) // 60}"
                    # 增加当前分钟的token
                    Redis5Dao().incrby(minute_key, token_count)
                    Redis5Dao().expire(minute_key, 60)

            async def _arecord_token_callback(key: str, token_count: int):
                if Redis5Dao().is_init:
                    # 使用当前分钟作为键
                    minute_key = f"{key}_{int(time.time()) // 60}"
                    # 增加当前分钟的token
                    await Redis5Dao().aincrby(minute_key, token_count)
                    await Redis5Dao().aexpire(minute_key, 60)

            def _get_tpm_callback(key: str) -> int:
                if Redis5Dao().is_init:
                    # 获取当前分钟的键
                    minute_key = f"{key}_{int(time.time()) // 60}"
                    # 获取当前分钟的令牌数
                    tpm = Redis5Dao().getint(minute_key)
                    return tpm
                else:
                    return 0

            async def _aget_tpm_callback(key: str) -> int:
                if Redis5Dao().is_init:
                    # 获取当前分钟的键
                    minute_key = f"{key}_{int(time.time()) // 60}"
                    # 获取当前分钟的令牌数
                    tpm = await Redis5Dao().agetint(minute_key)
                    return tpm
                else:
                    return 0

        sft_conf = SftModelGateway.Conf(
            host="http://privatization-model-test.kna.wps.cn",
            token=os.environ["kna_gateway_token"],
            record_token=record_token,
            record_token_callback=_record_token_callback,
            async_record_token_callback=_arecord_token_callback,
            get_tpm_callback=_get_tpm_callback,
            async_get_tpm_callback=_aget_tpm_callback
        )

        llm = SftModelGateway(sft_conf)

        message = Message(content="帮我写一首五言律诗", role="user")
        params = SftChatParams(stream=False, messages=[message])

        res = llm.chat(params)
        print(res)
        print("TPM", llm.get_tpm(params.chat_type))

        params.stream = True
        res = llm.chat_stream(params)
        for t in res:
            print(t)

        print("TPM", llm.get_tpm(params.chat_type))

        params.stream = False
        print(await llm.async_chat(params))
        print("TPM", await llm.async_get_tpm(params.chat_type))

        params.stream = True

        async for t in llm.async_chat_stream(params):
            print(t)

        print("TPM", await llm.async_get_tpm(params.chat_type))

        print("多模态 ####################################################")
        from commons.llm_gateway.models.chat_data import MultiModalType, \
            SftMultiModalText, SftMultiModalImage, SftMultiModalImageUrl
        params.stream = False
        params.chat_type = ChatType.multimodal
        image_url = "https://kna-common.ks3-cn-beijing.ksyuncs.com/insight-ai/%E6%A0%91%E5%8F%B6.png?KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb&Expires=2090195433&Signature=JvRgCf5H76Jk7oxSW966t5Ack3E%3D"
        messages = [
            Message(
                content=[
                    SftMultiModalImage(type=MultiModalType.image_url, image_url=SftMultiModalImageUrl(url=image_url)),
                    SftMultiModalText(type=MultiModalType.text, text="这些图片说的是什么")
                ],
                role="user"
            )
        ]
        params.messages = messages
        print("sync:")
        print(llm.chat(params))
        print("async:")
        print(await llm.async_chat(params))

        params.stream = True
        print("sync:")
        for t in llm.chat_stream(params):
            print(t)

        print("async:")
        async for t in llm.async_chat_stream(params):
            print(t)

        print("模型切换 ####################################################")
        message = Message(content="1帮我写一首五言律诗", role="user")
        print("sync:")
        params = SftChatParams(stream=False, messages=[message], base_model=SftBaseModelType.kas_qsearch_qwen2_5_14b)
        print(llm.chat(params))
        print("async:")
        params = SftChatParams(stream=False, messages=[message], base_model=SftBaseModelType.kas_qsearch_qwen2_5_14b)
        print(await llm.async_chat(params))

        print("sync:")
        params = SftChatParams(stream=True, messages=[message], base_model=SftBaseModelType.kas_qsearch_qwen2_5_14b)
        for t in llm.chat_stream(params):
            print(t)
        print("async:")
        params = SftChatParams(stream=True, messages=[message], base_model=SftBaseModelType.kas_qsearch_qwen2_5_14b)
        async for t in llm.async_chat_stream(params):
            print(t)

        if record_token:
            await Redis5Dao().close()


    asyncio.run(_main())
