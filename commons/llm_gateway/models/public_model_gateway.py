# 公网AI网关文档：https://km.wps.cn/a/0fG8l8q8?from=koa
import aiohttp
import requests
from requests.adapters import HTTPAdapter
from commons.llm_gateway.models.chat_data import Message, Usage, ChatType, TraceInfo
from commons.logger.logger import request_id_context
from typing import Dict, List, Optional, Generator, Any, Tuple, Callable
from pydantic import BaseModel, Field
import logging
from enum import Enum
from abc import ABC
from commons.monitor.prom import get_mcount
from commons.llm_gateway.models.public_model_llm_arguments_normalizer import ParameterProcessor
import time
from commons.trace.tracer import (
    trace_span,
    trace_span_generator,
    async_trace_span,
    async_trace_span_generator
)
from commons.context.private_context import get_private_context


class PublicCode(str, Enum):
    FAILED = "Failed"
    OK = "Success"


ERROR_AUDIT_ARR = ["AuditError.InternalInputNoPass", "AuditError.InternalOutputNoPass", "AuditError.LlmInputNoPass",
                   "AuditError.LlmOutputNoPass", "AuditError.LlmNoPass", "AuditError.ExternalInputAndOutputNoPass",
                   "AuditError.InputCheckTimeout", "AuditError.OutputCheckTimeout", "AuditHandlerError",
                   "AuditConfigInvalid", "AuditError.NoPassUseAnswer"]
ERROR_PRIVILEGE_ARR = ["Privilege.CheckFailed", "Privilege.UserNotAvailable", "Privilege.ServerFailed",
                       "Privilege.ProductError", "Privilege.IntentionError", "Privilege.Baned",
                       "WorkerApiUnknownError"]
ERROR_LIMIT_ARR = ["GateWayLimitError", "ModelLimitError", "ModelAccessRestricted", "RateLimited.User",
                   "RateLimited.TokenId", "AuditRateLimited"]

default_max_time_out = 60


class Choice(BaseModel):
    # 选项索引
    index: int
    # 结束原因，枚举：stop、length、content_filter
    finish_reason: str
    # 分数
    logprobs: float
    # 文本内容
    text: str
    tool_calls: Optional[List[Dict]] = None


class ChatResponse(BaseModel):
    code: Optional[str] = PublicCode.FAILED
    message: Optional[str] = None
    external_id: Optional[str] = None
    created: Optional[int] = None
    task_id: Optional[str] = None
    # 选择结果列表
    choices: Optional[List[Choice]] = None
    # 具体模型特有，非公共字段
    extended_resp_fields: Optional[Dict[str, Any]] = None
    usage: Optional[Usage] = None


class LLMParam(BaseModel):
    temperature: float = 0.7
    max_tokens: Optional[int] = None
    top_p: Optional[float] = None
    top_k: Optional[int] = None
    stop: Optional[List[str]] = None


class SecText(BaseModel):
    # 场景设置
    scene: Optional[str] = None
    # 业务编号,需要申请
    from_: str = Field(alias="from", default=None)
    # 代答策略标识
    # 0: 不开启代答  (默认)
    # 1: 开启代答（使用 提示词 上下文 进行代答）
    # 2:开启代答
    answer_flag: int = 0
    # 传递用户输入或者其他可变内容 比如 用户提问 选择的文本, 每个类型为一个独立的item
    extra_text: Optional[List[str]] = None


class RetryStrategy(BaseModel):
    retry_count: int
    timeout: int


class ChatParams(BaseModel):
    stream: bool
    # 模型配置
    provider: str
    model: str
    version: Optional[str] = None
    # 私有化参数，企业模型或公网模型，由上游传入
    model_type: Optional[str] = None

    chat_type: Optional[ChatType] = ChatType.chat
    messages: List[Message]
    # 对话背景、人物或功能设定
    context: Optional[str] = None
    examples: Optional[List[Message]] = None
    base_llm_arguments: Optional[LLMParam] = None
    extended_llm_arguments: Optional[Dict[str, Any]] = None
    sec_text: Optional[SecText] = None
    retry_strategy: Optional[RetryStrategy] = None
    company_id: Optional[str] = None
    ai_token: Optional[str] = None
    ai_product_name: Optional[str] = None
    ai_intention_code: Optional[str] = None
    user_id: Optional[str] = None
    tool_choice: Optional[str] = None
    tools: Optional[List[Dict]] = None
    time_out: Optional[int] = 60


class PublicModelGateway(object):
    class Conf(BaseModel):
        # 基础配置
        host: str
        token: str
        uid: str
        product_name: str
        intention_code: str
        api: str = "/api/v2/llm/chat"
        multimodal_api: str = "/api/v2/llm/multimodal"
        # chat模型默认配置
        provider: Optional[str] = None
        model: Optional[str] = None
        version: Optional[str] = None
        # multimodal模型默认配置
        multimodal_provider: Optional[str] = None
        multimodal_model: Optional[str] = None
        multimodal_version: Optional[str] = None
        # chat模型审核配置
        sec_scene: Optional[str] = None
        sec_from: Optional[str] = None
        # multimodal模型审核配置
        multimodal_sec_scene: Optional[str] = None
        multimodal_sec_from: Optional[str] = None

        # TPM监控
        prom_token: bool = False
        # TPM中间件上报配置
        record_token: bool = False
        record_token_key: str = "public_token"
        record_token_callback: Optional[Callable[[str, int], None]] = None
        async_record_token_callback: Optional[Callable[[str, int], None]] = None
        get_tpm_callback: Optional[Callable[[str], int]] = None
        async_get_tpm_callback: Optional[Callable[[str], int]] = None

    def __init__(self, conf: Conf, pool_max=-1):
        self._conf = conf
        self._chat_url = f"{self._conf.host}{self._conf.api}"
        self._multimodal_url = f"{self._conf.host}{self._conf.multimodal_api}"
        self._default_headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self._conf.token}",
            "AI-Gateway-Uid": f"{self._conf.uid}",
            "AI-Gateway-Product-Name": self._conf.product_name,
            "AI-Gateway-Intention-Code": self._conf.intention_code,
        }
        self._sess = None
        self._pool_max = pool_max
        # 初始化连接池
        async_conn = None
        if pool_max > 0:
            self._sess = requests.Session()
            self._sess.mount(self._conf.host, HTTPAdapter(pool_connections=1, pool_maxsize=pool_max))
            async_conn = aiohttp.TCPConnector(limit=pool_max)
        self._async_sess = aiohttp.ClientSession(connector=async_conn,
                                                 timeout=aiohttp.ClientTimeout(total=3600, sock_read=3600,
                                                                               connect=3600))
        # tpm监控，需初始化prom
        if self._conf.prom_token:
            assert get_mcount() is not None
            self._prom_endpoint = "public_model_gateway"
            self._prom_multimodal_endpoint = "public_model_multimodal_gateway"

    async def close(self):
        await self._async_sess.close()

    def get_conf(self) -> Conf:
        return self._conf

    def _record_token(self, key: str, token_count: int):
        if self._conf.record_token and self._conf.record_token_callback:
            self._conf.record_token_callback(f"{self._conf.record_token_key}_{key}", token_count)

    async def _async_record_token(self, key: str, token_count: int):
        if self._conf.record_token and self._conf.async_record_token_callback:
            await self._conf.async_record_token_callback(f"{self._conf.record_token_key}_{key}", token_count)

    def get_tpm(self, key: str) -> int:
        if self._conf.record_token and self._conf.get_tpm_callback:
            return self._conf.get_tpm_callback(f"{self._conf.record_token_key}_{key}")
        return 0

    async def async_get_tpm(self, key: str) -> int:
        if self._conf.record_token and self._conf.async_get_tpm_callback:
            return await self._conf.async_get_tpm_callback(f"{self._conf.record_token_key}_{key}")
        return 0

    def _get_dict_params(self, params: ChatParams) -> dict:
        if not params.sec_text:
            if params.chat_type == ChatType.multimodal:
                params.sec_text = SecText(scene=self._conf.multimodal_sec_scene)
                params.sec_text.from_ = self._conf.multimodal_sec_from
            else:
                params.sec_text = SecText(scene=self._conf.sec_scene)
                params.sec_text.from_ = self._conf.sec_from

        if not params.provider and not params.model:
            if params.chat_type == ChatType.multimodal:
                params.provider = self._conf.multimodal_provider
                params.model = self._conf.multimodal_model
                params.version = self._conf.multimodal_version
            else:
                params.provider = self._conf.provider
                params.model = self._conf.model
                params.version = self._conf.version
        jdata = params.dict(by_alias=True, exclude_none=True, exclude={"chat_type"})
        normalized_paras = ParameterProcessor(
            params.provider,
            params.model,
            params.extended_llm_arguments).process_params(params.messages).dict()

        jdata.update(normalized_paras)
        # print(jdata)
        return jdata

    def _get_httpcall_datas(self, params: ChatParams) -> Tuple[Dict, Dict]:
        headers = self._default_headers.copy()
        request_id = request_id_context.get()
        headers["Client-Request-Id"] = request_id
        if params.company_id is not None:
            headers["AI-Gateway-Company-Id"] = params.company_id
            params.company_id = None
        if params.user_id is not None:
            headers["AI-Gateway-Uid"] = params.user_id
            params.user_id = None
        if params.ai_token is not None:
            headers["AI-Gateway-Billing-Token"] = params.ai_token
            params.ai_token = None
        if params.ai_product_name is not None:
            headers["AI-Gateway-Product-Name"] = params.ai_product_name
            params.ai_product_name = None
        if params.ai_intention_code is not None:
            headers["AI-Gateway-Intention-Code"] = params.ai_intention_code
            params.ai_intention_code = None
        if params.model_type is not None:
            headers["AI-Gateway-Model-Choice"] = params.model_type
            params.model_type = None
        # logging.debug(f"llm chat headers: {headers}")
        jdata = self._get_dict_params(params)
        return headers, jdata

    def parse_params(self, params: ChatParams):
        if params.chat_type == ChatType.chat:
            url = self._chat_url
        else:
            url = self._multimodal_url
        headers, jdata = self._get_httpcall_datas(params)
        private_context_headers = get_private_context()
        if private_context_headers is not None:
            headers.update(private_context_headers)
        return url, headers, jdata

    def record_res(self, usage: Usage, params: ChatParams):
        log_text = (f"model provider: {params.provider}, model version: {params.model},"
                    f" sec_text: scene: {params.sec_text.scene}, from: {params.sec_text.from_},"
                    f" answer_flag: {params.sec_text.answer_flag}, extra_text len: {len(params.sec_text.extra_text) if params.sec_text.extra_text is not None else 'None'},")
        if usage is not None and usage.total_tokens > 0:
            log_text += (f" model provider: {params.provider}, model version: {params.model},"
                         f" total_tokens: {usage.total_tokens}, prompt_tokens: {usage.prompt_tokens}, completion_tokens: {usage.completion_tokens}")
            if self._conf.prom_token:
                get_mcount().record_token(self._prom_endpoint, usage.completion_tokens, usage.prompt_tokens,
                                          usage.total_tokens, params.model, params.provider, params.version)
            if self._conf.record_token:
                self._record_token(f"{params.provider}_{params.model}", usage.total_tokens)
        logging.info(log_text)

    @trace_span
    def chat(self, params: ChatParams) -> ChatResponse | Tuple[ChatResponse, TraceInfo]:
        assert not params.stream
        url, headers, jdata = self.parse_params(params)
        usage = None
        try:
            if self._sess:
                r = self._sess.post(url, json=jdata, headers=headers)
            else:
                r = requests.post(url, json=jdata, headers=headers)
            if r.status_code != requests.codes.ok:
                errmsg = f"ai-gateway fail, url: {url}, status_code: {r.status_code}, res_text: {r.text}"
                logging.error(errmsg)
                resp = ChatResponse.parse_raw(r.text)
                # 审核拒绝或权益不通过
                if resp.code in ERROR_AUDIT_ARR or resp.code in ERROR_PRIVILEGE_ARR or resp.code in ERROR_LIMIT_ARR:
                    return resp

                return ChatResponse(code=PublicCode.FAILED, message=errmsg)

            res = ChatResponse.parse_raw(r.text)
            # 审核异常也通过200状态码返回
            if res.code in ERROR_AUDIT_ARR:
                errmsg = f"ai-gateway fail, url: {url}, status_code: {r.status_code}, res_text: {r.text}"
                logging.error(errmsg)
                logging.warning(f"sec_text: {params.sec_text.from_}")
                return res
            usage = res.usage
            return res, TraceInfo(usage=res.usage)
        except Exception as e:
            logging.error(e)
            return ChatResponse(code=PublicCode.FAILED, message=str(e))
        finally:
            self.record_res(usage, params)

    @trace_span_generator
    def chat_stream(self, params: ChatParams) -> Generator[ChatResponse, None, None] | Generator[
        Tuple[ChatResponse, TraceInfo], None, None]:
        assert params.stream
        url, headers, jdata = self.parse_params(params)
        usage = None
        try:
            if self._sess:
                r = self._sess.post(url, json=jdata, headers=headers, stream=True, timeout=(3600, 3600))
            else:
                r = requests.post(url, json=jdata, headers=headers, stream=True, timeout=(3600, 3600))
            if r.status_code != requests.codes.ok:
                errmsg = f"ai-gateway fail, url: {url}, status_code: {r.status_code}, res_text: {r.text}"
                logging.error(errmsg)
                resp = ChatResponse.parse_raw(r.text)
                if resp.code in ERROR_AUDIT_ARR or resp.code in ERROR_PRIVILEGE_ARR or resp.code in ERROR_LIMIT_ARR:
                    yield resp
                else:
                    yield ChatResponse(code=PublicCode.FAILED, message=errmsg)
            else:
                for line in r.iter_lines():
                    line = line.strip()
                    if line:
                        if not line.startswith(b"data:"):
                            errmsg = f"ai-gateway fail, url: {url}, status_code: {r.status_code}, res_text: {line.decode('utf-8')}"
                            logging.error(errmsg)
                            resp = ChatResponse.parse_raw(line)
                            if resp.code in ERROR_AUDIT_ARR:
                                logging.warning(f"sec_text: {params.sec_text.from_}")
                                yield resp
                            else:
                                yield ChatResponse(code=PublicCode.FAILED, message=errmsg)
                            return
                        line = line.split(b"data:", 1)[1]
                        resp = ChatResponse.parse_raw(line)
                        if resp.code == PublicCode.OK:
                            if resp.usage is not None and resp.usage.total_tokens > 0:
                                # 流式请求有时候是每一帧都返回usage，有时候是最终帧返回usage，这里取最后一帧的usage
                                usage = resp.usage
                        else:
                            errmsg = f"ai-gateway fail, url: {url}, code:{resp.code}, msg:{resp.message}"
                            logging.error(errmsg)
                        yield resp, TraceInfo(usage=resp.usage)
        except Exception as e:
            logging.error(e)
            yield ChatResponse(code=PublicCode.FAILED, message=str(e))
        finally:
            self.record_res(usage, params)

    @async_trace_span
    async def async_chat(self, params: ChatParams) -> ChatResponse | Tuple[ChatResponse, TraceInfo]:
        assert not params.stream
        url, headers, jdata = self.parse_params(params)
        usage = None
        try:
            timeout = aiohttp.ClientTimeout(total=params.time_out if params.time_out and params.time_out > 0 else default_max_time_out)
            async with self._async_sess.post(url, json=jdata, headers=headers, timeout=timeout) as r:
                code = r.status
                text = await r.text(encoding="utf-8")
                if code != 200:
                    errmsg = (f"ai-gateway fail, url: {url}, "
                              f"status_code: {code},"
                              f" res_text: {text}")
                    logging.error(errmsg)
                    resp = ChatResponse.parse_raw(text)
                    # 审核拒绝或权益不通过
                    if resp.code in ERROR_AUDIT_ARR or resp.code in ERROR_PRIVILEGE_ARR or resp.code in ERROR_LIMIT_ARR:
                        logging.warning(f"sec_text: {params.sec_text.from_}")
                        return resp

                    return ChatResponse(code=PublicCode.FAILED, message=errmsg)
                res = ChatResponse.parse_raw(text)
                # 审核异常也通过200状态码返回
                if res.code in ERROR_AUDIT_ARR:
                    errmsg = f"ai-gateway fail, url: {url}, status_code: {code}, res_text: {text}"
                    logging.error(errmsg)
                    logging.warning(f"sec_text: {params.sec_text.from_}")
                    return res
                usage = res.usage
                return res, TraceInfo(usage=res.usage)
        except Exception as e:
            logging.error(e)
            return ChatResponse(code=PublicCode.FAILED, message=str(e))
        finally:
            self.record_res(usage, params)

    @async_trace_span_generator
    async def async_chat_stream(self, params: ChatParams) -> Generator[ChatResponse, None, None] | Generator[
        Tuple[ChatResponse, TraceInfo], None, None]:
        assert params.stream
        url, headers, jdata = self.parse_params(params)
        usage = None
        try:
            async with self._async_sess.post(url, json=jdata, headers=headers) as r:
                code = r.status
                if code != 200:
                    res_text = await r.text(encoding='utf-8')
                    errmsg = (f"ai-gateway fail, url: {url}, "
                              f"status_code: {code},"
                              f" res_text: {res_text}")
                    logging.error(errmsg)
                    resp = ChatResponse.parse_raw(res_text)
                    if resp.code in ERROR_AUDIT_ARR or resp.code in ERROR_PRIVILEGE_ARR or resp.code in ERROR_LIMIT_ARR:
                        logging.warning(f"sec_text: {params.sec_text.from_}")
                        yield resp
                    else:
                        yield ChatResponse(code=PublicCode.FAILED, message=errmsg)
                else:
                    async for line in r.content:
                        line = line.strip()
                        if line:
                            if not line.startswith(b"data:"):
                                errmsg = (f"ai-gateway fail, url: {url},"
                                          f" status_code: {code},"
                                          f" res_text: {line.decode('utf-8')}")
                                logging.error(errmsg)
                                resp = ChatResponse.parse_raw(line)
                                if resp.code in ERROR_AUDIT_ARR:
                                    logging.warning(f"sec_text: {params.sec_text.from_}")
                                    yield resp
                                else:
                                    yield ChatResponse(code=PublicCode.FAILED, message=errmsg)
                                return
                            line = line.split(b"data:", 1)[1]
                            resp = ChatResponse.parse_raw(line)
                            if resp.code == PublicCode.OK:
                                if resp.usage is not None and resp.usage.total_tokens > 0:
                                    # 流式请求有时候是每一帧都返回usage，有时候是最终帧返回usage，这里取最后一帧的usage
                                    usage = resp.usage
                            else:
                                errmsg = (f"ai-gateway fail, url: {url},"
                                          f" code:{resp.code}, msg:{resp.message}")
                                logging.error(errmsg)
                            yield resp, TraceInfo(usage=resp.usage)
        except Exception as e:
            logging.error(e)
            yield ChatResponse(code=PublicCode.FAILED, message=str(e))
        finally:
            self.record_res(usage, params)


if __name__ == "__main__":
    import asyncio
    import os
    from commons.logger.logger import init_logger

    init_logger("", "DEBUG", "err")


    async def _main():
        record_token = False
        _record_token_callback = None
        _arecord_token_callback = None
        _get_tpm_callback = None
        _aget_tpm_callback = None
        if record_token:
            from commons.db.redis5dao import Redis5Dao
            await Redis5Dao().init(hosts=["127.0.0.1:6379"], password="", prefix="llm_", cluster=False)

            def _record_token_callback(key: str, token_count: int):
                if Redis5Dao().is_init:
                    # 使用当前分钟作为键
                    minute_key = f"{key}_{int(time.time()) // 60}"
                    # 增加当前分钟的token
                    Redis5Dao().incrby(minute_key, token_count)
                    Redis5Dao().expire(minute_key, 60)

            async def _arecord_token_callback(key: str, token_count: int):
                if Redis5Dao().is_init:
                    # 使用当前分钟作为键
                    minute_key = f"{key}_{int(time.time()) // 60}"
                    # 增加当前分钟的token
                    await Redis5Dao().aincrby(minute_key, token_count)
                    await Redis5Dao().aexpire(minute_key, 60)

            def _get_tpm_callback(key: str) -> int:
                if Redis5Dao().is_init:
                    # 获取当前分钟的键
                    minute_key = f"{key}_{int(time.time()) // 60}"
                    # 获取当前分钟的令牌数
                    tpm = Redis5Dao().getint(minute_key)
                    return tpm
                else:
                    return 0

            async def _aget_tpm_callback(key: str) -> int:
                if Redis5Dao().is_init:
                    # 获取当前分钟的键
                    minute_key = f"{key}_{int(time.time()) // 60}"
                    # 获取当前分钟的令牌数
                    tpm = await Redis5Dao().agetint(minute_key)
                    return tpm
                else:
                    return 0

        pub_conf = PublicModelGateway.Conf(
            host="http://aigc-gateway-test.ksord.com",
            token=os.environ["ai_gateway_token"],
            uid="9047",
            product_name="wps-kanmail-qa",
            intention_code="aigctest",
            provider="deepseek",
            model="deepseek-chat",
            version=None,
            multimodal_provider="ali",
            multimodal_model="qwen-vl-max-0809",
            multimodal_version=None,
            sec_from="AI_DRIVE_KNOWLEDGE",
            record_token=record_token,
            record_token_callback=_record_token_callback,
            async_record_token_callback=_arecord_token_callback,
            get_tpm_callback=_get_tpm_callback,
            async_get_tpm_callback=_aget_tpm_callback
        )
        llm = PublicModelGateway(pub_conf)

        message = Message(content="写一篇200字的风景文", role="user")
        params = ChatParams(stream=False, provider="deepseek", model="deepseek-chat", messages=[message])
        # params.company_id = "41000207"
        # params.ai_token = "1"
        # params.ai_product_name = "1"
        # params.ai_intention_code = "1"
        params.company_id = None
        params.ai_token = None
        params.ai_product_name = None
        params.ai_intention_code = None
        tpm_key = f"{params.provider}_{params.model}"
        res = llm.chat(params)
        print(res)
        print("TPM", llm.get_tpm(tpm_key))

        params.stream = True
        res = llm.chat_stream(params)
        for t in res:
            if t.code == PublicCode.OK:
                print(t.choices[0].text)

        print("TPM", llm.get_tpm(tpm_key))

        params.stream = False
        # print(await llm.async_chat(params))
        print("TPM", await llm.async_get_tpm(tpm_key))

        params.stream = True

        async for t in llm.async_chat_stream(params):
            if t.code == PublicCode.OK:
                print(t.choices[0].text)

        print("TPM", await llm.async_get_tpm(tpm_key))

        print("多模态 ####################################################")
        from commons.llm_gateway.models.chat_data import MultiModalContent, MultiModalType
        params.stream = False
        params.provider = None
        params.model = None
        params.sec_text = None
        params.chat_type = ChatType.multimodal
        image_url = "https://kna-common.ks3-cn-beijing.ksyuncs.com/insight-ai/%E6%A0%91%E5%8F%B6.png?KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb&Expires=**********&Signature=JvRgCf5H76Jk7oxSW966t5Ack3E%3D"
        res = requests.get(image_url)
        import base64
        image_base64 = base64.b64encode(res.content).decode('utf-8')
        messages = [
            Message(
                content=[
                    MultiModalContent(type=MultiModalType.image_url, content=image_url),
                    MultiModalContent(type=MultiModalType.image, content=f"data:image/png;base64,{image_base64}"),
                    MultiModalContent(type=MultiModalType.text, content="这些图片说的是什么")
                ],
                role="user"
            )
        ]
        params.messages = messages
        print("sync:")
        print(llm.chat(params))
        print("async:")
        print(await llm.async_chat(params))

        params.stream = True
        print("sync:")
        for t in llm.chat_stream(params):
            if t.code == PublicCode.OK:
                print(t.choices[0].text)

        print("async:")
        async for t in llm.async_chat_stream(params):
            if t.code == PublicCode.OK:
                print(t.choices[0].text)

        await llm.close()
        if record_token:
            await Redis5Dao().close()


    asyncio.run(_main())
