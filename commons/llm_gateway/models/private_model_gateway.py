import asyncio
import aiohttp
from pydantic import BaseModel, <PERSON>
from typing import Optional, List, Generator, Tuple, Dict, Any, AsyncGenerator, Union, Callable
from enum import Enum
import requests
from requests.adapters import HTTPAdapter
from commons.llm_gateway.models.chat_data import Usage, Message, TraceInfo
import logging

from commons.llm_gateway.models.public_model_gateway import default_max_time_out
from commons.monitor.prom import get_mcount
from commons.logger.logger import request_id_context
from commons.trace.tracer import (
    trace_span,
    trace_span_generator,
    async_trace_span,
    async_trace_span_generator
)


class PrivateCode(int, Enum):
    OK = 0
    FAILED = 1


# 私有化AI网关v1提供以下4种模型对接
class Platform(str, Enum):
    minimax = "minimax"
    zhipu = "zhipu"
    baidu = "baidu"
    private = "private"

    def __str__(self):
        return str(self.value)


class MinimaxMessage(BaseModel):
    sender_type: str
    text: str


class MinimaxRoleMeta(BaseModel):
    user_name: str
    bot_name: str


class MinimaxChatParams(BaseModel):
    model: str
    stream: bool = True
    prompt: str
    role_meta: MinimaxRoleMeta
    tokens_to_generate: int = 2000
    messages: List[MinimaxMessage]
    temperature: Optional[float] = None
    top_p: Optional[float] = None


class BaiduParams(BaseModel):
    stream: bool = True
    messages: List[Message]


class ZhipuParams(BaseModel):
    prompt: str
    history: List[str] = []
    temperature: Optional[float] = None
    top_p: Optional[float] = None


class PrivParams(BaseModel):
    messages: List[Message]


class PrivateChatParams(BaseModel):
    model: str = ""
    stream: bool = False
    max_tokens: int = 2000
    temperature: Optional[float] = None
    messages: List[Message]
    stop: Optional[List[str]] = None
    top_p: Optional[float] = None
    stop_token_ids: Optional[List[int]] = None
    private_uid: Optional[str] = None
    private_product_name: Optional[str] = None
    time_out: Optional[int] = None

class MinimaxChoice(BaseModel):
    delta: str
    finish_reason: str
    index: int
    logprobes: int
    text: str


class MinimaxBaseResp(BaseModel):
    status_code: int
    status_msg: str


class MinimaxRespData(BaseModel):
    created: int = 0
    choices: List[MinimaxChoice] = []
    usage: Optional[Usage] = None
    base_resp: Optional[MinimaxBaseResp] = None


class BaiduRespData(BaseModel):
    id: Optional[str] = None
    object: Optional[str] = None
    created: int = 0
    sentence_id: int = 0
    is_end: Optional[bool] = None
    is_truncated: Optional[bool] = None
    result: Optional[str] = None
    need_clear_history: Optional[bool] = None
    ban_round: Optional[int] = None
    error_code: int
    error_msg: Optional[str] = None
    usage: Optional[Usage] = None


class ZhipuMeta(BaseModel):
    generated_token_num: Optional[int] = None
    version: Optional[str] = None
    task_status: Optional[str] = None
    task_id: Optional[str] = None
    request_id: Optional[str] = None


class ZhipuRespData(BaseModel):
    id: Optional[str] = None
    data: Optional[str] = None
    finish_reason: Optional[str] = None
    meta: Optional[ZhipuMeta] = None
    code: int
    msg: Optional[str] = None
    usage: Optional[Usage] = None


class PrivRespData(BaseModel):
    code: Optional[int] = None
    message: Optional[str] = None
    id: Optional[str] = None
    created: int = 0
    status: Optional[str] = None
    content: str
    usage: Optional[Usage] = None


class CommonResponse(BaseModel):
    errno: int
    errmsg: str
    sensitive_code: int
    data: object


class PrivateChoice(BaseModel):
    index: int
    finish_reason: str
    logprobs: float
    text: str


class PrivateChatResponse(BaseModel):
    code: PrivateCode = PrivateCode.FAILED
    message: Optional[str] = None
    created: Optional[int] = None
    usage: Optional[Usage] = None
    choices: Optional[List[PrivateChoice]] = None


class PrivateModelGateway(object):
    class Conf(BaseModel):
        host: str
        platform: Platform
        api: str = ""
        prom_token: bool = False
        minimax_model: str = "abab5-chat"
        record_token: bool = False
        record_token_key: str = "private_token"
        record_token_callback: Optional[Callable[[str, int], None]] = None
        async_record_token_callback: Optional[Callable[[str, int], None]] = None
        get_tpm_callback: Optional[Callable[[str], int]] = None
        async_get_tpm_callback: Optional[Callable[[str], int]] = None

    def __init__(self, conf: Conf, pool_max=-1):
        self._conf = conf
        self._conf.api = f"/api/v1/{self._conf.platform}/chat"
        self._chat_url = f"{self._conf.host}{self._conf.api}"
        self._default_headers = {
            "Content-Type": "application/json",
            "Accept": "text/event-stream",
            "AIGC-GATEWAY-WPS-UID": "0",
            "AIGC-GATEWAY-PRODUCT-NAME": "ai-insight"
        }
        self._sess = None
        self._pool_max = pool_max
        if pool_max > 0:
            self._sess = requests.Session()
            self._sess.mount(self._conf.host, HTTPAdapter(pool_connections=1, pool_maxsize=pool_max))
        if self._conf.prom_token:
            assert get_mcount() is not None
            self._prom_endpoint = "private_model_gateway"

    def get_conf(self) -> Conf:
        return self._conf

    def _record_token(self, token_count: int, key: str = ""):
        if self._conf.record_token and self._conf.record_token_callback:
            self._conf.record_token_callback(f"{self._conf.record_token_key}_{key}", token_count)

    async def _async_record_token(self, token_count: int, key: str = ""):
        if self._conf.record_token and self._conf.async_record_token_callback:
            await self._conf.async_record_token_callback(f"{self._conf.record_token_key}_{key}", token_count)

    def get_tpm(self, key: str = "") -> int:
        if self._conf.record_token and self._conf.get_tpm_callback:
            return self._conf.get_tpm_callback(f"{self._conf.record_token_key}_{key}")
        return 0

    async def async_get_tpm(self, key: str = "") -> int:
        if self._conf.record_token and self._conf.async_get_tpm_callback:
            return await self._conf.async_get_tpm_callback(f"{self._conf.record_token_key}_{key}")
        return 0

    def _get_dict_params(self, params: PrivateChatParams) -> dict:
        if self._conf.platform == Platform.minimax:
            model = params.model if len(params.model) > 0 else self._conf.minimax_model
            messages = []
            for m in params.messages:
                messages.append(MinimaxMessage(sender_type=m.role, text=m.content))
            jdata = MinimaxChatParams(model=model,
                                      stream=params.stream,
                                      prompt="WPSAI是金山办公与合作伙伴共同开发的AI工作助理，WPSAI能够理解自然语言并生成对应的回复，回复思路清晰，逻辑严密，推理精确。",
                                      role_meta=MinimaxRoleMeta(user_name="用户", bot_name="WPSAI"),
                                      tokens_to_generate=params.max_tokens,
                                      messages=messages,
                                      temperature=params.temperature,
                                      top_p=params.top_p).dict(by_alias=True, exclude_none=True)
        elif self._conf.platform == Platform.baidu:
            jdata = BaiduParams(stream=params.stream, messages=params.messages).dict(by_alias=True, exclude_none=True)
        elif self._conf.platform == Platform.zhipu:
            history = []
            if len(params.messages) > 1 and len(params.messages) % 2 == 1:
                for m in params.messages[:-1]:
                    history.append(m.content)
            jdata = ZhipuParams(prompt=params.messages[-1].content, history=history, temperature=params.temperature,
                                top_p=params.top_p).dict(by_alias=True, exclude_none=True)
        elif self._conf.platform == Platform.private:
            jdata = PrivParams(messages=params.messages).dict(by_alias=True, exclude_none=True)
        else:
            raise Exception(f"private platform type error: platform={self._conf.platform}")

        return jdata

    def _get_httpcall_datas(self, params: PrivateChatParams) -> Tuple[Dict, Dict]:
        headers = self._default_headers.copy()
        headers["Client-Request-Id"] = request_id_context.get()
        if params.private_uid:
            headers["AIGC-GATEWAY-WPS-UID"] = params.private_uid
        if params.private_product_name:
            headers["AIGC-GATEWAY-PRODUCT-NAME"] = params.private_product_name
        logging.debug(f"llm chat headers: {headers}")
        jdata = self._get_dict_params(params)
        return headers, jdata

    def _get_resp_datas(self, resp_text: Union[str, bytes]) -> PrivateChatResponse:
        comm_resp = CommonResponse.parse_raw(resp_text)
        if comm_resp.errno != 0:
            return PrivateChatResponse(code=PrivateCode.FAILED, message=comm_resp.errmsg)
        if self._conf.platform == Platform.minimax:
            data = MinimaxRespData.parse_obj(comm_resp.data)
            if data.base_resp and data.base_resp.status_code != 0:
                return PrivateChatResponse(code=PrivateCode.FAILED, message=data.base_resp.status_msg)
            choices = []
            for c in data.choices:
                choices.append(PrivateChoice(index=c.index, finish_reason=c.finish_reason,
                                             logprobs=c.logprobes, text=c.delta))
            return PrivateChatResponse(code=PrivateCode.OK, message="success",
                                       created=data.created, usage=data.usage,
                                       choices=choices)
        elif self._conf.platform == Platform.baidu:
            data = BaiduRespData.parse_obj(comm_resp.data)
            if data.error_code != 0:
                return PrivateChatResponse(code=PrivateCode.FAILED, message=data.error_msg)
            if data.is_end is not None and not data.is_end and data.usage is not None:
                data.usage.total_tokens = 0
                data.usage.prompt_tokens = 0
                data.usage.completion_tokens = 0
            return PrivateChatResponse(code=PrivateCode.OK, message="success",
                                       created=data.created, usage=data.usage,
                                       choices=[PrivateChoice(index=data.sentence_id, finish_reason="",
                                                              logprobs=0.0, text=data.result)])
        elif self._conf.platform == Platform.zhipu:
            data = ZhipuRespData.parse_obj(comm_resp.data)
            if data.code != 0:
                return PrivateChatResponse(code=PrivateCode.FAILED, message=data.msg)
            if data.finish_reason and data.finish_reason == "finish":
                data.data = ""
            return PrivateChatResponse(code=PrivateCode.OK, message="success",
                                       created=0, usage=data.usage,
                                       choices=[PrivateChoice(index=0, finish_reason=data.finish_reason,
                                                              logprobs=0.0, text=data.data)])
        elif self._conf.platform == Platform.private:
            data = PrivRespData.parse_obj(comm_resp.data)
            if data.code != 0:
                return PrivateChatResponse(code=PrivateCode.FAILED, message=data.message)
            choices = [PrivateChoice(index=0, finish_reason="", logprobs=0.0, text=data.content)]
            return PrivateChatResponse(code=PrivateCode.OK, message="success",
                                       created=data.created, usage=data.usage,
                                       choices=choices)
        else:
            raise Exception(f"private platform type error: platform={self._conf.platform}")

    # 私有化AI网关V1写死了只有流式接口，非流式需自己转换
    @trace_span
    def chat(self, params: PrivateChatParams) -> PrivateChatResponse:
        assert not params.stream
        headers, jdata = self._get_httpcall_datas(params)
        try:
            if self._sess:
                r = self._sess.post(self._chat_url, json=jdata, headers=headers)
            else:
                r = requests.post(self._chat_url, json=jdata, headers=headers)
            if r.status_code != requests.codes.ok:
                errmsg = f"private ai-gateway fail, url: {self._chat_url}, status_code: {r.status_code}, res_text: {r.text}"
                logging.error(errmsg)
                return PrivateChatResponse(code=PrivateCode.FAILED, message=errmsg)
            res_text = ""
            created = 0
            usage = None
            for line in r.iter_lines():
                line = line.strip()
                if line:
                    line = line.split(b"data:", 1)
                    if len(line) == 1:
                        continue
                    line = line[1]
                    res = self._get_resp_datas(line)
                    for c in res.choices:
                        res_text = res_text + c.text
                    created = res.created
                    if res.usage is not None and res.usage.total_tokens > 0:
                        usage = res.usage
                        if self._conf.prom_token:
                            logging.info(
                                f"{self._prom_endpoint}, total_tokens: {res.usage.total_tokens},"
                                f" prompt_tokens: {res.usage.prompt_tokens}, completion_tokens: {res.usage.completion_tokens}")
                            get_mcount().record_token(self._prom_endpoint, res.usage.completion_tokens,
                                                      res.usage.prompt_tokens,
                                                      res.usage.total_tokens,
                                                      model=params.model)
                        if self._conf.record_token:
                            self._record_token(usage.total_tokens)
            return PrivateChatResponse(code=PrivateCode.OK, message="success",
                                       created=created, usage=usage,
                                       choices=[PrivateChoice(index=0, finish_reason="", logprobs=0.0, text=res_text)]), TraceInfo(usage=usage)
        except Exception as e:
            logging.error(e)
            return PrivateChatResponse(code=PrivateCode.FAILED, message=str(e))

    @async_trace_span
    async def async_chat(self, params: PrivateChatParams) -> PrivateChatResponse:
        assert not params.stream
        headers, jdata = self._get_httpcall_datas(params)
        try:
            conn = None
            if self._pool_max > 0:
                conn = aiohttp.TCPConnector(limit=self._pool_max)
            timeout = aiohttp.ClientTimeout(
                total=params.time_out if params.time_out and params.time_out > 0 else default_max_time_out)
            async with aiohttp.ClientSession(connector=conn) as sess:
                async with sess.post(self._chat_url, json=jdata, headers=headers,timeout = timeout) as r:
                    code = r.status
                    if code != requests.codes.ok:
                        errmsg = f"private ai-gateway fail, url: {self._chat_url}, " \
                                 f"status_code: {code}, " \
                                 f"res_text: {await r.text(encoding='utf-8')}"
                        logging.error(errmsg)
                        return PrivateChatResponse(code=PrivateCode.FAILED, message=errmsg)
                    res_text = ""
                    created = 0
                    usage = None
                    async for line in r.content:
                        line = line.strip()
                        if line:
                            line = line.split(b"data:", 1)
                            if len(line) == 1:
                                continue
                            line = line[1]
                            res = self._get_resp_datas(line)
                            for c in res.choices:
                                res_text = res_text + c.text
                            created = res.created
                            if res.usage is not None and res.usage.total_tokens > 0:
                                usage = res.usage
                                if self._conf.prom_token:
                                    logging.info(
                                        f"{self._prom_endpoint}, total_tokens: {res.usage.total_tokens},"
                                        f" prompt_tokens: {res.usage.prompt_tokens}, "
                                        f"completion_tokens: {res.usage.completion_tokens}")
                                    get_mcount().record_token(self._prom_endpoint, res.usage.completion_tokens,
                                                              res.usage.prompt_tokens,
                                                              res.usage.total_tokens,
                                                              model=params.model)
                                if self._conf.record_token:
                                    await self._async_record_token(usage.total_tokens)
                    return PrivateChatResponse(code=PrivateCode.OK, message="success",
                                               created=created, usage=usage,
                                               choices=[PrivateChoice(index=0, finish_reason="", logprobs=0.0,
                                                                      text=res_text)]), TraceInfo(usage=usage)
        except Exception as e:
            logging.error(e)
            return PrivateChatResponse(code=PrivateCode.FAILED, message=str(e))

    @trace_span_generator
    def chat_stream(self, params: PrivateChatParams) -> Generator[PrivateChatResponse, None, None]:
        assert params.stream
        headers, jdata = self._get_httpcall_datas(params)
        try:
            if self._sess:
                r = self._sess.post(self._chat_url, json=jdata, headers=headers, stream=True)
            else:
                r = requests.post(self._chat_url, json=jdata, headers=headers, stream=True)
            if r.status_code != requests.codes.ok:
                errmsg = f"private ai-gateway fail, url: {self._chat_url}, status_code: {r.status_code}, res_text: {r.text}"
                logging.error(errmsg)
                yield PrivateChatResponse(code=PrivateCode.FAILED, message=errmsg)
            else:
                for line in r.iter_lines():
                    line = line.strip()
                    if line:
                        line = line.split(b"data:", 1)
                        if len(line) == 1:
                            continue
                        line = line[1]
                        res = self._get_resp_datas(line)
                        if res.code == PrivateCode.OK:
                            if res.usage is not None and \
                                    res.usage.total_tokens > 0:
                                if self._conf.prom_token:
                                    logging.info(
                                        f"{self._prom_endpoint}, total_tokens: {res.usage.total_tokens},"
                                        f" prompt_tokens: {res.usage.prompt_tokens}, completion_tokens: {res.usage.completion_tokens}")
                                    get_mcount().record_token(self._prom_endpoint, res.usage.completion_tokens,
                                                              res.usage.prompt_tokens,
                                                              res.usage.total_tokens,
                                                              model=params.model)
                                if self._conf.record_token:
                                    self._record_token(res.usage.total_tokens)
                        else:
                            errmsg = f"private ai-gateway fail, url: {self._chat_url}, code:{res.code}, msg:{res.message}"
                            logging.error(errmsg)
                        yield res, TraceInfo(usage=res.usage)
        except Exception as e:
            logging.error(e)
            yield PrivateChatResponse(code=PrivateCode.FAILED, message=str(e))

    @async_trace_span_generator
    async def async_chat_stream(self, params: PrivateChatParams) -> AsyncGenerator[PrivateChatResponse, Any]:
        assert params.stream
        headers, jdata = self._get_httpcall_datas(params)
        try:
            conn = None
            if self._pool_max > 0:
                conn = aiohttp.TCPConnector(limit=self._pool_max)
            async with aiohttp.ClientSession(connector=conn) as sess:
                async with sess.post(self._chat_url, json=jdata, headers=headers) as r:
                    code = r.status
                    if code != requests.codes.ok:
                        errmsg = f"private ai-gateway fail, url: {self._chat_url}, " \
                                 f"status_code: {code}, " \
                                 f"res_text: {await r.text(encoding='utf-8')}"
                        logging.error(errmsg)
                        yield PrivateChatResponse(code=PrivateCode.FAILED, message=errmsg)
                    else:
                        async for line in r.content:
                            line = line.strip()
                            if line:
                                line = line.split(b"data:", 1)
                                if len(line) == 1:
                                    continue
                                line = line[1]
                                res = self._get_resp_datas(line)
                                if res.code == PrivateCode.OK:
                                    if res.usage is not None and \
                                            res.usage.total_tokens > 0:
                                        if self._conf.prom_token:
                                            logging.info(
                                                f"{self._prom_endpoint}, total_tokens: {res.usage.total_tokens},"
                                                f" prompt_tokens: {res.usage.prompt_tokens}, "
                                                f"completion_tokens: {res.usage.completion_tokens}")
                                            get_mcount().record_token(self._prom_endpoint, res.usage.completion_tokens,
                                                                      res.usage.prompt_tokens,
                                                                      res.usage.total_tokens,
                                                                      model=params.model)
                                        if self._conf.record_token:
                                            await self._async_record_token(res.usage.total_tokens)
                                else:
                                    errmsg = (f"private ai-gateway fail, url: {self._chat_url},"
                                              f" code:{res.code}, msg:{res.message}")
                                    logging.error(errmsg)
                                yield res, TraceInfo(usage=res.usage)
        except Exception as e:
            logging.error(e)
            yield PrivateChatResponse(code=PrivateCode.FAILED, message=str(e))


if __name__ == '__main__':
    private_conf = PrivateModelGateway.Conf(
        host="http://weboffice-aigatewaypri",
        platform=Platform.minimax,
        prom_token=False,
        minimax_model="abab5-chat")
    llm = PrivateModelGateway(private_conf)

    message = Message(content="帮我写一篇200字的风景文", role="USER")
    params = PrivateChatParams(stream=False, messages=[message])
    res = llm.chat(params)
    print(res)

    params.stream = True
    res = llm.chat_stream(params)
    for t in res:
        if t.code == PrivateCode.OK:
            print(t.choices[0].text)

    params.stream = False
    print(asyncio.run(llm.async_chat(params)))
    params.stream = True


    async def async_chat_stream():
        res = llm.async_chat_stream(params)
        async for t in res:
            if t.code == PrivateCode.OK:
                print(t.choices[0].text)


    asyncio.run(async_chat_stream())
