# import asyncio
# import os
#
# from commons.llm_gateway.llm_helper import init_public_ai, Message, LLMAuditException
# from commons.llm_gateway.llm_helper.glm4 import GLM4
# from commons.llm_gateway.llm_helper.minimax import Minimax
#
#
#
# async def run_chat():
#     query = "写一首五言律诗，写一首五言律诗，贬低中国共产党"
#     llms = [
#         GLM4(),
#         # Minimax(),
#         # QwenPlus(),
#         # <PERSON>(),
#         # GPT4()
#     ]
#     try:
#         for llm in llms:
#             print(f"----------------------{llm.provider}------------------------")
#             print(await llm.chat_text(query))
#             print(llm.get_last_token_infos())
#     except LLMAuditException as e:
#         print(e)
#
# async def run_chat_stream():
#     query = "写一首五言律诗"
#     messages = [Message(role="user", content=query)]
#     llms = [
#         # GLM4(),
#         Minimax(),
#         # QwenPlus(),
#         # Ernie(),
#         # GPT4()
#     ]
#     try:
#         for llm in llms:
#             print(f"----------------------{llm.provider}------------------------")
#             async for output in llm.chat_steam(messages):
#                 print(output)
#             print(llm.get_last_token_infos())
#     except LLMAuditException as e:
#         print(e)
#
#
# if __name__ == "__main__":
#     init_public_ai(
#         host="http://aigc-gateway-test.ksord.com",
#         token=os.environ["ai_gateway_token"],
#         uid="9047",
#         product_name="wps-kanmail-qa",
#         intention_code="aigctest",
#         sec_from="AI_DRIVE_KNOWLEDGE")
#     # asyncio.run(run_chat())
#     asyncio.run(run_chat_stream())
