import logging
from typing import List, Dict, AsyncGenerator, Any, Optional

from commons.llm_gateway.llm import LLModelRpc
from commons.llm_gateway.llm_helper import PublicLLM
from commons.llm_gateway.models.chat_data import Message
from commons.llm_gateway.models.public_model_gateway import ERROR_AUDIT_ARR


class Minimax(PublicLLM):
    provider = "minimax"
    model = "abab6.5s-chat"

    def __init__(self):
        super().__init__(Minimax.provider, Minimax.model)
        self._context = "WPSAI是金山办公与合作伙伴共同开发的AI工作助理,WPSAI能够理解自然语言并生成对应的回复,回复思路清晰,逻辑严密,推理精确。"
        self._bot_name = "WPSAI"

    def set_context(self, context: str):
        self._context = context

    def set_botname(self, botname: str):
        self._bot_name = botname

    async def _call(self, stream: bool, messages: List[Message], functions: List[Dict] = None):
        for msg in messages:
            if not msg.role:
                msg.role = "user"

            if not msg.name:
                if msg.role == "assistant" or msg.role == "tool" or msg.role == "function":
                    msg.name = self._bot_name
                else:
                    msg.name = msg.role

        if messages[0].role == "system":
            context = messages[0].content
            messages = messages[1:]
        else:
            context = self._context

        args = LLModelRpc.LLMArgs(
            temperature=self._temperature, max_tokens=self._max_tokens, stop=self._stop, context=context)
        args.extended_llm_arguments = {
            "mask_sensitive_info": False,
            "bot_setting": [{
                "bot_name": self._bot_name,
                "content": context
            }],
            "reply_constraints": {
                "sender_type": "BOT",
                "sender_name": self._bot_name
            }
        }

        if functions:
            functions = [{
                'type': 'function',
                'function': item
            } for item in functions]
            args.tool_choice = "auto"
            args.tools = functions

        selector = LLModelRpc.ModelSelector(provider=self.provider, model=self.model)
        res = await LLModelRpc().async_chat(stream, LLModelRpc.Gateway.Public, messages, selector, args)
        return res

    async def chat(self, messages: List[Message], functions: Optional[List[Dict]] = None) -> str | Dict:
        res = await self._call(False, messages, functions)
        if res.code in ERROR_AUDIT_ARR:
            raise LLMAuditException(res.code)

        if res.usage:
            self._token_infos = {
                "completion_tokens": res.usage.completion_tokens,
                "prompt_tokens": res.usage.prompt_tokens,
                "total_tokens": res.usage.total_tokens,
            }
        tool_calls = res.choices[0].tool_calls
        return {"tool_calls": tool_calls} if tool_calls else res.choices[0].text

    async def chat_steam(self,
                         messages: List[Message],
                         functions: Optional[List[Dict]] = None
                         ) -> AsyncGenerator[str, Any] | AsyncGenerator[Dict, Any]:
        res = await self._call(True, messages, functions)
        completion_tokens = 0
        prompt_tokens = 0
        total_tokens = 0
        async for output in res:
            # print(output)
            if output.code in ERROR_AUDIT_ARR:
                raise LLMAuditException(output.code)

            tool_calls = output.choices[0].tool_calls
            if tool_calls and output.choices[0].finish_reason != "stop":
                pass
            else:
                yield {"tool_calls": tool_calls} if tool_calls else output.choices[0].text
            if output.usage:
                completion_tokens += output.usage.completion_tokens
                if prompt_tokens == 0:
                    prompt_tokens = output.usage.prompt_tokens

        self._token_infos = {
            "completion_tokens": completion_tokens,
            "prompt_tokens": prompt_tokens,
            "total_tokens": completion_tokens + prompt_tokens,
        }
