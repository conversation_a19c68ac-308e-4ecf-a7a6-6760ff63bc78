import logging
from typing import List, Dict, Optional, AsyncGenerator, Any

from wpsai_insight_common.llm_gateway.llm import LLModelRpc
from wpsai_insight_common.llm_gateway.models.chat_data import Message
from wpsai_insight_common.llm_gateway.models.public_model_gateway import PublicModelGateway, ERROR_AUDIT_ARR


class LLMAuditException(Exception):
    def __init__(self, message: str):
        self.message = message

    def __str__(self):
        return self.message


def init_public_ai(
        host: str,
        token: str,
        uid: str,
        product_name: str,
        intention_code: str,
        sec_scene: Optional[str] = None,
        sec_from: Optional[str] = None,
        prom_token: bool = False,
        pool_max: Optional[int] = None):
    pub_conf = PublicModelGateway.Conf(
        host=host,
        token=token,
        uid=uid,
        product_name=product_name,
        intention_code=intention_code,
        sec_scene=sec_scene,
        sec_from=sec_from,
        prom_token=prom_token)
    if pool_max is None:
        pool_max = -1
    LLModelRpc().create_models(pub_conf, pool_max=pool_max)


class PublicLLM(object):
    def __init__(self, provider: str, model: str, version: str = ""):
        self._model = LLModelRpc.ModelSelector(provider=provider, model=model, version=version)
        self._temperature = 0.01
        self._max_tokens = 2000
        self._stop: Optional[List[str]] = None
        self._token_infos = {
            "completion_tokens": 0,
            "prompt_tokens": 0,
            "total_tokens": 0,
        }

    def set_temperature(self, temperature: float):
        self._temperature = temperature

    def set_max_tokens(self, max_tokens: int):
        self._max_tokens = max_tokens

    def set_stop(self, stop: List[str]):
        self._stop = stop

    def get_last_token_infos(self) -> dict:
        return self._token_infos

    async def chat(self, messages: List[Message], functions: Optional[List[Dict]] = None) -> str | Dict:
        args = LLModelRpc.LLMArgs(temperature=self._temperature, max_tokens=self._max_tokens, stop=self._stop)
        if functions:
            functions = [{'type': 'function', 'function': item} for item in functions]
            args.extended_llm_arguments = {"tools": functions}

        res = await LLModelRpc().async_chat(False, LLModelRpc.Gateway.Public, messages, self._model, args)
        if res.code in ERROR_AUDIT_ARR:
            raise LLMAuditException(res.code)

        if res.usage:
            self._token_infos = {
                "completion_tokens": res.usage.completion_tokens,
                "prompt_tokens": res.usage.prompt_tokens,
                "total_tokens": res.usage.total_tokens,
            }
        if res.choices[0].finish_reason in ["tool_calls", "function_call"]:
            return res.extended_resp_fields
        else:
            return res.choices[0].text


    async def chat_steam(self,
                         messages: List[Message],
                         functions: Optional[List[Dict]] = None
                         ) -> AsyncGenerator[str, Any] | AsyncGenerator[Dict, Any]:

        args = LLModelRpc.LLMArgs(temperature=self._temperature, max_tokens=self._max_tokens, stop=self._stop)
        if functions:
            functions = [{'type': 'function', 'function': item} for item in functions]
            args.extended_llm_arguments = {"tools": functions}

        res = await LLModelRpc().async_chat(True, LLModelRpc.Gateway.Public, messages, self._model, args)
        completion_tokens = 0
        prompt_tokens = 0
        total_tokens = 0
        async for output in res:
            if output.code in ERROR_AUDIT_ARR:
                raise LLMAuditException(output.code)

            if output.choices[0].finish_reason in ["tool_calls", "function_call"]:
                yield output.extended_resp_fields
            else:
                yield output.choices[0].text
            if output.usage:
                completion_tokens += output.usage.completion_tokens
                if prompt_tokens == 0:
                    prompt_tokens = output.usage.prompt_tokens

        self._token_infos = {
            "completion_tokens": completion_tokens,
            "prompt_tokens": prompt_tokens,
            "total_tokens": completion_tokens + prompt_tokens,
        }

    async def chat_text(self, text: str, stream: bool = False) -> str | AsyncGenerator[str, Any]:
        messages = [Message(role="user", content=text)]
        if stream:
            return self.chat_steam(messages)
        else:
            return await self.chat(messages)
