from typing import Optional
import re

from modules.llm.summary_generate import extract_sections
from modules.llm.chat_api import LMModel
from modules.llm.prompts import SUMMARY_PROMPT
from commons.tools.utils import error_trace
from services.datamodel import BaseModel, RespBaseModel
from routers.httpcode import HTTPCODE
from modules.llm.prompts import SUMMARY_PROMPT_WITHOUT_KEY


class ReqSummary(BaseModel):
    content: str


class RespSummaryData(BaseModel):
    text_summary: str
    high_level_key: str = ""


class RespSummary(RespBaseModel):
    data: Optional[RespSummaryData] = None


async def summary_services(r: ReqSummary):
    try:
        if r.content is None or len(r.content) == 0:
            return RespSummary(code=HTTPCODE.OK, data=RespSummaryData(text_summary=""))
        extracted_content = extract_sections(r.content, section_length=300)
        status, res = await LMModel.generate_response(
            SUMMARY_PROMPT_WITHOUT_KEY.format(input_text=extracted_content[:5000]), top_k=0.99)
        # # 正则匹配全文摘要和摘要
        # logging.info(f"summary response: {res}")
        # summary_pattern = r"(?:全文摘要|摘要)：(.*?)(?:(?:high-level key|high-level Key)：)"
        # summary_match = re.search(summary_pattern, res, re.DOTALL | re.IGNORECASE)
        #
        # # 正则匹配高层次关键字
        # keys_pattern = r"(?:high-level key|high-level Key)：(.*)"
        # keys_match = re.search(keys_pattern, res, re.IGNORECASE)

        text_summary = res
        # high_level_keys =  "" #此字段目前没用到，暂时为空
        return RespSummary(code=HTTPCODE.OK,
                           data=RespSummaryData(text_summary=text_summary))

    except Exception as e:
        error_trace()
        return RespSummary(code=HTTPCODE.ERROR)
