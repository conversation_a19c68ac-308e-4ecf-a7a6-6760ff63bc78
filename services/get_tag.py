import logging

from modules.llm.chat_api import LMModel, GET_TAGS_LORA
from commons.llm_gateway.llm import Message, LLModelRpc, LLMChatStatus
from modules.llm.prompts import PROMPT_CONTENT_GEN_TAGS_SFT
from typing import List


async def get_tag_services(content: str) -> List[str]:
    try:
        if content is None or len(content) == 0:
            return []
        messages = [
            Message(role='system', content="You are Qwen, created by Alibaba Cloud. You are a helpful assistant."),
            Message(role='user', content=PROMPT_CONTENT_GEN_TAGS_SFT.format(content[:5000]))]
        guided_json = {
            "type": "object",
            "properties": {
                "types": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "maxItems": 3
                },
                "keywords": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "maxItems": 5
                }
            }
        }
        t = 0.0
        selector = LLModelRpc.ModelSelector(model=GET_TAGS_LORA)
        status, text = await LMModel.generate_response(messages=messages, temperature=t, selector=selector, guided_json=guided_json)
        if status == LLMChatStatus.OK:
            try:
                text = eval(text)
                tags = text.get('keywords', []) + text.get('types', [])
            except Exception as e:
                logging.error(f"get_tag_service error: {e}, text: {text}")
                return []
            tags = [tag for tag in list(set(tags)) if tag]
            return tags
        else:
            return []
    except Exception as e:
        logging.error(e)
        return []
