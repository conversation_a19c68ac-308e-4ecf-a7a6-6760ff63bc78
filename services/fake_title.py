import logging

from modules.llm.chat_api import LMModel, GET_TITLE_LORA
from commons.llm_gateway.llm import Message, LLModelRpc, LLMChatStatus
from modules.llm.prompts import PROMPT_CONTENT_GEN_TITLE_SFT


async def fake_title_services(content: str) -> str:
    try:
        if content is None or len(content) == 0:
            return ""
        messages = [
            Message(role='system', content="You are Qwen, created by Alibaba Cloud. You are a helpful assistant."),
            Message(role='user', content=PROMPT_CONTENT_GEN_TITLE_SFT.format(content[:5000]))]
        status, text = await LMModel.generate_response(messages=messages, temperature=0.0, selector=LLModelRpc.ModelSelector(model=GET_TITLE_LORA))
        if status == LLMChatStatus.OK:
            return text.strip('"')
        else:
            return ""
    except Exception as e:
        logging.error(e)
        return ""
