# coding: utf-8
# <AUTHOR> linqi
# @date    : 2025/4/25 17:44

from pydantic import BaseModel
from typing import Optional, List
from enum import Enum
from fastapi import UploadFile

from modules.entity.parse_entity import RespGeneralParseData, RespMergeTableData
from modules.entity.version_entity import RespVersionData
from modules.entity.dst_entity import DST

# from modules.entity.pipeline_entity import PipeLineResult


class StoreType(str, Enum):
    Ks3 = "ks3"
    Minio = "minio"


class FileType(str, Enum):
    DOC = "doc"
    DOCX = "docx"
    PDF = "pdf"
    OTL = "otl"
    TXT = "txt"
    PPT = "ppt"
    PPTX = "pptx"
    XLSX = "xlsx"
    XLS = "xls"
    PNG = "png"
    JPG = "jpg"
    JPEG = "jpeg"
    WEBP = "webp"

def file_is_image(file_type: FileType) -> bool:
    return file_type in [FileType.PNG, FileType.JPG, FileType.JPEG, FileType.WEBP]


class ParseFormat(str, Enum):
    plain = "plain"
    insight_ai = "insight_ai"
    insight_ai_tree = "insight_ai_tree"


class ParseTarget(str, Enum):
    chunk = "chunk"
    fake_title = "fake_title"
    keywords = "keywords"
    summary = "summary"
    screenshot = "screenshot"
    img_desc = "img_desc"


class SpliterModelType(str, Enum):
    semantic = "semantic"
    mark = "mark"
    semantic_jsonl = "semantic_jsonl"


class ChunkMeta(BaseModel):
    model_type: SpliterModelType = SpliterModelType.semantic
    max_chunks: int = 1000
    format: ParseFormat = ParseFormat.plain
    chunk_size: int = 1024
    min_chunk_size: int = 512
    chunk_overlap: int = 0


class ParseTargetMeta(BaseModel):
    chunk: Optional[ChunkMeta] = None


class ReqGeneralParseType(str, Enum):
    normal = "normal"
    background = "background"
    queue = "queue"


class GeneralParseReqLevel(str, Enum):
    low = "low"
    normal = "normal"
    high = "high"


class ReqGeneralParse(BaseModel):
    # 文件下载链接
    file_url: Optional[str] = None
    # 文件
    file_io: Optional[UploadFile] = None
    kdc_ks3_url: Optional[str] = None
    fileinfo: Optional[dict] = None
    # 文件id
    wps_company_id: Optional[str] = "41000207"
    wps_group_id: Optional[str] = None
    wps_drive_id: Optional[str] = None
    wps_v7_file_id: Optional[str] = None
    wps_v5_file_id: Optional[str] = None
    download_id: Optional[str] = None
    return_ks3_url: bool = False
    use_external_link: bool = False
    use_ocr_pipeline: bool = False
    embed_enabled: bool = True
    need_callback: bool = False
    convert_options: Optional[dict] = None
    callback_url: Optional[str] = None

    ocr_only: bool = True

    file_name: str
    file_type: FileType = FileType.DOCX
    parse_res_type: ParseFormat = ParseFormat.plain

    use_layout: bool = False
    page_start: Optional[int] = None
    page_end: Optional[int] = None
    parse_target: Optional[List[ParseTarget]] = None
    parse_target_meta: Optional[ParseTargetMeta] = None
    page_count: Optional[int] = None
    word_count: Optional[int] = None

    # 是否异步
    req_type: ReqGeneralParseType = ReqGeneralParseType.normal
    req_level: GeneralParseReqLevel = GeneralParseReqLevel.normal

    # 表格合并版本
    cross_table_merge_version: Optional[str] = "v1"


class ReqGeneralParseRes(BaseModel):
    token: str
    return_ks3_url: bool = False
    use_external_link: bool = False
    fileinfo: Optional[dict] = None
    parse_target: List[ParseTarget] = None


class RespBaseModel(BaseModel):
    code: int
    message: str = ""


# class RespPipeline(RespBaseModel):
#     data: Optional[PipeLineResult] = None


class RespGeneralParseRes(RespBaseModel):
    data: Optional[RespGeneralParseData] = None


class RespMergeTableRes(RespBaseModel):
    data: Optional[RespMergeTableData] = None


class RespVersionRes(RespBaseModel):
    data: Optional[RespVersionData] = None


class ReqMergeTable(BaseModel):
    content: List[str] = []

class ReqMergeTableV2(BaseModel):
    dsts: List[DST] = []