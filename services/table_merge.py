import logging

from commons.tools.utils import error_trace
from commons.trace.tracer import async_trace_span


from modules.cross_table.merge_table_util import merge_table_by_force
from modules.cross_table.cross_page_table_detector import merge_table_by_force_v2
from modules.entity.parse_entity import RespMergeTableData
from routers.httpcode import HTTPCODE
from services.datamodel import ReqMergeTable, RespMergeTableRes, ReqMergeTableV2


@async_trace_span
async def merge_table(r: ReqMergeTable):
    try:
        # Default to fail if the status is not found or expired
        if r.content is None or len(r.content) < 2:
            return RespMergeTableRes(code=HTTPCODE.ERROR, message="Invalid content provided for merging.")
        logging.debug(f"Merging table with content: {r.content}")
        merged_content = merge_table_by_force(r.content)
        data = RespMergeTableData(content=[merged_content])
        return RespMergeTableRes(code=HTTPCODE.OK, data=data)
    except Exception as e:
        error_trace()
        return RespMergeTableRes(code=HTTPCODE.ERROR)


@async_trace_span
async def merge_table_v2(r: ReqMergeTableV2):
    try:
        # Default to fail if the status is not found or expired
        if r.dsts is None or len(r.dsts) < 2:
            return RespMergeTableRes(code=HTTPCODE.ERROR, message="Invalid dsts provided for merging.")
        # 还原成merge_results的格式
        merge_results = {
            "content": [],
            "table_info": [],
            "merged_type": []
        }
        for dst in r.dsts:
            merge_results["content"].append(dst.content[0])
            merge_results["table_info"].append(dst.table_info)
            merge_results["merged_type"].append(dst.merged_type)
        merged_content = merge_table_by_force_v2(merge_results)
        data = RespMergeTableData(content=[merged_content])
        return RespMergeTableRes(code=HTTPCODE.OK, data=data)
    except Exception as e:
        error_trace()
        return RespMergeTableRes(code=HTTPCODE.ERROR)

