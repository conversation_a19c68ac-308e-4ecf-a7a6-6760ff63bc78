import asyncio
import re
from typing import Dict, Any
from tqdm import tqdm
import os
# import jieba_fast as jieba
import math
import logging

from commons.llm_gateway.models.chat_data import Message
from commons.llm_gateway.llm import LLModelRpc, LLMChatStatus
from commons.llm_gateway.models.public_model_gateway import PublicModelGateway
from commons.llm_gateway.models.sft_model_gateway import SftModelGateway
from modules.llm.prompts import SUMMARY_PROMPT
from modules.llm.chat_api import LMModel

def split_text_into_sentences(text):
    """使用 jieba 对文本进行分句"""
    # 使用 jieba 将文本按句子分割
    # sentences = list(jieba.cut(text, cut_all=False))

    # 通过常见的句号、问号、叹号进行进一步分句
    sentences = re.split(r'(。|！|？|!|\?)', text)

    # 保证标点符号也保留在每个句子中
    sentences = [sent + punct for sent, punct in zip(sentences[0::2], sentences[1::2])]
    return sentences

def extract_text_sections(text, section_length=300):
    """从文本中提取前、中、后的300字左右内容，保持句子完整，并确保三段之间没有重复"""
    # 使用 jieba 分句
    sentences = split_text_into_sentences(text)

    # 计算总字数
    total_length = sum(len(sent) for sent in sentences)

    # 目标字数为总长度的三等分
    section_target_lengths = [total_length // 3, (total_length * 2) // 3, total_length]

    # 定义区段起始索引
    section_starts = [0]
    cumulative_length = 0

    for i, sentence in enumerate(sentences):
        cumulative_length += len(sentence)
        # 查找最接近1/3和2/3的点，记录该句子的索引
        if cumulative_length >= section_target_lengths[len(section_starts) - 1]:
            section_starts.append(i)
            if len(section_starts) == 3:  # 找到三分位置后退出
                break

    section_starts = sorted(list(set(section_starts)))

    print("区段起始句子索引:", section_starts, "总长度：", len(sentences))

    sections = []
    last_end = 0  # 记录上一段结束的位置
    for start in section_starts:
        current_length = 0
        section_sentences = []

        # 确保从上一段结束的位置开始
        start = max(start, last_end)

        # 从start位置开始，逐句添加，直到满足300字左右的条件
        for i in range(start, len(sentences)):
            current_length += len(sentences[i])
            section_sentences.append(sentences[i])
            if current_length >= section_length:
                last_end = i + 1  # 更新结束位置，避免重叠
                break

        sections.append("".join(section_sentences))

    # 合并提取的前、中、后区段
    extracted_content = "\n".join(sections)

    return extracted_content


def split_sentences(text):
    # 综合正则表达式：匹配句子的结尾（中文或英文标点符号），并且确保下一个字符是大写字母（英文句子）或中文字符（中文句子）
    sentence_pattern = r'(?<!\w\.\w.)(?<![A-Z]\.)(?<![A-Z][a-z]\.)(?<! [a-z]\.)(?<![A-Z][a-z][a-z]\.)(?<=[。！？!?.])(?=\s*([\d]+\.\s*)?[A-Z]|[\u4e00-\u9fff])'

    # 使用 finditer 找到所有符合模式的位置
    matches = list(re.finditer(sentence_pattern, text))

    sentences = []
    start = 0

    for match in matches:
        end = match.end()
        sentence = text[start:end].strip()
        if sentence:
            sentences.append(sentence)
        start = match.start()

    # 添加最后一句话
    last_sentence = text[start:].strip()
    if last_sentence:
        sentences.append(last_sentence)

    return sentences


def extract_sections(text, section_length=300):
    # use_idx = 0
    # for i, paragraph in enumerate(text):
    #     if "References" in paragraph or "REFERENCES" in paragraph:
    #         use_idx = i
    #         break
    # sentences = text[:use_idx]
    sentences = split_sentences(text)
    total_length = sum(len(sent) for sent in sentences)

    # 目标字数为总长度的三等分
    section_target_lengths = [total_length // 3, (total_length * 2) // 3, total_length]

    # 定义区段起始索引
    section_starts = [0]
    cumulative_length = 0

    for i, sentence in enumerate(sentences):
        cumulative_length += len(sentence)
        # 查找最接近1/3和2/3的点，记录该句子的索引
        if cumulative_length >= section_target_lengths[len(section_starts) - 1]:
            section_starts.append(i)
            if len(section_starts) == 3:  # 找到三分位置后退出
                break
    section_starts = sorted(list(set(section_starts)))
    logging.info(f"区段起始句子索引：{section_starts}, 总长度：{len(sentences)}")
    sections = []
    last_end = 0  # 记录上一段结束的位置
    for start in section_starts:
        current_length = 0
        section_sentences = []

        # 确保从上一段结束的位置开始
        start = max(start, last_end)
        # 从start位置开始，逐句添加，直到满足300字左右的条件
        for i in range(start, len(sentences)):
            current_length += len(sentences[i])
            section_sentences.append(sentences[i].strip())
            if current_length >= section_length:
                last_end = i + 1  # 更新结束位置，避免重叠
                break

        sections.append(" ".join(section_sentences))

    # 合并提取的前、中、后区段
    extracted_content = "\n".join(sections)
    # print(len(sections[0]), len(sections[1]), len(sections[2]))
    logging.debug(f'切分后的文本示例：{extracted_content[0:3]}******{extracted_content[-3:]}')
    return extracted_content


if __name__ == "__main__":
    # 依赖初始化
    pub_conf = PublicModelGateway.Conf(
        host="http://aigc-gateway-test.ksord.com",
        token="YOUR_TOKEN",
        uid="9047",
        product_name="wps-kanmail-qa",
        intention_code="aigctest",
        provider="minimax-zone",
        model="abab6.5s-chat-wps",
        version="",
        multimodal_provider="ali",
        multimodal_model="qwen-vl-max-0809",
        multimodal_version=None,
        sec_from="AI_DRIVE_KNOWLEDGE",
    )
    sft_conf = SftModelGateway.Conf(
        host="http://privatization-model.kna.wps.cn",
        multimodal_host="http://privatization-model.kna.wps.cn/vl_7b",
        token="YOUR_TOKEN"
    )
    LLModelRpc().create_models(pub_conf, sft_conf, None, pool_max=10)


    async def _main():
        LMModel.gateway = LLModelRpc.Gateway.Public
        LMModel.selector = LLModelRpc.ModelSelector(model="abab6.5s-chat-wps", provider="minimax-zone", version="")

        directory = './test_demo'
        for filename in tqdm(os.listdir(directory)):
            filepath = os.path.join(directory, filename)
            with open(filepath, 'r', encoding='utf-8') as file:
                text = file.read()

            # 提取前300字左右、中间300字左右、后300字左右的文本
            extracted_content = extract_text_sections(text, section_length=300)
            print("文件名：", filename)

            status, result = await LMModel.generate_response(SUMMARY_PROMPT.format(input_text=extracted_content))

            # 正则匹配全文摘要和摘要
            summary_pattern = r"(?:全文摘要|摘要)：(.*?)(?:(?:high-level key|high-level Key)：)"
            summary_match = re.search(summary_pattern, result, re.DOTALL | re.IGNORECASE)

            # 正则匹配高层次关键字
            keys_pattern = r"(?:high-level key|high-level Key)：(.*)"
            keys_match = re.search(keys_pattern, result, re.IGNORECASE)

            # 输出摘要和高层次关键字
            if summary_match:
                summary = summary_match.group(1).strip()
                print("全文摘要:", summary)
            print("\n")
            if keys_match:
                high_level_keys = keys_match.group(1).strip()
                print("高层次关键字:", high_level_keys)
            print("#############################")

    loop = asyncio.get_event_loop()
    loop.run_until_complete(_main())



        
        