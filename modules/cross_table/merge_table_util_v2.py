from bs4 import BeautifulSoup, Tag
from typing import List
import pandas as pd
from io import StringIO
import logging

def table_html2df(table_html: str) -> pd.DataFrame:
    try:
        # 判空或非字符串类型直接返回空 DataFrame
        if not isinstance(table_html, str) or not table_html.strip():
            return pd.DataFrame()

        # 检查是否包含 <table> 标签
        if "<table" not in table_html.lower():
            return pd.DataFrame()
        df_table = pd.read_html(StringIO(table_html), keep_default_na=False, na_values=[""])[0]
        df_table = df_table.fillna("")
        return df_table
    except Exception as e:
        logging.error(f"table html解析失败: {str(e)}")
        return pd.DataFrame()

def split_sub_table_by_html(html: str) -> (bool, list[list[str]]):
    """
    将给定 HTML 表格字符串按“全宽”行分割成子表格。
    规则：当某一行的 colspan 之和等于该表的最大列数时，
    认为该行是子表格的第一行，从该行开始到下一个此类行之前（不含下一标记行）为一个子表格。

    Args:
        html: 含 <table>...</table> 的 HTML 字符串

    Returns:
        subtables: 子表格 HTML 字符串列表，每项为 "<table>...</table>"
    """
    soup = BeautifulSoup(html, "html.parser")
    table = soup.find("table")
    if table is None:
        return False, []

    rows = table.find_all("tr")
    if not rows:
        return False, []

    # 计算每行的 colspan 总和
    def row_colspan_sum(row):
        total = 0
        for cell in row.find_all(['td', 'th']):
            colspan = cell.get('colspan')
            total += int(colspan) if colspan and colspan.isdigit() else 1
        return total
    
    # 判断某行是否“单元格数为1且colspan等于max_cols”
    def is_fullwidth_singlecell(row, max_cols) -> bool:
        cells = row.find_all(['td', 'th'])
        if len(cells) != 1:
            return False
        colspan = cells[0].get('colspan')
        try:
            return int(colspan) == max_cols
        except (TypeError, ValueError):
            return False
        
    # 找到最大列数（按行 colspan 和）
    max_cols = max(row_colspan_sum(r) for r in rows)

    subtables = []
    current_rows = []

    for row in rows:
        if is_fullwidth_singlecell(row, max_cols):

            # 遇到全宽行，开启一个新子表格
            if current_rows:
                subtables.append(current_rows)
            current_rows = [row]
        else:
            # 只有在已开启子表格时，才追加普通行
            current_rows.append(row)
            # 若 current_rows 仍然 None，说明还未遇到第一标记行，跳过
    # 循环结束后，若有剩余 current_rows，加入最后一个子表格
    if current_rows:
        subtables.append(current_rows)

    # 是否含有子表，返回True/False，以及子表列表
    if len(subtables) > 1:
        return True, subtables
    else:
        return False, subtables

def generate_new_html_table(rows: list[str]) -> str:
    """
    将行列表转换为新的 HTML 表格字符串。
    """
    def process_row_html(row):
        if isinstance(row, Tag):
            return row
        elif isinstance(row, str):
            # 解析字符串成 Tag
            tr_tag = BeautifulSoup(row, "html.parser").find("tr")
            return tr_tag if tr_tag else None
        return None
    
    new_soup = BeautifulSoup("<table></table>", "html.parser")
    new_table = new_soup.find("table")
    for row in rows:
        if isinstance(row, list):
            # input: list[list[str,Tag]]
            # 如果是列表，假设每个元素都是 <tr> 的字符串
            for r in row:
                tr_tag = process_row_html(r)
                if tr_tag:
                    new_table.append(tr_tag)
                else:
                    continue  # 跳过无效行
        else:
            # 输入是list[[str,Tag]]
            # row type 不是str，而是 BeautifulSoup 的 Tag 对象
            tr_tag = process_row_html(row)
            if tr_tag:
                new_table.append(tr_tag)
            else:
                continue  # 跳过无效行
        
    return str(new_table)

def cross_page_merge_by_html(pre_table: str, next_table: str, pre_sub_tables:List[str], next_sub_tables:List[str]) -> (bool, list[str]):
    """
    input: potential table pair
    output: 
    merge_flag(bool); 
    final_table(分割合并后的子表格):list[str]
    """
    # pre_sub_tables = split_sub_table_by_html(pre_table)
    # next_sub_tables = split_sub_table_by_html(next_table)

    merge_flag = False
    # 先初始化为两个原始表格，如何满足下面条件，最后会返回一个合并之后的表格
    final_table = [pre_table, next_table]
    # 可能分割为子表格；也有可能不分割为子表格，不分割子表格返回一个原始表格rows，list[list]元素为1，不应该为空
    if pre_sub_tables and next_sub_tables:
        pre_sub_last = pre_sub_tables[-1]
        next_sub_first = next_sub_tables[0]

        pre_sub_last = generate_new_html_table(pre_sub_last)
        next_sub_first = generate_new_html_table(next_sub_first)
        pre_df_last = table_html2df(pre_sub_last)
        next_df_first = table_html2df(next_sub_first)
        if (not pre_df_last.empty) and (not next_df_first.empty):
            # 清洗重复列，还原子表格原始的真实列数
            pre_df_last = pre_df_last.T.drop_duplicates().T
            pre_df_last.columns = range(pre_df_last.shape[1])
            next_df_first = next_df_first.T.drop_duplicates().T
            next_df_first.columns = range(next_df_first.shape[1])

            if pre_df_last.shape[1] == next_df_first.shape[1]:
                if not pre_df_last.iloc[0].equals(next_df_first.iloc[0]):
                    merge_flag = True
                    merge_df = pd.concat([pre_df_last, next_df_first], axis=0).reset_index(drop=True)

                    merge_html = merge_df.to_html(index=False, header=False)#带有<table> tag

                    pre_table_html = generate_new_html_table(pre_sub_tables[:-1])
                    next_table_html = generate_new_html_table(next_sub_tables[1:])

                    final_table = [pre_table_html, merge_html, next_table_html]

    return merge_flag, final_table