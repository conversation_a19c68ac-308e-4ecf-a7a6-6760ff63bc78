from .table_structure_analyzer import TableStructureAnalyzer, TableStructure

from typing import List, Dict, Optional, Tuple, Set, Union
from rapidfuzz import fuzz
import re
from .merge_table_util_v2 import split_sub_table_by_html,  cross_page_merge_by_html, generate_new_html_table
import logging
from bs4 import BeautifulSoup, Tag
import pandas as pd
import asyncio

from commons.llm_gateway.llm import LLModelRpc, LLMChatStatus
from commons.llm_gateway.models.chat_data import SftBaseModelType, chat_api, completion_api, host_map
from commons.llm_gateway.models.public_model_gateway import PublicModelGateway
from commons.llm_gateway.models.sft_model_gateway import SftModelGateway

class CrossPageTableDetector:
    def __init__(self,  structure_analyzer, llm_client=None):
        self.llm_client = llm_client
        self.structure_analyzer = structure_analyzer
        self.merge_type = "not_cross_table"

    def normalize_header(self, header: str) -> str:
        """标准化表头文本"""
        if not header:
            return ""

        # 去除空白字符和特殊符号
        header = re.sub(r'[^\w\u4e00-\u9fff]', '', header)
        header = header.strip().lower()
        return header
        
    def calculate_jaccard_similarity(self, headers1:List[str], headers2:List[str])-> float:
        """方法3: 集合相似度（忽略顺序）"""
        if not headers1 or not headers2:
            return 0.0
        
        # 标准化所有表头
        set1 = set(self.normalize_header(h) for h in headers1)
        set2 = set(self.normalize_header(h) for h in headers2)
        
        # 计算Jaccard相似度
        intersection = len(set1 & set2)
        union = len(set1 | set2)
    
        return intersection / union if union > 0 else 0.0
    def average_best_match_similarity(self, list1: list[str], list2: list[str]) -> float:
        """
        计算两个 Header 列表的平均最佳匹配相似度。
        对于 list1 中的每个元素，在 list2 中找到最相似的（基于编辑距离），
        然后对所有这些最佳匹配的相似度得分求平均。
        计算每两个关键词str的分数时，加入基于距离差的惩罚
        """
        list1 = [self.normalize_header(h) for h in list1]
        list2 = [self.normalize_header(h) for h in list2]
        total_similarity = 0.0
        
        if not list1:
            return 1.0 if not list2 else 0.0

        for item1 in list1:
            # 找到 list2 中与 item1 最匹配的项的相似度得分
            best_match_score = max(fuzz.ratio(item1, item2) for item2 in list2) if list2 else 0
            # print(best_match_score)
            total_similarity += best_match_score
        
        # 返回平均相似度 (0-100之间，除以100归一化到0-1)
        return (total_similarity / len(list1)) / 100.0

    async def cross_page_pipeline(self, table1, table2, sim_threshold=0.9):
        is_sub_table1, subtables1 = split_sub_table_by_html(table1)
        is_sub_table2, subtables2 = split_sub_table_by_html(table2)
        if is_sub_table1 or is_sub_table2:
            merge_flag, final_table = cross_page_merge_by_html(table1, table2, subtables1, subtables2)
            if merge_flag:
                # merge_table = merge_table_by_force(final_table)
                self.merge_type = "sub_tables_merge"
                return True, None, None, "子表合并", self.merge_type
            else:
                self.merge_type = "not_sub_tables_merge"
                return False, None, None, "子表不合并", self.merge_type
        else:
            return await self.is_cross_page_table(table1, table2, sim_threshold)


    async def is_cross_page_table(self, table1, table2, sim_threshold = 0.9):
        # 首先分析表格结构，但不调用LLM获取表头信息
        table1_structure = self.structure_analyzer.analyze_table_structure(table1, use_llm_headers=False)
        table2_structure = self.structure_analyzer.analyze_table_structure(table2, use_llm_headers=False)
        table1_column = table1_structure.column_count
        table2_column = table2_structure.column_count
        
        table1_last_data_row = table1_structure.last_data_row
        table2_first_data_row = table2_structure.first_data_row

        table1_last_data_types = self.structure_analyzer.row_data_type(table1_last_data_row)
        table2_first_data_types = self.structure_analyzer.row_data_type(table2_first_data_row)
      
        if table1_column == table2_column:
            # 并发调用llm识别表头，并获取表头行数和表头内容
            table1_task = self.structure_analyzer.get_table_header(table1, table1_structure)
            table2_task = self.structure_analyzer.get_table_header(table2, table2_structure)
            
            # 使用asyncio.gather并发执行两个任务
            (table1_header_rows, table1_headers), (table2_header_rows, table2_headers) = await asyncio.gather(
                table1_task, table2_task
            )
            table1_structure.llm_header_rows = table1_header_rows
            table2_structure.llm_header_rows = table2_header_rows
            table1_structure.header_rows = table1_header_rows
            table2_structure.header_rows = table2_header_rows
            table1_structure.llm_headers = table1_headers
            table2_structure.llm_headers = table2_headers
            table1_structure.headers = table1_headers
            table2_structure.headers = table2_headers

            if table1_header_rows and not table2_header_rows:
                logging.info("前页表含有表头，下页表不含表头，直接合并")
                info_str = "前页表含有表头，下页表不含表头，直接合并"
                self.merge_type = "tables_wo_header"

                return True, table1_header_rows, table2_header_rows, info_str, self.merge_type
            elif table1_header_rows and table2_header_rows:
                # 都不是空表头
                # 如果多行表头，需要对表头先做flatten处理？然后计算相似度
                # todo: 对多行表头拼接为一个str，计算编辑距离
                # if len(table1_headers) > 1:
                table1_headers = [item for sublist in table1_headers for item in sublist]
                # if len(table2_headers) > 1:
                table2_headers = [item for sublist in table2_headers for item in sublist]

                # 计算表头相似度
                header_similaruty = self.average_best_match_similarity(table1_headers, table2_headers)
                data_types_similarity = self.calculate_jaccard_similarity(table1_last_data_types, table2_first_data_types)
                logging.info("两个表格列数相等且都含有表头，表头相似度为{}，数据类型相似度为{}".format(header_similaruty, data_types_similarity))
                # 重复表头合并
                info_str = "两个表格列数相等且都含有表头，表头相似度为{}，数据类型相似度为{}".format(header_similaruty, data_types_similarity)
                if header_similaruty > sim_threshold and data_types_similarity > sim_threshold:
                    # 去掉重复表头合并
                    logging.info("重复表头合并")
                    info_str += "\n 重复表头合并"
                    self.merge_type = "tables_w_header"
                    return True, table1_header_rows, table2_header_rows, info_str, self.merge_type
                else:
                    # 引入llm进行判断
                    logging.info("表头不匹配或者数据类型不一致，不合并")
                    info_str += "\n 表头不匹配或者数据类型不一致，不合并"
                    return False, table1_header_rows, table2_header_rows, info_str, self.merge_type
            else:
                logging.info("第一个表格不含表头，不合并")
                info_str = "第一个表格不含表头，不合并"
                return False, table1_header_rows, table2_header_rows, info_str, self.merge_type
        else:
            #列数不相等，要合并吗？流水单有解析错误导致列数不相等的问题
            #可以进入llm判断 or 规则计算相似度看看
            # 规则计算表头
            table1_headers = table1_structure.rule_headers
            table2_headers = table2_structure.rule_headers
            table1_header_rows = table1_structure.rule_header_rows
            table2_header_rows = table2_structure.rule_header_rows
            table1_headers = [item for sublist in table1_headers for item in sublist]
            table2_headers = [item for sublist in table2_headers for item in sublist]
            header_similaruty = self.average_best_match_similarity(table1_headers, table2_headers)
            data_types_similarity = self.calculate_jaccard_similarity(table1_last_data_types, table2_first_data_types)
            logging.info("两个表格列数不相等，表头相似度为{}，数据类型相似度为{}".format(header_similaruty, data_types_similarity))
            info_str = "两个表格列数不相等，表头相似度为{}，数据类型相似度为{}".format(header_similaruty, data_types_similarity)
            return False, table1_header_rows, table2_header_rows, info_str, self.merge_type

class TablePairMerge:
    # 区分凯莱英含有子表和普通含有列表头的表格
    def __init__(self, table1:str, table2:str, table1_header_rows:List[int], table2_header_rows:List[int]):
        self.table1 = table1
        self.table2 = table2
        self.table1_header_rows = table1_header_rows
        self.table2_header_rows = table2_header_rows

    def extract_trs(self,table_html: str) -> list[str]:
        """
        从 HTML 表格字符串中提取所有 <tr> 标签片段，返回列表形式的 <tr>...</tr> 字符串。
        会移除外层 <table>、<tbody>、</tbody>、</table> 等，仅保留 <tr> ... </tr>。
        """
        soup = BeautifulSoup(table_html, "html.parser")
        table = soup.find("table")
        if table is None:
            return []

        rows = table.find_all("tr")
        return [row for row in rows]

    def merge_tables(self, merge_type: str) -> Union[str, List[str]]:
        if merge_type == "tables_wo_header":
            return self.merge_tables_wo_duplicate_header(self.table1, self.table2)
        elif merge_type == "tables_w_header":
            return self.merge_tables_w_duplicate_header(self.table1, self.table2, self.table1_header_rows, self.table2_header_rows)
        elif merge_type == "sub_tables_merge":
            return self.merge_table_by_force([self.table1, self.table2])
        else:
            logging.error("merge_type not supported: {}".format(merge_type))
            return [self.table1, self.table2]

    def merge_table_by_force(self, table_list: list[str]) -> str:
        """
        适用于：子表合并
        输入：多个table list，暴力按行拼接，
        输出：一个完整表格，可能html解析异常
        """
        total_rows = []
        for table in table_list:
            table_rows = self.extract_trs(table)
            if table_rows:
                total_rows.append(table_rows)
        total_table_html = generate_new_html_table(total_rows)

        return total_table_html

    def merge_tables_w_duplicate_header(self, table1:str, table2:str, table1_header_rows:List[int], table2_header_rows:List[int]):
        # table2_header_rows = self.table2_structure.header_rows
        extract_trs1 = self.extract_trs(table1)
        extract_trs2 = self.extract_trs(table2)
        new_soup = BeautifulSoup("<table></table>", "html.parser")
        new_table = new_soup.find("table")
        new_table.extend(extract_trs1)
        for idx, row in enumerate(extract_trs2):
            if idx not in table2_header_rows:
                new_table.append(row)
        return str(new_table)

    def merge_tables_wo_duplicate_header(self, table1, table2):
        extract_trs1 = self.extract_trs(table1)
        extract_trs2 = self.extract_trs(table2)
        new_soup = BeautifulSoup("<table></table>", "html.parser")
        new_table = new_soup.find("table")
        new_table.extend(extract_trs1)
        new_table.extend(extract_trs2)
        return str(new_table)


def merge_table_by_force_v2(merged_result):
    """
    输入多个table list，暴力按行拼接，最后完整表格可能异常
    """
    if len(merged_result["content"]) < 1:
        return ""

    merge_table = merged_result["content"][0]

    for idx, content in enumerate(merged_result["content"][1:], 1):
        last_table_info = merged_result["table_info"][idx-1]
        table_info = merged_result["table_info"][idx]
        merged_type = merged_result["merged_type"][idx]
        table_merge = TablePairMerge(merge_table, content, last_table_info, table_info)
        merge_table = table_merge.merge_tables(merged_type)

    return merge_table

if __name__ == "__main__":

    # 示例：如何使用并发的跨页表格检测
    async def example_concurrent_detection():
        """
        示例：展示如何使用并发的跨页表格检测功能
        
        主要改进：
        1. get_table_header方法现在是异步的
        2. 使用asyncio.gather并发执行两个表格的LLM表头识别
        3. 大幅提升性能，两个LLM调用可以并行执行
        """
        from table_structure_analyzer import TableStructureAnalyzer

        # SftBaseModelType, chat_api, completion_api, host_map = load_sft_model_config()
        # pub_conf = PublicModelGateway.Conf(
        #     host="http://ai-gateway.wps.cn",
        #     token="I26OQDZMT3pj2K3IhcRsUhRnlpG7PW4F",  # os.environ("token")
        #     uid="9047",
        #     product_name="wps-kanmail-qa",
        #     intention_code="aigctest",
        #     sec_from="AI_DRIVE_KNOWLEDGE",
        # )
        # sft_conf = SftModelGateway.Conf(
        #     host="http://kmd-api.kas.wps.cn", # 随便str值都可以
        #     kas_host = "http://kmd-api.kas.wps.cn",
        #     # 普通模型和多模态模型共用token、uri
        #     token= "}7B$/(!k:n5Xs$q09|jxt6(&Y4Vx*(@x",
        #     chat_api= chat_api,
        #     completion_api = completion_api,
        #     host_map = host_map,
        #     product_name = "",
        # )
        # LLModelRpc().create_models(pub_conf, sft_conf, None, pool_max=10)
        sft_base_model: LLModelRpc.SftBaseModel = LLModelRpc.SftBaseModel(
            base_model = SftBaseModelType.kas_qwen3_14b,
        )
        selector: LLModelRpc.ModelSelector = LLModelRpc.ModelSelector(
            model=""
        )
        gateway = LLModelRpc.Gateway.Sft
        llm_sft = LLModelRpc()
        
        table_struct_analyzer = TableStructureAnalyzer( llm_sft=llm_sft, gateway=gateway, selector=selector, sft_base_model=sft_base_model)
        # 初始化检测器
        detector = CrossPageTableDetector(table_struct_analyzer)
        
        
        # 示例表格HTML
        table1_html = """<table><tr><td colspan="15" rowspan="1">临床项目及报告单位信息</td></tr><tr><td colspan="2" rowspan="1">医疗机构及专业名称</td><td colspan="7" rowspan="1">上海市第六人民医院呼吸内科</td><td colspan="3" rowspan="1">电话</td><td colspan="3" rowspan="1"></td></tr><tr><td colspan="2" rowspan="1">中报单位名称</td><td colspan="7" rowspan="1">上海市第六人民医院</td><td colspan="3" rowspan="1">电话</td><td colspan="3" rowspan="1"></td></tr><tr><td colspan="2" rowspan="1">临床研究方案名称</td><td colspan="13" rowspan="1">探索人脐带问充质干细胞注射液油性及初步有效性的开放性临床研究</td></tr><tr><td colspan="2" rowspan="1">临床研究方案号</td><td colspan="13" rowspan="1">I</td></tr><tr><td colspan="2" rowspan="1">临床适应症</td><td colspan="13" rowspan="1">质纤雄化</td></tr><tr><td colspan="2" rowspan="1">临床研究分类</td><td colspan="13" rowspan="1"> □I期  図Ⅱ期 □血期    □IV期    □生物等效性试验 □验证类临床试验</td></tr><tr><td colspan="2" rowspan="1">试验盲态情况</td><td colspan="13" rowspan="1">☒非盲态 □盲态( □未破盲 □已破盲一破盲时间：年 月 日)</td></tr><tr><td colspan="15" rowspan="1">报告者信息</td></tr><tr><td colspan="2" rowspan="1">报告者姓名</td><td colspan="7" rowspan="1"></td><td colspan="3" rowspan="1">所在国家</td><td colspan="3" rowspan="1">中国</td></tr><tr><td colspan="2" rowspan="1">职业</td><td colspan="7" rowspan="1">呼吸科医师</td><td colspan="3" rowspan="1">电话</td><td colspan="3" rowspan="1"></td></tr><tr><td colspan="2" rowspan="1">获知SAE时问</td><td colspan="13" rowspan="1"> □首次获知时问__年___月__日☒随访信息获知时问  2024     年   05月23    日19;25</td></tr><tr><td colspan="15" rowspan="1">受试者信息</td></tr><tr><td colspan="1" rowspan="1">姓名缩写</td><td colspan="3" rowspan="1"></td><td colspan="2" rowspan="1">性别</td><td colspan="1" rowspan="1">男</td><td colspan="3" rowspan="1">出生日期</td><td colspan="2" rowspan="1">1965年3月25日</td><td colspan="2" rowspan="1">发生SAE时的年龄</td><td colspan="1" rowspan="1">59</td></tr><tr><td colspan="1" rowspan="1">受试者编号</td><td colspan="3" rowspan="1"></td><td colspan="2" rowspan="1">民族</td><td colspan="1" rowspan="1">汉</td><td colspan="3" rowspan="1">身高(cm)</td><td colspan="2" rowspan="1">170</td><td colspan="2" rowspan="1">体重(kg)</td><td colspan="1" rowspan="1">87</td></tr><tr><td colspan="1" rowspan="2">患者死亡</td><td colspan="14" rowspan="1"> □否</td></tr><tr><td colspan="2" rowspan="1">☒是</td><td colspan="1" rowspan="1">死亡日期</td><td colspan="3" rowspan="1">2024年5月23日</td><td colspan="1" rowspan="1">死亡原因</td><td colspan="3" rowspan="1">急性肝衰竭</td><td colspan="2" rowspan="1">是否尸检</td><td colspan="2" rowspan="1">☒否 □是尸检结果______</td></tr><tr><td colspan="15" rowspan="1">现病史(试验用药适应症以外，SAE发生时未恢复的疾病)</td></tr><tr><td colspan="1" rowspan="1">疾病名称</td><td colspan="4" rowspan="1">开始日期</td><td colspan="3" rowspan="1">结束日期</td><td colspan="3" rowspan="1">是否为家族史</td><td colspan="4" rowspan="1">备注</td></tr><tr><td colspan="1" rowspan="1">脂肪所</td><td colspan="4" rowspan="1">2022-01-17</td><td colspan="3" rowspan="1">/</td><td colspan="3" rowspan="1">NA</td><td colspan="4" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">肝囊肿</td><td colspan="4" rowspan="1">2024-04-03</td><td colspan="3" rowspan="1">/</td><td colspan="3" rowspan="1">NA</td><td colspan="4" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">胆囊肌腺症</td><td colspan="4" rowspan="1">2022-01-13</td><td colspan="3" rowspan="1">/</td><td colspan="3" rowspan="1">NA</td><td colspan="4" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">脾肿大</td><td colspan="4" rowspan="1">2024-04-03</td><td colspan="3" rowspan="1">/</td><td colspan="3" rowspan="1">NA</td><td colspan="4" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">IJ型糖尿病</td><td colspan="4" rowspan="1">2022-01-UK</td><td colspan="3" rowspan="1">/</td><td colspan="3" rowspan="1">NA</td><td colspan="4" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">高血压</td><td colspan="4" rowspan="1">2022-UK-UK</td><td colspan="3" rowspan="1">/</td><td colspan="3" rowspan="1">NA</td><td colspan="4" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">癌胚抗原升高</td><td colspan="4" rowspan="1">2024-03-29</td><td colspan="3" rowspan="1">/</td><td colspan="3" rowspan="1">NA</td><td colspan="4" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">糖类抗原升高</td><td colspan="4" rowspan="1">2024-03-29</td><td colspan="3" rowspan="1">/</td><td colspan="3" rowspan="1">NA</td><td colspan="4" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">神经元特异烯醇化酶升高</td><td colspan="4" rowspan="1">2024-03-29</td><td colspan="3" rowspan="1">/</td><td colspan="3" rowspan="1">NA</td><td colspan="4" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">非小细胞肺癌</td><td colspan="4" rowspan="1">2024-03-29</td><td colspan="3" rowspan="1">/</td><td colspan="3" rowspan="1">NA</td><td colspan="4" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">相关Ag21-1升高</td><td colspan="2" rowspan="1"></td><td colspan="2" rowspan="1"></td><td colspan="2" rowspan="1"></td><td colspan="3" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">肝功能不全</td><td colspan="2" rowspan="1">2022-01-12</td><td colspan="2" rowspan="1">/</td><td colspan="2" rowspan="1">NA</td><td colspan="3" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">高尿酸血症</td><td colspan="2" rowspan="1">2022-01-12</td><td colspan="2" rowspan="1">/</td><td colspan="2" rowspan="1">NA</td><td colspan="3" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">肌陵激酶同工隐升离</td><td colspan="2" rowspan="1">2024-04-03</td><td colspan="2" rowspan="1">1.</td><td colspan="2" rowspan="1">NA</td><td colspan="3" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">乳酸脱氢酶升高</td><td colspan="2" rowspan="1">2024-04-03</td><td colspan="2" rowspan="1">/</td><td colspan="2" rowspan="1">NA</td><td colspan="3" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">尿红细胞升高</td><td colspan="2" rowspan="1">2024-04-03</td><td colspan="2" rowspan="1">2024.04.17</td><td colspan="2" rowspan="1">NA</td><td colspan="3" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">心律失常</td><td colspan="2" rowspan="1">2022-0112</td><td colspan="2" rowspan="1">/</td><td colspan="2" rowspan="1">NA</td><td colspan="3" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">肺曲霉病</td><td colspan="2" rowspan="1">2024-03-29</td><td colspan="2" rowspan="1">/</td><td colspan="2" rowspan="1">NA</td><td colspan="3" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">两下肢动脉硬化班块形成</td><td colspan="2" rowspan="1">2022-01-13</td><td colspan="2" rowspan="1">/</td><td colspan="2" rowspan="1">NA</td><td colspan="3" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">左肺上叶小结节</td><td colspan="2" rowspan="1">2024-03-25</td><td colspan="2" rowspan="1">/</td><td colspan="2" rowspan="1">NA</td><td colspan="3" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">前上纵隔结节</td><td colspan="2" rowspan="1">2024-03-25</td><td colspan="2" rowspan="1">/</td><td colspan="2" rowspan="1">NA</td><td colspan="3" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">右侧第8后肋陈旧性骨折</td><td colspan="2" rowspan="1">2024-03-25</td><td colspan="2" rowspan="1">/</td><td colspan="2" rowspan="1">NA</td><td colspan="3" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">颅内多发缺血灶</td><td colspan="2" rowspan="1">2024-03-30</td><td colspan="2" rowspan="1">/</td><td colspan="2" rowspan="1">N</td><td colspan="3" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">左侧颡枕叶班片陈旧性病史</td><td colspan="2" rowspan="1">2024-03-30</td><td colspan="2" rowspan="1">/</td><td colspan="2" rowspan="1">NA</td><td colspan="3" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">冠心病</td><td colspan="2" rowspan="1">2024-03-27</td><td colspan="2" rowspan="1">/</td><td colspan="2" rowspan="1">NA</td><td colspan="3" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">肺动脉高压中度</td><td colspan="2" rowspan="1">2024-03-29</td><td colspan="2" rowspan="1">/</td><td colspan="2" rowspan="1">NA</td><td colspan="3" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">及下胶水肿</td><td colspan="2" rowspan="1">2024.05.UK</td><td colspan="2" rowspan="1">/</td><td colspan="2" rowspan="1">NA</td><td colspan="3" rowspan="1"></td></tr><tr><td colspan="10" rowspan="1">既往病史(SAE发生前已恢复的疾病)</td></tr><tr><td colspan="1" rowspan="1">族病名称</td><td colspan="2" rowspan="1">开始日期</td><td colspan="2" rowspan="1">结束日期</td><td colspan="2" rowspan="1">是否为家族史</td><td colspan="3" rowspan="1">备注</td></tr><tr><td colspan="1" rowspan="1">肺炎</td><td colspan="2" rowspan="1">2024.04.30</td><td colspan="2" rowspan="1">2024.05.07</td><td colspan="2" rowspan="1">NA</td><td colspan="3" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1"></td><td colspan="2" rowspan="1"></td><td colspan="2" rowspan="1"></td><td colspan="2" rowspan="1"></td><td colspan="3" rowspan="1"></td></tr><tr><td colspan="10" rowspan="1">过敏史：☒无 □有(过敏原：)</td></tr><tr><td colspan="10" rowspan="1">饮酒史： □无☒有2-3两/日，约40余年</td></tr><tr><td colspan="10" rowspan="1">吸烟史： □无☒有2包/日，约40余年</td></tr><tr><td colspan="10" rowspan="1">其他：☒无 □有</td></tr><tr><td colspan="10" rowspan="1">相关实验室检查(如适用，选填)</td></tr><tr><td colspan="1" rowspan="1">检查项</td><td colspan="1" rowspan="1">检查日期</td><td colspan="2" rowspan="1">检查结果</td><td colspan="2" rowspan="1">单位</td><td colspan="2" rowspan="1">正常值下限</td><td colspan="1" rowspan="1">正常值上限</td><td colspan="1" rowspan="1">备注</td></tr><tr><td colspan="1" rowspan="1">透明质酸</td><td colspan="1" rowspan="1">2024.5.19</td><td colspan="2" rowspan="1">2437.16</td><td colspan="2" rowspan="1">Mg/ml</td><td colspan="2" rowspan="1">0</td><td colspan="1" rowspan="1">120</td><td colspan="1" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">层粘连蛋白</td><td colspan="1" rowspan="1">2024.5.19</td><td colspan="2" rowspan="1">652.73</td><td colspan="2" rowspan="1">mg/ml</td><td colspan="2" rowspan="1">0</td><td colspan="1" rowspan="1">130</td><td colspan="1" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">III型前胶原N端肚</td><td colspan="1" rowspan="1">2024.5.19</td><td colspan="2" rowspan="1">96.46</td><td colspan="2" rowspan="1">mg/ml</td><td colspan="2" rowspan="1">0</td><td colspan="1" rowspan="1">15</td><td colspan="1" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">IV型胶原</td><td colspan="1" rowspan="1">2024.5.19</td><td colspan="2" rowspan="1">839</td><td colspan="2" rowspan="1">mg/ml</td><td colspan="2" rowspan="1">0</td><td colspan="1" rowspan="1">95</td><td colspan="1" rowspan="1"></td></tr></table>"""
        table2_html = """<table><tr><td colspan="1" rowspan="1">游离甲状牒素</td><td colspan="1" rowspan="1">2024.5.19</td><td colspan="1" rowspan="1">23.19</td><td colspan="1" rowspan="1">mg/ml</td><td colspan="1" rowspan="1">10</td><td colspan="1" rowspan="1">22</td><td colspan="1" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">三碘甲状隙原氨酸</td><td colspan="1" rowspan="1">2024.5.19</td><td colspan="1" rowspan="1">0.75</td><td colspan="1" rowspan="1">mg/ml</td><td colspan="1" rowspan="1">0.8</td><td colspan="1" rowspan="1">1.9</td><td colspan="1" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">田状隙责</td><td colspan="1" rowspan="1">2024.5.19</td><td colspan="1" rowspan="1">13.34</td><td colspan="1" rowspan="1">mg/ml</td><td colspan="1" rowspan="1">5</td><td colspan="1" rowspan="1">13</td><td colspan="1" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">慈愿杭原</td><td colspan="1" rowspan="1">2024.5.19</td><td colspan="1" rowspan="1">276.02</td><td colspan="1" rowspan="1">mg/ml</td><td colspan="1" rowspan="1">0</td><td colspan="1" rowspan="1">5</td><td colspan="1" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">缔类抗原CA125</td><td colspan="1" rowspan="1">2024.5.19</td><td colspan="1" rowspan="1">341.59</td><td colspan="1" rowspan="1">U/ml</td><td colspan="1" rowspan="1">0</td><td colspan="1" rowspan="1">35</td><td colspan="1" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">缔类抗原CA50</td><td colspan="1" rowspan="1">2024.5.19</td><td colspan="1" rowspan="1">153.47</td><td colspan="1" rowspan="1">U/ml</td><td colspan="1" rowspan="1">0</td><td colspan="1" rowspan="1">25</td><td colspan="1" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">糖类抗原CA153</td><td colspan="1" rowspan="1">2024.5.19</td><td colspan="1" rowspan="1">313.7</td><td colspan="1" rowspan="1">U/ml</td><td colspan="1" rowspan="1">0</td><td colspan="1" rowspan="1">35</td><td colspan="1" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">糖类抗原CA242</td><td colspan="1" rowspan="1">2024.5.19</td><td colspan="1" rowspan="1">27.25</td><td colspan="1" rowspan="1">U/ml</td><td colspan="1" rowspan="1">0</td><td colspan="1" rowspan="1">25</td><td colspan="1" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">筛类抗原199</td><td colspan="1" rowspan="1">2024.5.19</td><td colspan="1" rowspan="1">136.24</td><td colspan="1" rowspan="1">U/ml</td><td colspan="1" rowspan="1">0</td><td colspan="1" rowspan="1">35</td><td colspan="1" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">谷丙转氛酶</td><td colspan="1" rowspan="1">2024.5.19</td><td colspan="1" rowspan="1">153.8</td><td colspan="1" rowspan="1">U/L</td><td colspan="1" rowspan="1">0</td><td colspan="1" rowspan="1">65</td><td colspan="1" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">谷草转氮酶</td><td colspan="1" rowspan="1">2024.5.19</td><td colspan="1" rowspan="1">147.9</td><td colspan="1" rowspan="1">U/L</td><td colspan="1" rowspan="1">0</td><td colspan="1" rowspan="1">60</td><td colspan="1" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">谷草转氛酶同工酶</td><td colspan="1" rowspan="1">2024.5.19</td><td colspan="1" rowspan="1">29.9</td><td colspan="1" rowspan="1">U/L</td><td colspan="1" rowspan="1">0</td><td colspan="1" rowspan="1">18</td><td colspan="1" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">总胆红素</td><td colspan="1" rowspan="1">2024.5.19</td><td colspan="1" rowspan="1">159.5</td><td colspan="1" rowspan="1">Umol/L</td><td colspan="1" rowspan="1">.0</td><td colspan="1" rowspan="1">25</td><td colspan="1" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">直接胆红素</td><td colspan="1" rowspan="1">2024.5.19</td><td colspan="1" rowspan="1">118.43</td><td colspan="1" rowspan="1">Umol/L</td><td colspan="1" rowspan="1">0</td><td colspan="1" rowspan="1">6.84</td><td colspan="1" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">总蛋白</td><td colspan="1" rowspan="1">2024.5.19</td><td colspan="1" rowspan="1">80.5</td><td colspan="1" rowspan="1">8/L</td><td colspan="1" rowspan="1">62</td><td colspan="1" rowspan="1">85</td><td colspan="1" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">白蛋白</td><td colspan="1" rowspan="1">2024.5.19</td><td colspan="1" rowspan="1">27.9</td><td colspan="1" rowspan="1">g/L</td><td colspan="1" rowspan="1">35</td><td colspan="1" rowspan="1">55</td><td colspan="1" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">碱性磷酸酶</td><td colspan="1" rowspan="1">2024.5.19</td><td colspan="1" rowspan="1">330.4</td><td colspan="1" rowspan="1">U/L</td><td colspan="1" rowspan="1">45</td><td colspan="1" rowspan="1">150</td><td colspan="1" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">Y-谷氨酰转肽街</td><td colspan="1" rowspan="1">2024.5.19</td><td colspan="1" rowspan="1">1349.4</td><td colspan="1" rowspan="1">U/L</td><td colspan="1" rowspan="1">11</td><td colspan="1" rowspan="1">49</td><td colspan="1" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">总恩汁酸</td><td colspan="1" rowspan="1">2024.5.19</td><td colspan="1" rowspan="1">93.7</td><td colspan="1" rowspan="1">Umol/L</td><td colspan="1" rowspan="1">0</td><td colspan="1" rowspan="1">12</td><td colspan="1" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">甘胆跋CG</td><td colspan="1" rowspan="1">2024.5.19</td><td colspan="1" rowspan="1">66.49</td><td colspan="1" rowspan="1">Mg/L</td><td colspan="1" rowspan="1">0.4</td><td colspan="1" rowspan="1">2.78</td><td colspan="1" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">谷胱甘肚还原酶</td><td colspan="1" rowspan="1">2024.5.19</td><td colspan="1" rowspan="1">145.6</td><td colspan="1" rowspan="1">U/L</td><td colspan="1" rowspan="1">33</td><td colspan="1" rowspan="1">73</td><td colspan="1" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">葡萄糖空腹</td><td colspan="1" rowspan="1">2024.5.19</td><td colspan="1" rowspan="1">8.93</td><td colspan="1" rowspan="1">Mmol/L</td><td colspan="1" rowspan="1">3.9</td><td colspan="1" rowspan="1">5.8</td><td colspan="1" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">肌酐</td><td colspan="1" rowspan="1">2024.5.19</td><td colspan="1" rowspan="1">146.3</td><td colspan="1" rowspan="1">Umol/L</td><td colspan="1" rowspan="1">17.7</td><td colspan="1" rowspan="1">107</td><td colspan="1" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">尿酸</td><td colspan="1" rowspan="1">2024.5.19</td><td colspan="1" rowspan="1">706.3</td><td colspan="1" rowspan="1">Umol/L</td><td colspan="1" rowspan="1">200</td><td colspan="1" rowspan="1">415</td><td colspan="1" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">钠</td><td colspan="1" rowspan="1">2024.5.19</td><td colspan="1" rowspan="1">131.6</td><td colspan="1" rowspan="1">Mmol/L</td><td colspan="1" rowspan="1">136</td><td colspan="1" rowspan="1">144</td><td colspan="1" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">乳酸脱氢酶</td><td colspan="1" rowspan="1">2024.5.19</td><td colspan="1" rowspan="1">340.6</td><td colspan="1" rowspan="1">U/L</td><td colspan="1" rowspan="1">155</td><td colspan="1" rowspan="1">300</td><td colspan="1" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">肌酸激酶同工酶</td><td colspan="1" rowspan="1">2024.5.19</td><td colspan="1" rowspan="1">140</td><td colspan="1" rowspan="1">U/L</td><td colspan="1" rowspan="1">0</td><td colspan="1" rowspan="1">24</td><td colspan="1" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">降钙素原</td><td colspan="1" rowspan="1">2024.5.19</td><td colspan="1" rowspan="1">1.26</td><td colspan="1" rowspan="1">mg/ml</td><td colspan="1" rowspan="1">0</td><td colspan="1" rowspan="1">0.5</td><td colspan="1" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">血清淀粉样蛋白A</td><td colspan="1" rowspan="1">2024.5.19</td><td colspan="1" rowspan="1">19.6</td><td colspan="1" rowspan="1">Mg/L</td><td colspan="1" rowspan="1">0</td><td colspan="1" rowspan="1">10</td><td colspan="1" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">铁蛋白</td><td colspan="1" rowspan="1">2024.5.19</td><td colspan="1" rowspan="1">1724.7</td><td colspan="1" rowspan="1">mg/ml</td><td colspan="1" rowspan="1">50</td><td colspan="1" rowspan="1">300</td><td colspan="1" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">胰脂肪酶</td><td colspan="1" rowspan="1">2024.5.19</td><td colspan="1" rowspan="1">287.6</td><td colspan="1" rowspan="1">U/L</td><td colspan="1" rowspan="1">5.6</td><td colspan="1" rowspan="1">51.3</td><td colspan="1" rowspan="1"></td></tr><tr><td colspan="1" rowspan="1">a-淀粉酶</td><td colspan="1" rowspan="1">2024.5.19</td><td colspan="1" rowspan="1">277.5</td><td colspan="1" rowspan="1">U/L</td><td colspan="1" rowspan="1">0</td><td colspan="1" rowspan="1">82</td><td colspan="1" rowspan="1"></td></tr></table>"""
        
        # 使用并发检测（这会并发调用两个表格的LLM表头识别）

        is_cross_page, table1_info, table2_info, info_str, merged_type = await detector.cross_page_pipeline(
            table1_html, table2_html, sim_threshold=0.9
        )
        print(is_cross_page, table1_info, table2_info, info_str, merged_type)
        if is_cross_page:
            table_merge = TablePairMerge(table1_html, table2_html, table1_info, table2_info)
            merge_table_1_1 = table_merge.merge_tables(merged_type)


        is_cross_page, table2_2_info, table2_3_info, info_str, merged_type_2_3 = await detector.cross_page_pipeline(
            table2_html, table3_html, sim_threshold=0.9
        )

        if is_cross_page:
            table_merge = TablePairMerge(merge_table_1_1, table3_html, table2_info, table3_info)
            merge_table = table_merge.merge_tables(merged_type)

    # # 运行示例
    asyncio.run(example_concurrent_detection())

