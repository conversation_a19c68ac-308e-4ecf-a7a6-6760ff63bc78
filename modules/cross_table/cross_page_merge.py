import logging
from typing import List

from commons.llm_gateway.llm import LLModelRpc
from commons.llm_gateway.models.chat_data import SftBaseModelType, chat_api, completion_api, host_map
from commons.llm_gateway.models.public_model_gateway import PublicModelGateway
from commons.llm_gateway.models.sft_model_gateway import SftModelGateway

from modules.cross_table.merge_table_util import cross_page_merge_by_html, merge_table_by_force
from modules.cross_table.cross_page_table_detector import merge_table_by_force_v2
from modules.entity.chunk_entity import Chunk
from modules.entity.dst_entity import DSTType
from modules.cross_table.table_structure_analyzer import TableStructureAnalyzer
from modules.cross_table.cross_page_table_detector import CrossPageTableDetector

def classify_and_mark_table_indices(chunks):
    # Step 1: Classify chunks by page number
    page_chunks = {}
    for chunk in chunks:
        page_num = chunk.page_num[0]
        if page_num not in page_chunks:
            page_chunks[page_num] = []
        page_chunks[page_num].append(chunk)

    # Step 2: Mark table indices for each page
    table_indices = {}
    for page_num, chunks_on_page in page_chunks.items():
        first_table_index, last_table_index = -1, -1
        first_table_chunk_id, last_table_chunk_id = None, None
        table_index = 0
        mark_count = 0
        total_dsts = 0
        for i, chunk in enumerate(chunks_on_page):
            total_dsts += len(chunk.dsts)
            for dst in chunk.dsts:
                if dst.mark:
                    mark_count += 1
                    continue
                if dst.dst_type == DSTType.TABLE:  # Check if dst.type is table
                    if first_table_index == -1:
                        first_table_index = table_index
                        first_table_chunk_id = chunk.chunk_id
                    last_table_index = table_index
                    last_table_chunk_id = chunk.chunk_id
                table_index += 1

        # Calculate FromLast and handle cases with no tables
        from_last = total_dsts - last_table_index - mark_count if last_table_index != -1 else -1
        table_indices[page_num] = {
            "first_table_index": first_table_index,
            "first_chunk_id": first_table_chunk_id,
            "from_last_table_index": from_last,
            "last_chunk_id": last_table_chunk_id,
        }

    return table_indices


def process_and_merge_chunks(chunks, table_indices):
    merged_results = []  # Store merged results with chunk IDs
    # merged_chunk_ids = set()  # Track already merged chunk IDs

    # Convert table_indices to a sorted list of (page_num, indices) for sequential processing
    sorted_table_indices = sorted(table_indices.items())

    for i in range(len(sorted_table_indices) - 1):
        current_page, current_indices = sorted_table_indices[i]
        next_page, next_indices = sorted_table_indices[i + 1]

        # Check the condition
        if (
                current_indices["from_last_table_index"] != -1
                and next_indices["first_table_index"] != -1
                and current_indices["from_last_table_index"] + next_indices["first_table_index"] < 5
        ):
            # Get the corresponding chunks
            current_chunk = next(
                chunk for chunk in chunks if chunk.chunk_id == current_indices["last_chunk_id"]
            )
            before_table = current_chunk.content
            merge_table = None
            if len(merged_results) > 0 and current_chunk.chunk_id in merged_results[-1]["chunk_ids"]:
                before_table = merged_results[-1]["content"][-1]
                merge_table = merged_results[-1]
            next_chunk = next(
                chunk for chunk in chunks if chunk.chunk_id == next_indices["first_chunk_id"]
            )

            # Call cross_page_merge with their content
            merge_tag, _ = cross_page_merge_by_html(before_table, next_chunk.content)
            # If the result is a single merged table
            if merge_tag:
                if merge_table:
                    # If already merged, update the existing merged result
                    #
                    merge_table["content"].append(next_chunk.content)
                    merge_table["chunk_ids"].append(next_chunk.chunk_id)
                else:
                    # Create a new merged result
                    merged_results.append(
                        {"content": [before_table, next_chunk.content],
                        "chunk_ids": [current_chunk.chunk_id, next_chunk.chunk_id], }
                    )
    return merged_results


def merge_chunks_with_base(chunks: List[Chunk], merged_results):
    chunk_map = {chunk.chunk_id: chunk for chunk in chunks}
    merged_chunk_ids = set()

    for result in merged_results:
        base_chunk_id = result["chunk_ids"][0]
        base_chunk = chunk_map[base_chunk_id]

        for chunk_id in result["chunk_ids"][1:]:
            if chunk_id in chunk_map:
                base_chunk.dsts.extend(chunk_map[chunk_id].dsts)
                force = merge_table_by_force(result["content"])
                logging.info(f"Force merge content: {force}")
                base_chunk.content = force
                base_chunk.page_num = list(set(base_chunk.page_num + chunk_map[chunk_id].page_num))
                base_chunk.block.extend(chunk_map[chunk_id].block)
                merged_chunk_ids.add(chunk_id)

    chunks = [chunk for chunk in chunks if chunk.chunk_id not in merged_chunk_ids]
    return chunks


def cross_table_merge(chunks: List[Chunk]) -> List[Chunk]:
    merged_results = process_and_merge_chunks(chunks, classify_and_mark_table_indices(chunks))
    return merge_chunks_with_base(chunks, merged_results)


async def process_and_merge_chunks_v2(chunks, table_indices):
    merged_results = []  # Store merged results with chunk IDs
    # merged_chunk_ids = set()  # Track already merged chunk IDs

    # Convert table_indices to a sorted list of (page_num, indices) for sequential processing
    sorted_table_indices = sorted(table_indices.items())

    # SftBaseModelType, chat_api, completion_api, host_map = load_sft_model_config()
    pub_conf = PublicModelGateway.Conf(
        host="http://ai-gateway.wps.cn",
        token="I26OQDZMT3pj2K3IhcRsUhRnlpG7PW4F",  # os.environ("token")
        uid="9047",
        product_name="wps-kanmail-qa",
        intention_code="aigctest",
        sec_from="AI_DRIVE_KNOWLEDGE",
    )
    sft_conf = SftModelGateway.Conf(
        host="http://kmd-api.kas.wps.cn", # 随便str值都可以
        kas_host = "http://kmd-api.kas.wps.cn",
        # 普通模型和多模态模型共用token、uri
        token= "}7B$/(!k:n5Xs$q09|jxt6(&Y4Vx*(@x",
        chat_api= chat_api,
        completion_api = completion_api,
        host_map = host_map,
        product_name = "",
    )
    LLModelRpc().create_models(pub_conf, sft_conf, None, pool_max=10)
    sft_base_model: LLModelRpc.SftBaseModel = LLModelRpc.SftBaseModel(
        base_model = SftBaseModelType.kas_qsearch_qwen3_14b,
    )
    selector: LLModelRpc.ModelSelector = LLModelRpc.ModelSelector(
        model=""
    )
    gateway = LLModelRpc.Gateway.Sft
    llm_sft = LLModelRpc()
        
    table_struct_analyzer = TableStructureAnalyzer( llm_sft=llm_sft, gateway=gateway, selector=selector, sft_base_model=sft_base_model)
    # 初始化检测器
    detector = CrossPageTableDetector(table_struct_analyzer)

    for i in range(len(sorted_table_indices) - 1):
        current_page, current_indices = sorted_table_indices[i]
        next_page, next_indices = sorted_table_indices[i + 1]

        # Check the condition
        if (
                current_indices["from_last_table_index"] != -1
                and next_indices["first_table_index"] != -1
                and current_indices["from_last_table_index"] + next_indices["first_table_index"] < 5
        ):
            # Get the corresponding chunks
            current_chunk = next(
                chunk for chunk in chunks if chunk.chunk_id == current_indices["last_chunk_id"]
            )
            before_table = current_chunk.content
            merge_table = None
            if len(merged_results) > 0 and current_chunk.chunk_id in merged_results[-1]["chunk_ids"]:
                before_table = merged_results[-1]["content"][-1]
                merge_table = merged_results[-1]
            next_chunk = next(
                chunk for chunk in chunks if chunk.chunk_id == next_indices["first_chunk_id"]
            )

            # Call cross_page_merge with their content
            merge_tag, table1_info, table2_info, info_str, merged_type = await detector.cross_page_pipeline(
                before_table, next_chunk.content, sim_threshold=0.9
            )

            # If the result is a single merged table
            if merge_tag:
                if merge_table:
                    # If already merged, update the existing merged result
                    merge_table["table_info"].append(table2_info)
                    merge_table["merged_type"].append(merged_type)
                    merge_table["content"].append(next_chunk.content)
                    merge_table["chunk_ids"].append(next_chunk.chunk_id)
                else:
                    # Create a new merged result
                    merged_results.append({
                        "table_info": [table1_info, table2_info],
                        "merged_type": [merged_type, merged_type],
                        "content": [before_table, next_chunk.content],
                        "chunk_ids": [current_chunk.chunk_id, next_chunk.chunk_id],
                    })
    return merged_results

def merge_chunks_with_base_v2(chunks: List[Chunk], merged_results):
    chunk_map = {chunk.chunk_id: chunk for chunk in chunks}
    merged_chunk_ids = set()

    for result in merged_results:
        base_chunk_id = result["chunk_ids"][0]
        base_chunk = chunk_map[base_chunk_id]
        base_chunk.dsts[0].table_info = result["table_info"][0]
        base_chunk.dsts[0].merged_type = result["merged_type"][0]

        for idx, chunk_id in enumerate(result["chunk_ids"][1:]):
            if chunk_id in chunk_map:
                chunk_map[chunk_id].dsts[0].table_info = result["table_info"][idx]
                chunk_map[chunk_id].dsts[0].merged_type = result["merged_type"][idx]
                base_chunk.dsts.extend(chunk_map[chunk_id].dsts)
                base_chunk.page_num = list(set(base_chunk.page_num + chunk_map[chunk_id].page_num))
                base_chunk.block.extend(chunk_map[chunk_id].block)
                merged_chunk_ids.add(chunk_id)
        
        force = merge_table_by_force_v2(result)
        base_chunk.content = force

    chunks = [chunk for chunk in chunks if chunk.chunk_id not in merged_chunk_ids]
    return chunks


async def cross_table_merge_v2(chunks: List[Chunk]) -> List[Chunk]:
    merged_results = await process_and_merge_chunks_v2(chunks, classify_and_mark_table_indices(chunks))
    return merge_chunks_with_base_v2(chunks, merged_results)

# def table_html2df(table_html: str) -> pd.DataFrame:
#     try:
#         # 判空或非字符串类型直接返回空 DataFrame
#         if not isinstance(table_html, str) or not table_html.strip():
#             return pd.DataFrame()
#
#         # 检查是否包含 <table> 标签
#         if "<table" not in table_html.lower():
#             return pd.DataFrame()
#         df_table = pd.read_html(table_html)[0]
#         df_table = df_table.fillna("")
#         return df_table
#     except Exception as e:
#         logging.error(f"table html解析失败: {str(e)}")
#         return pd.DataFrame()
#
#
# def split_sub_table(table_df: pd.DataFrame):
#     """
#     输入DataFrame形式表格，根据合并单元格内容唯一，分割为子表格
#     """
#     final_table = []
#     potential_table_crop = []
#     columns = table_df.shape[1]
#     if columns > 1:
#         first_col = table_df.iloc[:, 0]
#         mask = table_df.eq(first_col, axis=0).all(axis=1)
#         potential_table_crop = table_df.index[mask].tolist()
#         max_idx = table_df.shape[0]
#
#         # 1. 去重、排序
#         unique_splits = sorted(set(potential_table_crop))
#         # 只有第一行为表头直接添加
#         if len(potential_table_crop) > 1 or (len(potential_table_crop) == 1 and potential_table_crop[0] != 0):
#
#             # 2. 如果第一个不是 0，就插入 0
#             if not unique_splits or unique_splits[0] != 0:
#                 unique_splits.insert(0, 0)
#                 # print("unique_splits", unique_splits)
#             n = len(unique_splits)
#             for i, start in enumerate(unique_splits):
#                 # end 如果不是最后一个，就取下一个 split，否则取到 df 尾
#                 end = unique_splits[i + 1] if i + 1 < n else max_idx
#                 sub_table = table_df.iloc[start:end].reset_index(drop=True)
#                 # print("sub_table", sub_table)
#                 # 去除重复列便于后面比较列数
#                 rm_dupcol_table = sub_table.T.drop_duplicates().T
#                 rm_dupcol_table.columns = range(rm_dupcol_table.shape[1])
#                 final_table.append(rm_dupcol_table)
#         else:
#             final_table.append(table_df)
#     else:
#         # 只有一列的表格直接添加，不做分割
#         final_table.append(table_df)
#
#     return potential_table_crop, final_table


# def cross_page_merge(pre_table: str, next_table: str) -> list[str]:
#     """
#     分页表格合并
#     合并规则：先分割子表格（如果存在），再根据两两表格列数是否相同判断是否属于一个表格
#     to do: 增加表格的内容相似度，设计融合特征来判断
#     """
#     processed_table = []
#     pre_df = table_html2df(pre_table)
#     next_df = table_html2df(next_table)
#     if (not pre_df.empty) and (not next_df.empty):
#         pre_crop_idx, pre_sub = split_sub_table(pre_df)
#         next_crop_idx, next_sub = split_sub_table(next_df)
#
#         # 分割后pre_sub last 和 next_sub first 合并
#         if pre_sub and next_sub:
#             pre_sub_last = pre_sub[-1]
#             next_sub_first = next_sub[0]
#             if pre_sub_last.shape[1] == next_sub_first.shape[1]:
#                 if not pre_sub_last.iloc[0].equals(next_sub_first.iloc[0]):
#                     merge_df = pd.concat([pre_sub_last, next_sub_first], axis=0).reset_index(drop=True)
#                     # print("merge_df", merge_df)
#                     merge_html = merge_df.to_html(index=False, header=False)
#                     if pre_crop_idx:
#                         pre_last_crop = pre_crop_idx[-1]
#                         pre_df_wo_last = pre_df.iloc[:pre_last_crop, :].reset_index(drop=True)
#                         if not pre_df_wo_last.empty:
#                             processed_table.append(pre_df_wo_last.to_html(index=False, header=False))
#                     processed_table.append(merge_html)
#
#                     if next_crop_idx:
#                         next_fir_crop = next_crop_idx[0]
#                         next_df_wo_fir = next_df.iloc[next_fir_crop:, :].reset_index(drop=True)
#                         processed_table.append(next_df_wo_fir.to_html(index=False, header=False))
#                 else:
#                     processed_table.append(pre_table)
#                     processed_table.append(next_table)
#             else:
#                 processed_table.append(pre_table)
#                 processed_table.append(next_table)
#     else:
#         processed_table.append(pre_table)
#         processed_table.append(next_table)
#
#     processed_table = [tbl.replace("<tbody>", "").replace("</tbody>", "") for tbl in processed_table]
#     return processed_table


# if __name__ == "__main__":
#     pre_table_html = """
#     <table><tr><td rowspan=\"1\" colspan=\"1\">SAE发生及处理的详细情况(如同时发生多个严重不良事件，可统一记录在此处)</td></tr><tr><td rowspan=\"1\" colspan=\"1\">[基本信息]男，汉族，年龄：59岁，受试者编号   ，因治疗受试者组本临床试验，于2024年04月02日签署知情同意书“探索人研究”101,版本号：2.0版，版本日期：2023年06月16日)、完善筛选期检查，于2024.04.09第一次给予药物编号(方案编号为：液10ml,于2024.05.09第二次给予F011人脐带间充质干细胞注射液10ml.为[相关病史：合并疾病和既往病史]受试者的合并疾病，既往病史，台并用药情况己于上表合并用药部分详细表述。[不良事件情况]患者家属于2024.05.20晚上19:35告知患者于2024.05.18因人意识不清楚，大小便失禁在上海市奉贤区古华医院办理入院，2024.05.21追问患者家属，告知人清醒，反馈部分检查报告并获知诊断为肝损伤，入院后的相关检查具体异常值见表格。患者家属于2024.,05.23晚19:25告知患者去世，2024.05.24早上家属微信反馈患者死亡小结，报告见附件。患者入院时主要症状及体征：患者因“乏力、纳差1月余”入院，查体：神志清晰，呼吸平稳，全身皮肤粘膜轻度黄染，全身浅表淋巴结无肿大，双巩膜明显黄染，颈软，无颈静脉充盈，无颈静脉怒张，两肺呼吸音粗，未闻及明显干湿啰音，心率90次/分，律齐，无杂音，腹部略膨隆，腹围97厘米，腹软，上腹部轻压痛，无腹部反跳痛，肝脏未触及，颈静脉回流征阴性，移动性浊音阳性，双下肢无浮肿。[不良事件的处理]截至SAE上报时家属未提供住院病历信息。2024.05.24早上家属微信反馈患者死亡小结，患者入院后完善检查，2024-05-20 CT检查报告：检查结果：1两局部肺通气不良：两肺局限性肺气肿：两肺间质纤维化：右肺门部淋巴结钙化：心影饱满：肺动脉干内径增宽：主动脉壁及冠脉局部钙化：两侧局部胸膜增厚、黏连；请结合临床及相关检查，随访。附见：奇静脉、半奇静脉扩张。2、肝脾肿大、肝硬化：脂肪肝，腹腔少量积液，餐后胆囊?肝胃间隙及腹膜后多发淋巴结及肿大：胃窦部、十二指肠局部肠壁增厚：请结合腹部增强检查。2024-05-21彩超检查报告：检查结果：肝脏部分显示肝呈慢性肝病改变(提示早期肝硬化)肝内结节形成中量腹水胆囊显示不清胰腺显示不清(建议消胀后复查)脾略大膀胱充盈差前列腺显示不清、给予保肝：注射用还原型谷胱甘肽、水飞蓟兵葡甲胺片；利胆退黄：丁二磺酸腺甘蛋氨酸、苦黄、熊去氧胆酸胶囊；护胃，保护胃黏膜：注射用法莫替丁：抗感染：头孢美唑；比阿培南：利尿：呋塞米、螺内酯：通便，乳果糖：营养支持，人血白蛋白；氨茶碱解痉平喘：肾衰竭对症活血补气，通腑泻浊；经治疗患者病情未见好转，请公共卫生临床中心钱志平主任会诊后建议加强抗感染，抗生素升级，保护肾功能，注意生命体征变化。与家属沟通后，告知病情，目前患者病情重，随时可出现生命危险，建议上级医院进一步诊治，患者表示拒绝，仍坚持住本院治疗，病程中患者测血压示80/30mmHG,呼吸明显急促，予以洛贝林及尼可刹米注射液、多巴胺及间羟胺注射液：升压及呼吸兴奋治疗，19:00左右患者家属即刻诉患者心电监护出现报警，心电监护仪呈一直线。即刻监测患者血压测不出，呼吸、脉搏、心率为0,血氧饱和度为0,大动脉搏动消失，双瞳孔散大固定，对光反射消失，测心电图示一直线。于2024.5.23,19:06宜告临床死亡。治疗结果：死亡[试验用药使用情况J受试者的试验药物使用情况己于上表试验用药部分详细表述。</td></tr></table>
#     """
#     next_table_html = """
#     <table><tr><td rowspan=\"1\" colspan=\"1\">[对可疑产品采取的措施]不适用[不良事件转归至此报告时，患者有败水，人清覆，仍住院，驶归持续。2024.05.24早上家属微信反馈患者治疗结果：死亡，转归：死亡研究者相关性评价此次SAE与研究药物人脐带间充质干细胞注射液可能无关，追溯病史，由于息者长期有脂肪肝病史，筛选期轻度肝功能不全，不排除患者二次给药后合并用药引起的肝功能损伤。患者本次住院癌胚抗原指标异常升高，不排除肿瘤可能。待患者反馈病历及报告后再进一步评价相关性。</td></tr><tr><td rowspan=\"1\" colspan=\"1\">2024.05.24结合患者家属提供死亡小结报告，根据患者检查结果及患者基础疾病，判定患者死亡与肝功能衰竭、肝硬化失代偿期、酒精性肝病、胆汁淤积、自发性腹膜炎、肾功能不全、高血压、糖尿病、便秘、肺气肿、消化道肿瘤?均可能有关，与研究药物人济带间充质干细胞注射液可能无关。</td></tr></table>
#     """
#     merged_tables = cross_page_merge(pre_table_html, next_table_html)
#     for table in merged_tables:
#         print(table)
