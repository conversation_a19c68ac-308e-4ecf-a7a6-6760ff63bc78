import re
import json
from typing import List, Dict, Optional, Tuple, Set
from dataclasses import dataclass
from bs4 import BeautifulSoup
import logging
import pandas as pd
from io import StringIO
from commons.llm_gateway.llm import LLModelRpc, LLMChatStatus
from commons.llm_gateway.models.chat_data import SftBaseModelType, chat_api, completion_api, host_map
from commons.llm_gateway.models.public_model_gateway import PublicModelGateway
from commons.llm_gateway.models.sft_model_gateway import SftModelGateway
import json_repair
import asyncio
import time

logger = logging.getLogger(__name__)

def load_json_object(text):
    start_index = text.rfind("```json")
    end_index = text.rfind("```")
    if start_index != -1 and end_index != -1:
        json_string = text[start_index + len("```json"):end_index].strip()
        try:
            json_object = json.loads(json_string)
            return json_object
        except json.JSONDecodeError:
            logging.error("Error: Invalid JSON format.")
    else:
        logging.error("no json found")
        try:
            json_object = json_repair.repair_json(text, return_objects=True)
            return json_object
        except json.JSONDecodeError:
            logging.error("Error: Invalid JSON format")
     # 区分真实json:{"table_header":[]} 和 json解析错误
    return None

@dataclass
class TableStructure:
    """表格结构信息"""
    column_count: int
    row_count: int
    rule_headers: List[str]
    rule_header_rows: List[int]
    llm_headers: List[str]
    llm_header_rows: List[int]
    headers: List[str] #最终使用的表头
    header_rows: List[int]
    data_types: List[str]  # 每列的数据类型
    first_data_row: Optional[List[str]] = None
    last_data_row: Optional[List[str]] = None

class TableStructureAnalyzer:
    """表格结构分析器"""
    
    def __init__(self, llm_sft,  gateway, selector, sft_base_model=None):
        # 时间格式正则表达式
        self.time_patterns = [
            r'\d{4}[-/]\d{1,2}[-/]\d{1,2}',  # 2024-01-01, 2024/1/1
            r'\d{1,2}[-/]\d{1,2}[-/]\d{4}',  # 01-01-2024, 1/1/2024
            r'\d{4}\.\d{1,2}\.\d{1,2}',      # 2024.01.01
            r'\d{4}年\d{1,2}月\d{1,2}日',     # 2024年1月1日
            r'\d{1,2}:\d{1,2}:\d{1,2}',      # 12:30:45
        ]
        
        # 序列号模式
        self.sequence_patterns = [
            r'^\d+$',           # 纯数字
            r'^[A-Z]\d+$',      # A001, B002
            r'^\d+\.',          # 1., 2., 3.
            r'^第\d+[项条]',     # 第1项, 第2条
        ]
        
        self.gateway = gateway
        self.selector = selector
        self.sft_base_model = sft_base_model
        self.llm_sft = llm_sft

    def analyze_table_structure(self, table_html: str, use_llm_headers: bool = False) -> TableStructure:
        """分析表格结构"""
        try:
            soup = BeautifulSoup(table_html, 'html.parser')
            table = soup.find('table')
            
            if not table:
                logger.warning("未找到table标签")
                return self._create_empty_structure()
            
            rows = table.find_all('tr')
            if not rows:
                logger.warning("表格中未找到行")
                return self._create_empty_structure()
            
            # 分析表格基本结构
            row_count = len(rows)
            column_count = self._get_column_count(rows)
            
            table_df = self.table_html2df(table_html)
            table_data = table_df.values.tolist()
            table_data = [[str(cell) for cell in row] for row in table_data]
            # 提取表格数据
            # table_data = self._extract_table_data(rows)
            
            # 识别表头
            rule_header_rows, rule_headers = self._identify_headers(table_data)
            llm_header_rows = []
            llm_headers = [[]]
            header_rows = rule_header_rows
            headers = rule_headers

            
            # 分析数据类型
            data_types = self._analyze_data_types(table_data, rule_header_rows)
            
            # # 获取首末行数据
            first_data_row, last_data_row = self._get_boundary_rows(table_data, rule_header_rows)
            
            return TableStructure(
                column_count=column_count,
                row_count=row_count,
                rule_headers=rule_headers,
                rule_header_rows=rule_header_rows,
                llm_headers=llm_headers,
                llm_header_rows=llm_header_rows,
                headers=headers,
                header_rows=header_rows,
                data_types=data_types,
                first_data_row=first_data_row,
                last_data_row=last_data_row
            )
            
        except Exception as e:
            logger.error(f"分析表格结构时出错: {e}")
            return self._create_empty_structure()

    async def get_table_header(self, table_html, table_structure):
        status, header_info = await self._sft_llm_based_identify_headers(table_html)
        if status and header_info != None:
            table_llm_header_rows = header_info.get('table_header', [])
            # if not table_llm_header_rows:
            table_data = self._extract_table_data(table_html)
            if table_data:
                table_llm_headers = [table_data[i] for i in table_llm_header_rows] if table_llm_header_rows else [[]]
                logging.info(f"table_llm_headers success:{table_llm_header_rows}")
            else:
                table_llm_headers = [[]]
            table_header_rows = table_llm_header_rows
            table_headers = table_llm_headers
        else:
            logging.info(f"table_llm_headers failed")
            table_header_rows = table_structure.rule_header_rows
            table_headers = table_structure.rule_headers
        return table_header_rows, table_headers


    def table_structure_to_dict(self, table_structure: TableStructure) -> Dict:
        """将TableStructure对象转换为字典格式"""
        return {
            "column_count": table_structure.column_count,
            "row_count": table_structure.row_count,
            "rule_headers": table_structure.rule_headers,
            "rule_header_rows": table_structure.rule_header_rows,
            "llm_headers": table_structure.llm_headers,
            "llm_header_rows": table_structure.llm_header_rows,
            "headers": table_structure.headers,
            "header_rows": table_structure.header_rows,
            "data_types": table_structure.data_types,
            "first_data_row": table_structure.first_data_row,
            "last_data_row": table_structure.last_data_row
        }

    def _create_empty_structure(self) -> TableStructure:
        """创建空的表格结构"""
        return TableStructure(
            column_count=0,
            row_count=0,
            rule_headers=[[]],
            rule_header_rows=[],
            llm_headers=[[]],
            llm_header_rows=[],
            headers=[[]],
            header_rows=[],
            data_types=[],
            first_data_row=None,
            last_data_row=None
        )
    def table_html2df(self, table_html: str) -> pd.DataFrame:
        try:
            # 判空或非字符串类型直接返回空 DataFrame
            if not isinstance(table_html, str) or not table_html.strip():
                return pd.DataFrame()

            # 检查是否包含 <table> 标签
            if "<table" not in table_html.lower():
                return pd.DataFrame()
            
            # 使用 StringIO 包装 HTML 字符串
            df_table = pd.read_html(StringIO(table_html), keep_default_na=False, na_values=[""])[0]
            df_table = df_table.fillna("")
            return df_table
        except Exception as e:
            logging.error(f"table html解析失败: {str(e)}")
            return pd.DataFrame()

    def _get_column_count(self, rows: List) -> int:
        """获取表格列数"""
        max_cols = 0
        for row in rows:
            cols = row.find_all(['td', 'th'])
            col_count = sum(int(col.get('colspan', 1)) for col in cols)
            max_cols = max(max_cols, col_count)
        return max_cols

    def _extract_table_data(self, table_html: str) -> List[List[str]]:
        """提取表格二维数组"""
        table_df = self.table_html2df(table_html)
        if table_df.empty:
            return [[]]
        else:
            table_data = table_df.values.tolist()
            table_data = [[str(cell) for cell in row] for row in table_data]
            return table_data


    def _build_header_prompt3(self, table: str) -> str:
        """构建检测提示"""
        return f"""
        你是一个资深表格结构分析专家，需要精准识别HTML表格的表头行索引。请严格遵循以下流程：

        ### 任务定义
        输入：HTML格式的表格（包含`<table>`标签及行列结构）
        输出：表头行在原始表格中的索引数组（从0开始计数）

        ### 核心识别原则：先表格分类，再处理表头

        **第一步：必须首先判断表格类型**
        1.  **列表头表格（Header Row Table）**：
            *   **特征**：存在**一行或多行**，其单元格的**唯一或主要作用**是作为**列标签**，描述其**下方整列数据**的属性。
            *   **示例**：一个表格第一行为 [`姓名`, `年龄`, `城市`]，其下方每一行都是具体的人物数据。`姓名`列下方全是名字，`年龄`列下方全是数字。

        2.  **属性-值对表格（Key-Value Table）**：
            *   **特征**：表格呈现为一个**属性列表**。每一行（或一组相邻单元格）包含一个独立的“属性-值”对。
                *   **键（Key）**：描述性的标签（如“药物名称”、“生产厂家”）。
                *   **值（Value）**：对应标签的具体内容。
            *   **关键判别点**：**没有任何一行**是在为下方的“列”提供描述；相反，每一行的**第一个单元格**（或前几个单元格）**自我描述**了该行其余单元格内容的含义。
            *   **处理**：此类表格**没有传统意义的表头行**，必须输出空列表 `[]`。

        3.  **矩阵型表格（Matrix Table）**：
            *   **特征**：首行和首列都是标签，交叉区域的单元格是数据值。行列标签在逻辑上是平等的。
            *   **处理**：此类表格**没有传统意义的表头行**，必须输出空列表 `[]`。

        4.  **无表头数据网格（Headless Data Grid）**：
            *   **特征**：从第一行起就是纯粹的数据，没有任何描述性标签行。
            *   **处理**：输出空列表 `[]`。

        **第二步：仅对“列表头表格”执行以下表头检测**
        1.  **表头本质**：表头是**描述下方数据列属性**的标签行（如"姓名"、"年龄"、"价格(元)"）。其核心特征是**为列提供定义**。
        
        2.  **多行表头**：
            *   遇到跨行合并单元格（`rowspan`）时，表头包含所有被合并的行。
            *   示例：若第0-1行共同组成表头 → 输出 [0,1]
        
        3.  **非首行表头**：
            *   跳过标题行（通常为跨列的全行文本）。
            *   忽略表头前的元数据行（如"单位：万元"）。

        ### 分析步骤（强制逐步推理）
        1.  **解析结构**：分析HTML，理清所有单元格的实际行跨与列跨。
        2.  **类型判别**（**最关键步骤**）：
            *   检查表格是“列表头型”、“属性-值对型”、“矩阵型”还是“无表头型”。
            *   **重点**：如果表格呈现为“属性-值对型”、“矩阵型”和“无表头型”，结论为 `[]`，无需进一步分析表头。
        3.  **表头检测**（**仅在判定为“列表头表格”后执行**）：
            *   定位首个包含**列描述标签**的行（即非全表合并行）。
            *   检查该行及相邻行的语义连续性（如多级表头）。
        4.  **边界确认**：
            *   上边界：确保不是标题行（标题行通常无数据列）。
            *   下边界：识别表头终结行（当出现数据类型从标签突变为具体数据时）。
        5.  **输出**：严格按格式输出。

        ### 输出规范
        ```json
        {{
        "table_header": [int] // 有序行索引数组。对于`属性-值对表格`、`矩阵型表格`、`无表头数据网格`，必须输出空列表 `[]`。
        }}

        ### 输入表格
        {table}
        """

    async def _sft_llm_based_identify_headers(self, table) -> str:

        prompt = self._build_header_prompt3(table = table)
        status, response = await self.llm_sft.async_chat_text(
            gateway = self.gateway,
            query = prompt,
            sft_base_model = self.sft_base_model,
            temperature= 0.0,      
            max_token = 256,       
            # stream = False,
            chat_template_kwargs = {"enable_thinking": False},
            selector = self.selector,
            # return_usage = True
        )
        print("response", response)
        # print("usage", usage)
        if status == LLMChatStatus.OK:
            response = response.strip()
            json_object = load_json_object(response)
            return True, json_object
        else:
            logging.error(f"SFT模型检测表头时出错: {status}")
            return False, {}
        
    def _identify_headers(self, table_data: List[List[str]]) -> Tuple[List[int], List[List]]:
        """识别表头行"""
        if not table_data:
            return [], [[]]
        
        table_data_types = self._analyze_data_types(table_data, [])
        print("table_data_types", table_data_types)
        header_rows = []
        headers = []
        
        # 简单策略：前几行包含表头关键词的行作为表头
        for i, row in enumerate(table_data[:3]):  # 只检查前3行
            row_data_types = [self._cell_data_type(cell) for cell in row]
            print("row_data_types", row_data_types)
            # 检测表头的条件仍要修改
            if (row_data_types != table_data_types) and row_data_types.count("text") > len(row_data_types) * 0.5:

                header_rows.append(i)
                headers.append(row)
        
        # 如果没有找到表头，使用第一行; 有可能不存在表头
        if not header_rows:
            headers = [[]]
        
        return header_rows, headers

    def _is_header_row(self, row: List[str], table_data_types) -> bool:
        """判断是否为表头行"""
        if not row:
            return False
        
        row_data_types = [self._cell_data_type(cell) for cell in row]
        
        if row_data_types != table_data_types and row_data_types.count("text") > len(row_data_types) * 0.5:
            return True


    def _analyze_data_types(self, table_data: List[List[str]], header_rows: List[int]) -> List[str]:
        """分析每列的数据类型"""
        # if not table_data or not header_rows:
        #     return []
        if not table_data:
            return []
        column_count = len(table_data[0]) if table_data else 0
        data_types = []
        
        # 获取数据行（非表头行）
        data_rows = [row for i, row in enumerate(table_data) if i not in header_rows]
        
        for col_idx in range(column_count):
            col_data = [row[col_idx] if col_idx < len(row) else "" for row in data_rows]
            col_type = self._infer_column_type(col_data)
            data_types.append(col_type)
        
        return data_types

    def _infer_column_type(self, column_data: List[str]) -> str:
        """推断列的数据类型"""

        if not column_data:
            return "empty"
        
        # 检查数据类型
        numeric_count = sum(1 for cell in column_data if self._is_numeric(cell))
        time_count = sum(1 for cell in column_data if self._is_time_format(cell))
        empty_count = sum(1 for cell in column_data if self._cell_data_type(cell) == "empty")
        
        total_count = len(column_data)
        
        if time_count >= int(total_count * 0.5):
            return "time"
        elif numeric_count >= int(total_count * 0.7):
            return "numeric"
        elif empty_count >= int(total_count * 0.8):
            return "empty"
        else:
            return "text"
    def row_data_type(self, row: List[str]) -> List[str]:
        """判断行数据类型"""
        row_data_types = []
        if row:
            for cell in row:
                row_data_types.append(self._cell_data_type(str(cell)))
        
        return row_data_types

    def _cell_data_type(self, text: str) -> str:
        """判断单元格的数据类型"""
        if not text.strip():
            return "empty"
        elif self._is_numeric(text):
            return "numeric"
        elif self._is_time_format(text):
            return "time"
        else:
            return "text"

    def _is_numeric(self, text: str) -> bool:
        """判断文本是否为数字"""
        if not text.strip():
            return False
        
        # 移除常见的数字格式符号
        cleaned = re.sub(r'[,，\s%％]', '', text.strip())
        
        try:
            float(cleaned)
            return True
        except ValueError:
            return False

    def _is_time_format(self, text: str) -> bool:
        """判断文本是否为时间格式"""
        if not text.strip():
            return False
        
        for pattern in self.time_patterns:
            if re.search(pattern, text):
                return True
        return False

    def _get_boundary_rows(self, table_data: List[List[str]], 
                          header_rows: List[int]) -> Tuple[Optional[List[str]], Optional[List[str]]]:
        """获取首末数据行"""
        if not table_data:
            return None, None
        
        data_rows = [row for i, row in enumerate(table_data) if i not in header_rows]
        
        if not data_rows:
            return None, None
        
        return data_rows[0], data_rows[-1]


async def main():

    # SftBaseModelType, chat_api, completion_api, host_map = load_sft_model_config()
    pub_conf = PublicModelGateway.Conf(
        host="http://ai-gateway.wps.cn",
        token="I26OQDZMT3pj2K3IhcRsUhRnlpG7PW4F",  # os.environ("token")
        uid="9047",
        product_name="wps-kanmail-qa",
        intention_code="aigctest",
        sec_from="AI_DRIVE_KNOWLEDGE",
    )
    sft_conf = SftModelGateway.Conf(
        host="http://kmd-api.kas.wps.cn", # 随便str值都可以
        kas_host = "http://kmd-api.kas.wps.cn",
        # 普通模型和多模态模型共用token、uri
        token= "}7B$/(!k:n5Xs$q09|jxt6(&Y4Vx*(@x",
        chat_api= chat_api,
        completion_api = completion_api,
        host_map = host_map,
        product_name = "",
    )
    LLModelRpc().create_models(pub_conf, sft_conf, None, pool_max=10)
    sft_base_model: LLModelRpc.SftBaseModel = LLModelRpc.SftBaseModel(
        base_model = SftBaseModelType.kas_qwen3_14b,
    )
    selector: LLModelRpc.ModelSelector = LLModelRpc.ModelSelector(
        model=""
    )
    gateway = LLModelRpc.Gateway.Sft
    llm_sft = LLModelRpc()
    
    table_structure_analyzer = TableStructureAnalyzer( llm_sft=llm_sft, gateway=gateway, selector=selector, sft_base_model=sft_base_model)
    
    table1 = """<table><tr><td rowspan="2" colspan="1">Sl. No.</td><td rowspan="2" colspan="1">Component</td><td rowspan="2" colspan="2">Test Method</td><td rowspan="1" colspan="2">Limit</td><td rowspan="2" colspan="1">Result</td><td rowspan="2" colspan="1">Remark</td></tr><tr><td rowspan="1" colspan="1">Min</td><td rowspan="1" colspan="1">Max</td></tr><tr><td rowspan="7" colspan="1">13</td><td rowspan="7" colspan="1">Size Distribution, %</td><td rowspan="7" colspan="1">EN-933 -1:2007</td><td rowspan="1" colspan="3">> 50 mm</td><td rowspan="1" colspan="1">0</td></tr><tr><td rowspan="1" colspan="3">25 < size < 50 mm</td><td rowspan="1" colspan="1">0</td></tr><tr><td rowspan="1" colspan="3">13 < size < 25 mm</td><td rowspan="1" colspan="1">3.92</td></tr><tr><td rowspan="1" colspan="3">10 < size < 13 mm</td><td rowspan="1" colspan="1">11.11</td></tr><tr><td rowspan="1" colspan="3">1.5 < size < 10 mm</td><td rowspan="1" colspan="1">44.44</td></tr><tr><td rowspan="1" colspan="3">0.4 < size < 1.5 mm</td><td rowspan="1" colspan="1">20.92</td></tr><tr><td rowspan="1" colspan="3">< 0.4 mm</td><td rowspan="1" colspan="1">19.61</td></tr></table>"""

    table_df = table_structure_analyzer.table_html2df(table1)
    table_data = table_df.values.tolist()
    table_data = [[str(cell) for cell in row] for row in table_data]
    print(table_data)
    print(len(table_data))
    header_rows, headers = table_structure_analyzer._identify_headers(table_data)
    header_rows, headers = table_structure_analyzer._openai_llm_based_identify_headers(table_data)

    table_structure = table_structure_analyzer.analyze_table_structure(table1)
    header_rows, headers = await table_structure_analyzer.get_table_header(table1, table_structure)
    print(header_rows)
    print(headers)


if __name__ == "__main__":
    asyncio.run(main())



