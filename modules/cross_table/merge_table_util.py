import pandas as pd
import logging
import json
from bs4 import BeautifulSoup, Tag


def table_html2df(table_html: str) -> pd.DataFrame:
    try:
        # 判空或非字符串类型直接返回空 DataFrame
        if not isinstance(table_html, str) or not table_html.strip():
            return pd.DataFrame()

        # 检查是否包含 <table> 标签
        if "<table" not in table_html.lower():
            return pd.DataFrame()
        df_table = pd.read_html(table_html, keep_default_na=False, na_values=[""])[0]
        df_table = df_table.fillna("")
        return df_table
    except Exception as e:
        logging.error(f"table html解析失败: {str(e)}")
        return pd.DataFrame()


def split_sub_table_by_html(html: str) -> list[list[str]]:
    """
    将给定 HTML 表格字符串按“全宽”行分割成子表格。
    规则：当某一行的 colspan 之和等于该表的最大列数时，
    认为该行是子表格的第一行，从该行开始到下一个此类行之前（不含下一标记行）为一个子表格。

    Args:
        html: 含 <table>...</table> 的 HTML 字符串

    Returns:
        subtables: 子表格 HTML 字符串列表，每项为 "<table>...</table>"
    """
    soup = BeautifulSoup(html, "html.parser")
    table = soup.find("table")
    if table is None:
        return []

    rows = table.find_all("tr")
    if not rows:
        return []

    # 计算每行的 colspan 总和
    def row_colspan_sum(row):
        total = 0
        for cell in row.find_all(['td', 'th']):
            colspan = cell.get('colspan')
            total += int(colspan) if colspan and colspan.isdigit() else 1
        return total

    # 判断某行是否“单元格数为1且colspan等于max_cols”
    def is_fullwidth_singlecell(row, max_cols) -> bool:
        cells = row.find_all(['td', 'th'])
        if len(cells) != 1:
            return False
        colspan = cells[0].get('colspan')
        try:
            return int(colspan) == max_cols
        except (TypeError, ValueError):
            return False

    # 找到最大列数（按行 colspan 和）
    max_cols = max(row_colspan_sum(r) for r in rows)

    subtables = []
    current_rows = []

    for row in rows:
        if is_fullwidth_singlecell(row, max_cols):

            # 遇到全宽行，开启一个新子表格
            if current_rows:
                subtables.append(current_rows)
            current_rows = [row]
        else:
            # 只有在已开启子表格时，才追加普通行
            # if current_rows is not None:
            #     current_rows.append(row)
            current_rows.append(row)
            # 若 current_rows 仍然 None，说明还未遇到第一标记行，跳过
    # 循环结束后，若有剩余 current_rows，加入最后一个子表格
    if current_rows:
        subtables.append(current_rows)

    # 将每个子表格的行列表封装回 <table> ... </table>
    subtable_htmls = []
    for rows in subtables:
        # 创建新的空 table
        new_soup = BeautifulSoup("<table></table>", "html.parser")
        new_table = new_soup.find("table")
        for r in rows:
            # 注意：直接 append 可能会将原 row 从原 table 中移动到新 table。
            # 如果不想修改原始 soup，可以使用 copy.copy 或者重新构造行。
            new_table.append(r)
        subtable_htmls.append(str(new_table))

    return subtables


def generate_new_html_table(rows) -> str:
    """
    将行列表转换为新的 HTML 表格字符串。
    """

    def process_row_html(row):
        if isinstance(row, Tag):
            return row
        elif isinstance(row, str):
            # 解析字符串成 Tag
            tr_tag = BeautifulSoup(row, "html.parser").find("tr")
            return tr_tag if tr_tag else None
        return None

    new_soup = BeautifulSoup("<table></table>", "html.parser")
    new_table = new_soup.find("table")
    for row in rows:
        if isinstance(row, list):
            # input: list[list[str,Tag]]
            # 如果是列表，假设每个元素都是 <tr> 的字符串
            for r in row:
                tr_tag = process_row_html(r)
                if tr_tag:
                    new_table.append(tr_tag)
                else:
                    continue  # 跳过无效行
        else:
            # 输入是list[[str,Tag]]
            # row type 不是str，而是 BeautifulSoup 的 Tag 对象
            tr_tag = process_row_html(row)
            if tr_tag:
                new_table.append(tr_tag)
            else:
                continue  # 跳过无效行

    return str(new_table)


def extract_trs(table_html: str) -> list[str]:
    """
    从 HTML 表格字符串中提取所有 <tr> 标签片段，返回列表形式的 <tr>...</tr> 字符串。
    会移除外层 <table>、<tbody>、</tbody>、</table> 等，仅保留 <tr> ... </tr>。
    """
    soup = BeautifulSoup(table_html, "html.parser")
    table = soup.find("table")
    if table is None:
        return []

    rows = table.find_all("tr")
    return [str(row) for row in rows]


def cross_page_merge_by_html(pre_table: str, next_table: str):
    pre_sub_tables = split_sub_table_by_html(pre_table)
    next_sub_tables = split_sub_table_by_html(next_table)

    merge_flag = False
    # 先初始化为两个原始表格，如何满足下面条件，最后会返回一个合并之后的表格
    final_table = [pre_table, next_table]
    # 可能分割为子表格；也有可能不分割为子表格，不分割子表格返回一个原始表格rows，list[list]元素为1，不应该为空
    if pre_sub_tables and next_sub_tables:
        pre_sub_last = pre_sub_tables[-1]
        next_sub_first = next_sub_tables[0]

        pre_sub_last = generate_new_html_table(pre_sub_last)
        next_sub_first = generate_new_html_table(next_sub_first)
        pre_df_last = table_html2df(pre_sub_last)
        next_df_first = table_html2df(next_sub_first)
        if (not pre_df_last.empty) and (not next_df_first.empty):
            # 清洗重复列，还原子表格原始的真实列数
            pre_df_last = pre_df_last.T.drop_duplicates().T
            pre_df_last.columns = range(pre_df_last.shape[1])
            next_df_first = next_df_first.T.drop_duplicates().T
            next_df_first.columns = range(next_df_first.shape[1])

            if pre_df_last.shape[1] == next_df_first.shape[1]:
                if not pre_df_last.iloc[0].equals(next_df_first.iloc[0]):
                    merge_flag = True
                    merge_df = pd.concat([pre_df_last, next_df_first], axis=0).reset_index(drop=True)

                    merge_html = merge_df.to_html(index=False, header=False)  # 带有<table> tag

                    pre_table_html = generate_new_html_table(pre_sub_tables[:-1])
                    next_table_html = generate_new_html_table(next_sub_tables[1:])

                    final_table = [pre_table_html, merge_html, next_table_html]

    return merge_flag, final_table


# def parse_table_rows(table_html: str) -> list[str]:
#     """
#     解析表格行，返回行列表。
#     """
#     soup = BeautifulSoup(table_html, "html.parser")
#     table = soup.find("table")
#     if table is None:
#         return []

#     rows = table.find_all("tr")
#     return [str(row) for row in rows]

def merge_table_by_force(table_list):
    """
    输入多个table list，暴力按行拼接，最后完整表格可能异常
    """
    total_rows = []
    for table in table_list:
        table_rows = extract_trs(table)
        if table_rows:
            total_rows.append(table_rows)
    total_table_html = generate_new_html_table(total_rows)

    return total_table_html


if __name__ == "__main__":
    # pre_table = """<table><tr><td rowspan="1" colspan="15">临床项目及报告单位信息</td></tr><tr><td rowspan="1" colspan="2">医疗机构及专业名称</td><td rowspan="1" colspan="7">上海市第六人民医院呼吸内科</td><td rowspan="1" colspan="3">电话</td><td rowspan="1" colspan="3"></td></tr><tr><td rowspan="1" colspan="2">中报单位名称</td><td rowspan="1" colspan="7">上海市第六人民医院</td><td rowspan="1" colspan="3">电话</td><td rowspan="1" colspan="3"></td></tr><tr><td rowspan="1" colspan="2">临床研究方案名称</td><td rowspan="1" colspan="13">股性及初步有效性探拿人脐带问充质干细胞注射液湘的开放性临床研究</td></tr><tr><td rowspan="1" colspan="2">临床研究方案号</td><td rowspan="1" colspan="13">[</td></tr><tr><td rowspan="1" colspan="2">临床适应症</td><td rowspan="1" colspan="13">质纤维化</td></tr><tr><td rowspan="1" colspan="2">临床研究分类</td><td rowspan="1" colspan="13">□验证类临床试验□I期□生物等效性试验☑Ⅱ期□Ⅲ期   IV期</td></tr><tr><td rowspan="1" colspan="2">试验盲态情况</td><td rowspan="1" colspan="13">年 月 日)□盲态(□未破盲□已破盲一破盲时间：☑非盲态</td></tr><tr><td rowspan="1" colspan="15">报告者信息</td></tr><tr><td rowspan="1" colspan="2">报告者姓名</td><td rowspan="1" colspan="7"></td><td rowspan="1" colspan="3">所在国家</td><td rowspan="1" colspan="3">中国</td></tr><tr><td rowspan="1" colspan="2">职业</td><td rowspan="1" colspan="7">呼吸科医师</td><td rowspan="1" colspan="3">电话</td><td rowspan="1" colspan="3"></td></tr><tr><td rowspan="1" colspan="2">获知SAE时问</td><td rowspan="1" colspan="13">___年____月___日□首次获知时问☑随访信息获知时问   2024      年   05月23    日19:25</td></tr><tr><td rowspan="1" colspan="15">受试者信息</td></tr><tr><td rowspan="1" colspan="1">姓名缩写</td><td rowspan="1" colspan="3"></td><td rowspan="1" colspan="2">性别</td><td rowspan="1" colspan="1">男</td><td rowspan="1" colspan="3">出生日期</td><td rowspan="1" colspan="2">1965年3月25日</td><td rowspan="1" colspan="2">发生SAE时的年龄</td><td rowspan="1" colspan="1">59</td></tr><tr><td rowspan="1" colspan="1">受试者编号</td><td rowspan="1" colspan="3"></td><td rowspan="1" colspan="2">民族</td><td rowspan="1" colspan="1">汉</td><td rowspan="1" colspan="3">身高(cm)</td><td rowspan="1" colspan="2">170</td><td rowspan="1" colspan="2">体重(kg)</td><td rowspan="1" colspan="1">87</td></tr><tr><td rowspan="2" colspan="1">患者死亡</td><td rowspan="1" colspan="14">□否</td></tr><tr><td rowspan="1" colspan="2">☑是</td><td rowspan="1" colspan="1">死亡日期</td><td rowspan="1" colspan="3">2024年5月23日</td><td rowspan="1" colspan="1">死亡原因</td><td rowspan="1" colspan="3">急性肝衰竭</td><td rowspan="1" colspan="2">是否尸检</td><td rowspan="1" colspan="2">☑否□是尸检结果______</td></tr><tr><td rowspan="1" colspan="15">现病史(试验用药适应症以外，SAE发生时未恢复的疾病)</td></tr><tr><td rowspan="1" colspan="1">疾病名称</td><td rowspan="1" colspan="4">开始日期</td><td rowspan="1" colspan="3">结束日期</td><td rowspan="1" colspan="3">是否为家族史</td><td rowspan="1" colspan="4">备注</td></tr><tr><td rowspan="1" colspan="1">脂肪所</td><td rowspan="1" colspan="4">2022-01-17</td><td rowspan="1" colspan="3">/</td><td rowspan="1" colspan="3">NA</td><td rowspan="1" colspan="4"></td></tr><tr><td rowspan="1" colspan="1">肝囊肿</td><td rowspan="1" colspan="4">2024-04-03</td><td rowspan="1" colspan="3">/</td><td rowspan="1" colspan="3">NA</td><td rowspan="1" colspan="4"></td></tr><tr><td rowspan="1" colspan="1">胆囊肌腺症</td><td rowspan="1" colspan="4">2022-01-13</td><td rowspan="1" colspan="3">/</td><td rowspan="1" colspan="3">NA</td><td rowspan="1" colspan="4"></td></tr><tr><td rowspan="1" colspan="1">脾肿大</td><td rowspan="1" colspan="4">2024-04-03</td><td rowspan="1" colspan="3">/</td><td rowspan="1" colspan="3">NA</td><td rowspan="1" colspan="4"></td></tr><tr><td rowspan="1" colspan="1">I]型糖尿病</td><td rowspan="1" colspan="4">2022-01-UK</td><td rowspan="1" colspan="3">/</td><td rowspan="1" colspan="3">NA</td><td rowspan="1" colspan="4"></td></tr><tr><td rowspan="1" colspan="1">高血压</td><td rowspan="1" colspan="4">2022-UK-UK</td><td rowspan="1" colspan="3">/</td><td rowspan="1" colspan="3">NA</td><td rowspan="1" colspan="4"></td></tr><tr><td rowspan="1" colspan="1">癌胚抗原升高</td><td rowspan="1" colspan="4">2024-03-29</td><td rowspan="1" colspan="3">/</td><td rowspan="1" colspan="3">NA</td><td rowspan="1" colspan="4"></td></tr><tr><td rowspan="1" colspan="1">糖类抗原升高</td><td rowspan="1" colspan="4">2024-03-29</td><td rowspan="1" colspan="3">/</td><td rowspan="1" colspan="3">NA</td><td rowspan="1" colspan="4"></td></tr><tr><td rowspan="1" colspan="1">神经元特异烯醇化酶升高</td><td rowspan="1" colspan="4">2024-03-29</td><td rowspan="1" colspan="3">/</td><td rowspan="1" colspan="3">NA</td><td rowspan="1" colspan="4"></td></tr><tr><td rowspan="1" colspan="1">非小细胞肺癌</td><td rowspan="1" colspan="4">2024-03-29</td><td rowspan="1" colspan="3">/</td><td rowspan="1" colspan="3">NA</td><td rowspan="1" colspan="4"></td></tr></table>"""
    # next_table = """<table><tr><td rowspan="1" colspan="1">相关Ag21-1升高</td><td rowspan="1" colspan="2"></td><td rowspan="1" colspan="2"></td><td rowspan="1" colspan="2"></td><td rowspan="1" colspan="3"></td></tr><tr><td rowspan="1" colspan="1">肝功能不全</td><td rowspan="1" colspan="2">2022-01-12</td><td rowspan="1" colspan="2">/</td><td rowspan="1" colspan="2">NA</td><td rowspan="1" colspan="3"></td></tr><tr><td rowspan="1" colspan="1">高尿酸血症</td><td rowspan="1" colspan="2">2022-01-12</td><td rowspan="1" colspan="2">/</td><td rowspan="1" colspan="2">NA</td><td rowspan="1" colspan="3"></td></tr><tr><td rowspan="1" colspan="1">飘酸激酶同工酶升高</td><td rowspan="1" colspan="2">2024-04-03</td><td rowspan="1" colspan="2">1.</td><td rowspan="1" colspan="2">NA</td><td rowspan="1" colspan="3"></td></tr><tr><td rowspan="1" colspan="1">乳酸脱氢酶升高</td><td rowspan="1" colspan="2">2024-04-03</td><td rowspan="1" colspan="2">/</td><td rowspan="1" colspan="2">NA</td><td rowspan="1" colspan="3"></td></tr><tr><td rowspan="1" colspan="1">尿红细胞升高</td><td rowspan="1" colspan="2">2024-04-03</td><td rowspan="1" colspan="2">2024.04.17</td><td rowspan="1" colspan="2">NA</td><td rowspan="1" colspan="3"></td></tr><tr><td rowspan="1" colspan="1">心律失常</td><td rowspan="1" colspan="2">2022-01-12</td><td rowspan="1" colspan="2">/</td><td rowspan="1" colspan="2">NA</td><td rowspan="1" colspan="3"></td></tr><tr><td rowspan="1" colspan="1">肺曲莓病</td><td rowspan="1" colspan="2">2024-03-29</td><td rowspan="1" colspan="2">/</td><td rowspan="1" colspan="2">NA</td><td rowspan="1" colspan="3"></td></tr><tr><td rowspan="1" colspan="1">两下肢动脉硬化玻块形成</td><td rowspan="1" colspan="2">2022-01-13</td><td rowspan="1" colspan="2">/</td><td rowspan="1" colspan="2">NA</td><td rowspan="1" colspan="3"></td></tr><tr><td rowspan="1" colspan="1">左肺上叶小结节</td><td rowspan="1" colspan="2">2024-03-25</td><td rowspan="1" colspan="2">/</td><td rowspan="1" colspan="2">NA</td><td rowspan="1" colspan="3"></td></tr><tr><td rowspan="1" colspan="1">前上纵隔结节</td><td rowspan="1" colspan="2">2024-03-25</td><td rowspan="1" colspan="2">/</td><td rowspan="1" colspan="2">NA</td><td rowspan="1" colspan="3"></td></tr><tr><td rowspan="1" colspan="1">右侧第8后肋陈旧性骨折</td><td rowspan="1" colspan="2">2024-03-25</td><td rowspan="1" colspan="2">/</td><td rowspan="1" colspan="2">NA</td><td rowspan="1" colspan="3"></td></tr><tr><td rowspan="1" colspan="1">颅内多发缺血灶</td><td rowspan="1" colspan="2">2024-03-30</td><td rowspan="1" colspan="2">/</td><td rowspan="1" colspan="2">NA</td><td rowspan="1" colspan="3"></td></tr><tr><td rowspan="1" colspan="1">左例频枕叶班片陈旧性病史</td><td rowspan="1" colspan="2">2024-03-30</td><td rowspan="1" colspan="2">/</td><td rowspan="1" colspan="2">NA</td><td rowspan="1" colspan="3"></td></tr><tr><td rowspan="1" colspan="1">冠心病</td><td rowspan="1" colspan="2">2024-03-27</td><td rowspan="1" colspan="2">/</td><td rowspan="1" colspan="2">NA</td><td rowspan="1" colspan="3"></td></tr><tr><td rowspan="1" colspan="1">肺动脉高压中度</td><td rowspan="1" colspan="2">2024-03-29</td><td rowspan="1" colspan="2">/</td><td rowspan="1" colspan="2">NA</td><td rowspan="1" colspan="3"></td></tr><tr><td rowspan="1" colspan="1">及下胶水肿</td><td rowspan="1" colspan="2">2024.05.UK</td><td rowspan="1" colspan="2">/</td><td rowspan="1" colspan="2">NA</td><td rowspan="1" colspan="3"></td></tr><tr><td rowspan="1" colspan="10">既往病史(SAE发生前已恢复的疾病)</td></tr><tr><td rowspan="1" colspan="1">疾病名称</td><td rowspan="1" colspan="2">开始日期</td><td rowspan="1" colspan="2">结束日期</td><td rowspan="1" colspan="2">是否为家族史</td><td rowspan="1" colspan="3">备注</td></tr><tr><td rowspan="1" colspan="1">肺炎</td><td rowspan="1" colspan="2">2024.04.30</td><td rowspan="1" colspan="2">2024.05.07</td><td rowspan="1" colspan="2">NA</td><td rowspan="1" colspan="3"></td></tr><tr><td rowspan="1" colspan="1"></td><td rowspan="1" colspan="2"></td><td rowspan="1" colspan="2"></td><td rowspan="1" colspan="2"></td><td rowspan="1" colspan="3"></td></tr><tr><td rowspan="1" colspan="10">过敏史：(过敏原：_)口有☑无</td></tr><tr><td rowspan="1" colspan="10">饮酒史：因有□无2-3两/日，约40余年</td></tr><tr><td rowspan="1" colspan="10">吸烟史：☑有□无2包/日，约40余年</td></tr><tr><td rowspan="1" colspan="10">其他：□有☑无</td></tr><tr><td rowspan="1" colspan="10">相关实验室检查(如适用，选填)</td></tr><tr><td rowspan="1" colspan="1">检查项</td><td rowspan="1" colspan="1">检查日期</td><td rowspan="1" colspan="2">检查结果</td><td rowspan="1" colspan="2">单位</td><td rowspan="1" colspan="2">正常值下限</td><td rowspan="1" colspan="1">正常值上限</td><td rowspan="1" colspan="1">备注</td></tr><tr><td rowspan="1" colspan="1">透明质酸</td><td rowspan="1" colspan="1">2024.5.19</td><td rowspan="1" colspan="2">2437.16</td><td rowspan="1" colspan="2">Mg/ml</td><td rowspan="1" colspan="2">0</td><td rowspan="1" colspan="1">120</td><td rowspan="1" colspan="1"></td></tr><tr><td rowspan="1" colspan="1">层粘连蛋白</td><td rowspan="1" colspan="1">2024.5.19</td><td rowspan="1" colspan="2">652.73</td><td rowspan="1" colspan="2">mg/ml</td><td rowspan="1" colspan="2">0</td><td rowspan="1" colspan="1">130</td><td rowspan="1" colspan="1"></td></tr><tr><td rowspan="1" colspan="1">III型前胶原N端肽</td><td rowspan="1" colspan="1">2024.5.19</td><td rowspan="1" colspan="2">96.46</td><td rowspan="1" colspan="2">mg/ml</td><td rowspan="1" colspan="2">0</td><td rowspan="1" colspan="1">15</td><td rowspan="1" colspan="1"></td></tr><tr><td rowspan="1" colspan="1">IV型胶原</td><td rowspan="1" colspan="1">2024.5.19</td><td rowspan="1" colspan="2">839</td><td rowspan="1" colspan="2">mg/ml</td><td rowspan="1" colspan="2">0</td><td rowspan="1" colspan="1">95</td><td rowspan="1" colspan="1"></td></tr></table>"""
    # merge_tag, final_table = cross_page_merge_by_html(pre_table, next_table)
    # table_list = [pre_table, next_table]
    table_list= ['<table><tr><td rowspan="1" colspan="1">SAE发生及处理的详细情况(如同时发生多个严重不良事件，可统一记录在此处)</td></tr><tr><td rowspan="1" colspan="1">[基本信息]受试者组本临床试验，于2024年04月02男，汉族，年龄：59岁，受试者编号 ，因治疗日签署知情同意书“探索儿研究”101,版本号：2.0版，版本日期：2023年06月16日)、完善筛选期检查，于2024.04.09第一次给予药物编号(方案编号为：为液10ml,于2024.05.09第二次给予F011人脐带间充质干细胞注射液10ml.【相关病史：合并疾病和既往病史]受试者的合并疾病，既往病史，台并用药情况己于上表合并用药部分详细表述。[不良事件情况]思者家属于2024.05.20晚上19:35告知患者于2024.05.18因人意识不清楚，大小便失禁在上海市奉贤区古华医院办理入院，2024.05.21追问患者家属，告知人清醒，反馈部分检查报告并获知诊断为肝损伤，入院后的相关检查具体异常值见表格。患者家属于2024.05.23晚19:25告知患者去世，2024.05.24早上家属微信反馈患者死亡小结，报告见附件。患者入院时主要症状及体征：患者因“乏力、纳差1月余”入院，查体：神志清晰，呼吸平稳，全身皮肤粘膜轻度黄染，全身浅表淋巴结无肿大，双巩膜明显黄染，颈软，无颈静脉充盈，无颈静脉怒张，两肺呼吸音粗，未闻及明显干湿啰音，心率90次/分，律齐，无杂音，腹部略膨隆，腹围97厘米，腹软，上腹部轻压痛，无腹部反跳痛，肝脏未触及，颈静脉回流征阴性，移动性浊音阳性，双下肢无浮肿。[不良事件的处理]截至SAE上报时家属未提供住院病历信息。2024.05.24早上家属微信反馈患者死亡小结，患者入院后完善检查，2024-05-20 CT检查报告：检查结果：1两局部肺通气不良：两肺局限性肺气肿：两肺间质纤维化：右肺门部淋巴结钙化：心影饱满：肺动脉干内径增宽：主动脉壁及冠脉局部钙化：两侧局部胸膜增厚、黏连；请结合临床及相关检查，随访。附见：奇静脉、半奇静脉扩张。2、肝脾肿大、肝硬化：脂肪肝，腹腔少量积液，餐后胆囊?肝胃间隙及腹膜后多发淋巴结及肿大：胃窦部、十二指肠局部肠壁增厚：请结合腹部增强检查。2024-05-21彩超检查报告：检查结果：肝脏部分显示肝呈慢性肝病改变(提示早期肝硬化)肝内结节形成中量腹水胆囊显示不清胰腺显示不清(建议消胀后复查)脾略大膀胱充盈差前列腺显示不清、给予保肝：注射用还原型谷胱甘肽、水飞蓟兵葡甲胺片；利胆退黄：丁二磺酸腺甘蛋氨酸、苦黄、熊去氧胆酸胶囊；护胃，保护胃黏膜：注射用法莫替丁：抗感染：头孢美唑；比阿培南：利尿：呋塞米、螺内酯：通便，乳果糖：营养支持，人血白蛋白；氨茶碱解痉平喘：肾衰竭对症活血补气，通腑泻浊；经治疗患者病情未见好转，请公共卫生临床中心钱志平主任会诊后建议加强抗感染，抗生素升级，保护肾功能，注意生命体征变化。与家属沟通后，告知病情，目前患者病情重，随时可出现生命危险，建议上级医院进一步诊治，患者表示拒绝，仍坚持住本院治疗，病程中患者测血压示80/30mmHG,呼吸明显急促，予以洛贝林及尼可刹米注射液、多巴胺及向羟胺注射液：升压及呼吸兴奋治疗，19:00左右患者家属即刻诉患者心电监护出现报警，心电监护仪呈一直线。即刻监测患者血压测不出，呼吸、脉搏、心率为0,血氧饱和度为0,大动脉搏动消失，双瞳孔散大固定，对光反射消失，测心电图示一直线。于2024.5.23,19:06宜告临床死亡。治疗结果：死亡[试验用药使用情况J受试者的试验药物使用情况己于上表试验用药部分详细表述。</td></tr></table>', '<table><tr><td rowspan="1" colspan="1">[对可疑产品采取的措施]不适用[不良事件转归至此报告时，患者有腹水，人清醒，仍住院，转归持续。2024.05.24早上家属微信反馈患者治疗结果，死亡。转归：死亡。[研究者相关性评价]此次SAE与研究药物人脐带间充质干细胞注射液可能无关，追溯病史，由于总者长期有腐肋肝病史，筛选期轻度肝功能不全， 不除患者二次给药后合并用药引起的肝功能损伤。总者本次住院痛胚抗原指标异常升高，不排除肿瘤可能。待患者反馈病历及报告后再避一步评价相关性。</td></tr><tr><td rowspan="1" colspan="1">2024.05.24结合患者家属提供死亡小结报告，根据患者检查结果及患者基础疾病，判定患者死亡与肝功能衰竭、肝硬化失代偿期、酒辅性肝病、胆汁淤积、自发性腹膜炎、肾功能不全、高血压、糖尿病、便秘、肺气肿、消化道肿瘤?均可能有关，与研究药物人脐带间充质干细胞注射液可能无关。人</td></tr></table>']
    total_table_html = merge_table_by_force(table_list)
    print(total_table_html)
