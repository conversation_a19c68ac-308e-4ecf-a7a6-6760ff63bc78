import json
import logging
from typing import List, Optional

from pydantic import BaseModel

from commons.tools.utils import Singleton
from commons.auth.auth_rpc import AuthRequest, SigVerType
from commons.trace.tracer import async_trace_span
from conf import ConfHttpTriton
from modules.entity.crop_entity import LayOutData


class SftOcrResponse(BaseModel):
    success: bool = False
    task_type: Optional[str] = None
    content: Optional[str] = None
    message: Optional[str] = None


class MonkeyOCRModelClient(metaclass=Singleton):
    def __init__(self):
        self._req: Optional[AuthRequest] = None

    def init(self, host: str, ak: str, sk: str, sig_type: SigVerType = SigVerType.wps2):
        self._req = AuthRequest(host, ak, sk, sig_type)

    @async_trace_span
    async def request_ocr(self, pic_url: str) -> SftOcrResponse:
        uri = ConfHttpTriton.monkey_ocr_uri
        body = {
            "file": pic_url
        }
        #'http://kmd-api.kas.wps.cn/api/11456-v1/Qa0N8/v1/ocr/text'
        status, text = await self._req.async_call("POST", uri, body=body)
        res = None
        if status == 200:
            res_dict = json.loads(text)
            res = SftOcrResponse.model_validate(res_dict)
        else:
            logging.error(f"Layout rpc fail, uri: {uri}, status: {status}, text: {text}")
        return res
