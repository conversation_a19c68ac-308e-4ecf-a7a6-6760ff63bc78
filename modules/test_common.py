from unittest import TestCase

from modules.common import calculate_original_bbox
from modules.entity.dst_entity import BBox


class Test(TestCase):
    def test_calculate_original_bbox(self):
        bbox = BBox(x1=1569, y1=1239, x2=15500, y2=3360, rotate=270)
        width = 11910
        height = 16840

        # Expected output
        expected_output = (1239, 1340, 3360, 15271)

        # Call the function
        result = calculate_original_bbox(bbox, width, height)
        print(result)
        # Assert the result
        self.assertEqual(result, expected_output)
