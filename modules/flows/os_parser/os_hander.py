# coding: utf-8
# <AUTHOR> linqi
# @date    : 2025/5/22 10:40
import asyncio

import requests
from io import BytesIO
import io
from PIL import Image
import fitz
import base64
import logging

from commons.trace.tracer import async_trace_span
from modules.pipeline.base import PipelineHandler
from modules.pipeline.context import PipelineContext, OcData
from services.datamodel import FileType
from modules.common import upload_image


# 开源(Open Source)解析handler
class OSParseNode(PipelineHandler):
    """KDC 数据获取处理节点"""

    def __init__(self, name: str = "kdc_data_processor"):
        super().__init__(name)
        self.name = name

    @async_trace_span
    async def process(self, context: PipelineContext) -> PipelineContext:
        """
        处理流程：
        1. 文件加载
        2. 版式判断
        3. 根据配置和版式选择解析路径
        4. 文件页开源解析
        """
        if context.file_info.file_type == FileType.PDF:
            image_urls = await self._process_pdf_and_upload(context.kdc_input.file_url_or_bytes)
            tasks = [self._use_oc_parser(image_url) for image_url in image_urls]
            oc_data_list = await asyncio.gather(*tasks, return_exceptions=True)
            valid_oc_data = []
            for idx, result in enumerate(oc_data_list):
                if isinstance(result, Exception):
                    # 记录异常但继续执行
                    logging.error(f"第 {idx + 1} 页执行开源解析任务执行失败: {result}")
                    continue
                valid_oc_data.append(result)

            context.oc_datas = valid_oc_data

        return context

    async def _process_pdf_and_upload(self, file_url_or_bytes):
        urls = []
        if isinstance(file_url_or_bytes, str):
            response = requests.get(file_url_or_bytes)
            response.raise_for_status()
            pdf_data = BytesIO(response.content)
        elif isinstance(file_url_or_bytes, bytes):
            pdf_data = BytesIO(file_url_or_bytes)
        else:
            raise ValueError("Invalid file_url_or_bytes format")

        pdf_document = fitz.open(stream=pdf_data, filetype="pdf")

        for page_number in range(len(pdf_document)):
            page = pdf_document.load_page(page_number)
            pix = page.get_pixmap()

            with io.BytesIO() as buffer:
                Image.frombytes("RGB", (pix.width, pix.height), pix.samples).save(buffer, format="PNG")
                img_base64 = base64.b64encode(buffer.getvalue()).decode("utf-8")
                url = upload_image(img_base64)
                if url:
                    urls.append(url)
        pdf_document.close()
        return urls

    async def _use_oc_parser(self, image_url: str) -> OcData:
        pass