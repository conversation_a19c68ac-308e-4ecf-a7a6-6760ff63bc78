# Author: linqi
# Date: 2025/7/10
# Time: 10:52
from io import BytesIO
from PIL import Image
import pymupdf
import requests
import fitz
import io
import base64
from typing import Union, Optional

from modules.entity.pre_check_entity import ParserConfig, ParserName
from modules.flows.pre_check.pre_check_template import PreCheckTemplate
from commons.trace.tracer import async_trace_span
from modules.common import _upload_image
from modules.pipeline.context import PipelineContext, FileInfo, FileType
from modules.rpc.ocr_model import OCRModelClient
from modules.common import get_fileinfo, open_pdf
from commons.logger.business_log import logger

scan_text_limit = 10


async def extract_and_upload_full_page_images(pdf_document: fitz.Document) -> list[dict]:
    """
    提取PDF中与页面尺寸匹配的图像并进行上传

    :param pdf_document: 已打开的PDF文档对象
    :return: 包含页码和图片URL的字典列表 [{"page_num": int, "image_url": str}]
    """
    cover_image = []
    try:
        for page_num in range(pdf_document.page_count):
            p = pdf_document.load_page(page_num)
            page_width = p.rect.width
            page_height = p.rect.height

            for img in p.get_images(full=True):
                xref = img[0]
                # 提取图像数据
                image_data = p.parent.extract_image(xref)
                image_bytes = io.BytesIO(image_data["image"])
                image = Image.open(image_bytes)
                image_width, image_height = image.size  # 获取图像原始尺寸

                # 判断是否与页面尺寸匹配
                if (image_width - page_width > 0 and image_height - page_height > 0) or (
                        page_width - image_width < 0.1 * page_width and page_height - image_height < 0.1 * page_height):
                    # 如果图像尺寸与页面尺寸相差不大，或者图片尺寸远大于页面尺寸，则认为是封面图
                    # 这里的判断条件可以根据实际需求调整

                    # 将图像转换为base64字符串
                    buffer = io.BytesIO()
                    image.save(buffer, format="PNG")
                    img_base64 = base64.b64encode(buffer.getvalue()).decode("utf-8")
                    buffer.close()

                    # 上传图像并获取URL
                    image_url = await _upload_image('', img_base64)
                    cover_image.append({
                        "page_num": p.number,
                        "image_url": image_url,
                    })

        return cover_image
    except Exception as e:
        logger.error(f"Error extracting images from PDF: {e}")
        return []


def find_flag_pages(pdf_document: fitz.Document) -> (list[int], list[int], str):
    """
    识别PDF文件中哪些页面只包含图片没有文字,和包括表格的页面

    :param pdf_document: 已打开的PDF文档对象
    :return: 只包含图片的页码列表,以及包含表格的页码列表
    """
    image_only_pages = []
    pages_with_tables = []
    full_text = ""
    # 遍历所有页面
    for page_num in range(pdf_document.page_count):
        page = pdf_document.load_page(page_num)

        # 提取页面文本（使用strip()去除空白字符）
        text = page.get_text().strip()
        if text:
            full_text = full_text + text
        # 检查页面是否包含图像
        images = page.get_images()
        has_images = len(images) > 0

        # 如果没有文本但有图像，则认为这一页全是图片
        if not text and has_images:
            image_only_pages.append(page_num)
        tables = page.find_tables()

        if tables.tables:
            pages_with_tables.append(page_num)

    return image_only_pages, pages_with_tables, full_text


def create_file_info_from_pdf(pdf_document: fitz.Document) -> FileInfo:
    """
    从 PDF 文档创建 FileInfo 对象

    :param pdf_document: 已打开的 PDF 文档
    :return: FileInfo 对象
    """
    # 初始化 FileInfo
    file_info = FileInfo(
        page_size=pdf_document.page_count,
        file_type=FileType.PDF,
        rotate_page={}
    )

    # 统计文字数量（简单估计）
    max_width = 0
    max_height = 0

    # 处理每个页面以获取宽度、高度和旋转信息
    for page_index in range(pdf_document.page_count):
        page = pdf_document.load_page(page_index)

        # 获取页面尺寸
        width = page.rect.width
        height = page.rect.height

        # 处理页面旋转
        rotate = page.rotation
        if rotate == 90:
            width, height = height, width
            file_info.rotate_page[90] = file_info.rotate_page.get(90, [])
            file_info.rotate_page[90].append(page_index)
        elif rotate == 180:
            file_info.rotate_page[180] = file_info.rotate_page.get(180, [])
            file_info.rotate_page[180].append(page_index)
        elif rotate == 270:
            width, height = height, width
            file_info.rotate_page[270] = file_info.rotate_page.get(270, [])
            file_info.rotate_page[270].append(page_index)

        # 更新最大宽高
        max_width = max(max_width, width)
        max_height = max(max_height, height)

    # 设置宽高和字数
    file_info.width = int(max_width)
    file_info.height = int(max_height)

    return file_info


class PDFPreCheck(PreCheckTemplate):

    @async_trace_span
    async def precheck_process(self, context: PipelineContext):
        """
        预检查PDF文件并确定哪些页面需要进行后续处理

        :param context: 上下文
        :return: 包含预检查结果的字典
        """

        file_url_or_bytes = context.kdc_input.file_url_or_bytes
        max_page_count = context.page_count
        # 初始化结果
        kdc_config = ParserConfig(
            parser_name=ParserName.KdcParser,
            is_all=False,
            processing_pages=set(),
        )

        # 检查页面数量
        file_page = await get_fileinfo(file_url_or_bytes)
        if max_page_count is not None and 0 < max_page_count < file_page:
            raise ValueError(
                f"File page count ({file_page}) exceeds limit ({max_page_count})")
        context.file_info.page_size = file_page
        # 只打开一次PDF文件
        pdf_document = await open_pdf(file_url_or_bytes)
        try:
            # 获取pdf的书签
            # 1. 识别只包含图片的页面
            image_only_pages, tables_pages, full_text = find_flag_pages(pdf_document)
            logger.info(f"Image-only pages: {image_only_pages}")

            file_info = create_file_info_from_pdf(pdf_document)

            context.file_info.page_size = file_info.page_size
            context.file_info.word_count = file_info.word_count
            context.file_info.width = file_info.width
            context.file_info.height = file_info.height
            context.file_info.rotate_page = file_info.rotate_page
            context.file_info.word_count = len(full_text)

            ##代表为全图片的pdf，可能是扫描件，可能是全图片的标准件 ,或者文本内容过少
            if file_page == len(image_only_pages) or len(full_text) / file_page < scan_text_limit:
                kdc_config.is_all = True
                context.file_info.is_scan = True
                return [kdc_config]

            # toc = pdf_document.get_toc()
            # if toc is None or len(toc) == 0:
            #     kdc_config.is_all = True
            #     return [kdc_config]
            ##图片页走kdc
            if image_only_pages:
                kdc_config.processing_pages.update(image_only_pages)

            # 2. 识别包含表格的页面
            logger.info(f"Table pages: {tables_pages}")
            if tables_pages:
                kdc_config.processing_pages.update(tables_pages)

            # 3. 提取与页面尺寸匹配的图像并检测表格类型
            cover_images = await extract_and_upload_full_page_images(pdf_document)
            if cover_images:
                logger.info(f"Cover images: {cover_images}")
                for img_info in cover_images:
                    page_num = img_info["page_num"]
                    img_url = img_info["image_url"]

                    # 调用OCR服务进行表格分类
                    table_cls = await OCRModelClient().request_table_cls(img_url)

                    # 如果检测到"wired"类型的表格，将该页标记为需要处理
                    if table_cls == "wired":
                        kdc_config.processing_pages.add(page_num)

        finally:
            # 确保PDF文档被关闭
            pdf_document.close()
        mupdf_page = set()
        # 遍历kdc_config的page，将未在里面的页码放到mupdf_config里
        kdc_config.processing_pages = sorted(kdc_config.processing_pages)
        for page_num in range(0, file_page):
            if page_num not in kdc_config.processing_pages:
                mupdf_page.add(page_num)
        has_kdc_pages = bool(kdc_config.processing_pages)
        has_mupdf_pages = bool(mupdf_page)

        mupdf_config = None
        if has_mupdf_pages:
            mupdf_config = ParserConfig(
                parser_name=ParserName.MuPdfParser,
                is_all=False,
                processing_pages=mupdf_page
            )

        if has_kdc_pages and has_mupdf_pages:
            return [kdc_config, mupdf_config]
        elif has_kdc_pages:
            return [kdc_config]
        else:
            return [mupdf_config]
