# Author: linqi
# Date: 2025/7/3
# Time: 16:08
import copy
from typing import List, Tuple

from modules.pipeline.base import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from commons.trace.tracer import async_trace_span
from commons.llm_gateway.llm import LLMChatStatus
from commons.logger.business_log import logger
from conf import ConfImageFilter
from modules.pipeline.context import PipelineContext
from modules.entity.dst_entity import DST, DSTType
from commons.thread.multicoroutine import MultiCoroutine
from conf import ConfCoroutine
from services.datamodel import ParseTarget
from modules.rpc.parse_res_rpc import RecallChunkClient, RecallChunkInput
from conf import ConfHandlerName

class ChunkRecallNode(PipelineHandler):
    def __init__(self, name: str):
        super().__init__(name)
        self.name = name

    @async_trace_span
    async def process(self, context: PipelineContext) -> PipelineContext:
        try:
            has_chunk_target = False
            for target in context.parse_target:
                if target == ParseTarget.chunk:
                    has_chunk_target = True
            if not has_chunk_target:
                res = await RecallChunkClient().request_parse_result(
                    RecallChunkInput(
                        drive_id=context.file_drive_id,
                        file_ids=[context.file_info.file_id],
                        with_content=True,
                        with_embedding=False,
                        with_fileinfo=True
                    ),
                    context.recall_chunk_header
                )
                if res is None:
                    error_msg = f"No chunk data found for the given file, file_id: {context.file_info.file_id}"
                    context.business_log.error(error_msg)
                    raise ValueError(error_msg)
                chunks = [chunk_info.chunk for chunk_info in res.chunks if chunk_info.chunk]
                context.chunks = chunks
                dsts = []
                for chunk in chunks:
                    dsts.extend(chunk.dsts)
                context.dst = dsts
                context.file_info = res.file_infos[0]
                context.handler_results[ConfHandlerName.dst_handler] = context.dst
                context.handler_results[ConfHandlerName.chunk_handler] = context.chunks

            return context
        except Exception as e:
            context.business_log.error(f"Error in DescNode.process: {e}")
            raise e