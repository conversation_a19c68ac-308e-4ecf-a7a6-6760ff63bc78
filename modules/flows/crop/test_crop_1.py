from unittest import TestCase
from unittest.mock import patch, MagicMock

from modules.entity.chunk_entity import Chunk, LabelType
from modules.entity.dst_entity import DST, DSTAttribute, BBox
from modules.entity.parse_entity import ImageType
from modules.flows.crop.crop import get_checkbox_crop


class Test(TestCase):
    @patch("modules.flows.crop.crop.fitz.open")
    @patch("modules.flows.crop.crop.upload_image")
    def test_get_checkbox_crop(self, mock_upload_image, mock_fitz_open):
        # Mock file_info
        file_info = MagicMock()
        file_info.file_type = "pdf"
        file_info.width = 1000
        file_info.height = 2000

        # Mock dst and chunk structure
        dst1 = DST(
            id="dst1",
            parent="-1",
            order=1,
            dst_type="text",
            attributes=DSTAttribute(
                level=0,
                position=BBox(x1=100, y1=100, x2=200, y2=200),
                page=0,
                hash="hash1sssasfasfassfasfasfgasgaasgasgasgasgagsags"
            ),
            content=["□ Option 1"],
            mark=None
        )
        dst2 = DST(
            id="dst2",
            parent="-1",
            order=2,
            dst_type="text",
            attributes=DSTAttribute(
                level=0,
                position=BBox(x1=300, y1=300, x2=400, y2=400),
                page=1,
                hash="hash2asfasfgasgasdgasgadgdasgsdgadsagasgasf"
            ),
            content=["☑ Option 2"],
            mark=None
        )
        chunk = Chunk(
            chunk_id="chunk1",
            page_size=1,
            content="□ Option 1 ☑ Option 2",
            label=LabelType.TEXT,
            page_num=[0],
            block=["dst1", "dst2"],
            dsts=[dst1, dst2]
        )

        # Mock fitz behavior
        mock_document = MagicMock()
        mock_page = MagicMock()
        mock_pixmap = MagicMock()
        mock_pixmap.width = 100
        mock_pixmap.height = 100
        mock_pixmap.samples = b"\xff" * (mock_pixmap.width * mock_pixmap.height * 3)

        mock_page.get_pixmap.return_value = mock_pixmap
        mock_document.load_page.return_value = mock_page
        mock_fitz_open.return_value = mock_document

        # Mock upload_image behavior
        mock_upload_image.return_value = "http://mocked_image_url"

        # Call the function
        result = get_checkbox_crop(file_url_or_bytes=b"mocked_pdf_bytes", file_info=file_info, dsts=[dst1, dst2])

        # Assertions
        self.assertEqual(len(result), 2)
        self.assertEqual(result[0].url, "http://mocked_image_url")
        self.assertEqual(result[0].page_num, 0)
        self.assertEqual(result[0].image_type, ImageType.CHECK_BOX_IMAGE)
        self.assertEqual(result[0].dst_ids, ["dst1"])

        self.assertEqual(result[1].url, "http://mocked_image_url")
        self.assertEqual(result[1].page_num, 1)
        self.assertEqual(result[1].image_type, ImageType.CHECK_BOX_IMAGE)
        self.assertEqual(result[1].dst_ids, ["dst2"])
