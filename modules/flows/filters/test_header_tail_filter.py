from unittest import TestCase

from modules.entity.dst_entity import DSTType, MarkType, DST, DSTAttribute, BBox
from modules.flows.filters.header_tail_filter import mark_page_header_tail_text


class Test(TestCase):
    def test_mark_page_header_tail_text(self):
        attributes1 = DSTAttribute(
            level=0,
            position=BBox(x1=2, y1=10, x2=100, y2=20),
            page=1,
            hash="hash1asfasfgasgasgasgasgasgasgasgagsasgash"
        )
        attributes2 = DSTAttribute(
            level=0,
            position=BBox(x1=2, y1=10, x2=100, y2=20),
            page=2,
            hash="hash2asgfasgasgagasgasgasgasgasgagsasggasa"
        )
        attributes3 = DSTAttribute(
            level=0,
            position=BBox(x1=2, y1=800, x2=100, y2=810),
            page=1,
            hash="hash3asgfasgasgasgasgasgasgsagasgasgasggas"
        )
        attributes4 = DSTAttribute(
            level=0,
            position=BBox(x1=2, y1=800, x2=100, y2=810),
            page=2,
            hash="hash4asgasgasggassafdsgdsgdshdhhsdhhddsd"
        )

        attributes5 = DSTAttribute(
            level=0,
            position=BBox(x1=2, y1=500, x2=100, y2=600),
            page=2,
            hash="hash4asgasgasggassafdsgdsgdshdhhsdhhddsd"
        )

        root_dst = DST(
            id="root_id",
            parent="-1",
            order=0,
            dst_type=DSTType.ROOT,
            attributes=DSTAttribute(
                level=0,
                position=BBox(x1=1, y1=1, x2=2, y2=2),
                page=0,
                hash="root_hashasfasfasfasgasdgfasgadsgagadsgdgdgsdsgdsg"
            ),
            content=["Root Node"]
        )
        # Create valid DST instances
        # Create valid DST instances with the required 'parent' field
        dst1 = DST(id="1", parent="root_id", order=0, dst_type=DSTType.TEXT, content=["Header"], attributes=attributes1)
        dst2 = DST(id="2", parent="root_id", order=0, dst_type=DSTType.TEXT, content=["Header"], attributes=attributes2)
        dst3 = DST(id="3", parent="root_id", order=0, dst_type=DSTType.TEXT, content=["content"], attributes=attributes5)
        dst4 = DST(id="4", parent="root_id", order=0, dst_type=DSTType.TEXT, content=["Footer"], attributes=attributes3)
        dst5 = DST(id="5", parent="root_id", order=0, dst_type=DSTType.TEXT, content=["Footer"], attributes=attributes4)
        dst_list = [dst1, dst2, dst3, dst4, dst5]

        # Call the function
        result = mark_page_header_tail_text(dst_list)

        # Assertions
        self.assertEqual(result[0].mark, MarkType.HEADER)
        self.assertEqual(result[1].mark, MarkType.HEADER)
        self.assertEqual(result[3].mark, MarkType.FOOTER)
        self.assertEqual(result[4].mark, MarkType.FOOTER)
