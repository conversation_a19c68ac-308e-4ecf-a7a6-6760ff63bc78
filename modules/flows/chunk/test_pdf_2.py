import re
from typing import List
from unittest import TestCase

from modules.entity.dst_entity import dst_to_json, DSTType, DST
from modules.flows.chunk.pdf import PDFChunk
from modules.flows.chunk.test_docx import convert_to_dst_list


def get_pdf_table_dst_2():
    return  [
    {
        "id": "3bfbf47549e84fa2b05ca160e06dedcf",
        "parent": "-1",
        "order": 0,
        "dst_type": "root",
        "attributes": {
            "level": 0,
            "position": {
                "x1": 1,
                "y1": 2,
                "x2": 3,
                "y2": 4
            },
            "page": 0,
            "hash": "roothashhashhashhashhashhashhashhashhashhash"
        },
        "content": [
            "根节点"
        ],
        "mark": None
    },
    {
        "id": "4455945261fd4956b7f834162a02ae03",
        "parent": "3bfbf47549e84fa2b05ca160e06dedcf",
        "order": 0,
        "dst_type": "image",
        "attributes": {
            "level": 10,
            "position": {
                "x1": 14599,
                "y1": 900,
                "x2": 15520,
                "y2": 1819
            },
            "page": 0,
            "hash": "519106facfe1cfc9dddd300c6b98539a29dc4722"
        },
        "content": [
            "http://zhai-platereduction.ks3-cn-beijing.ksyun.com/tmp/layout/2025-05-07/39232aa996f54aedacb2415410e20435/kdc_cut0.png?Expires=1746669844&AWSAccessKeyId=AKLThacEYfpQEiYtqqtfXFZP&Signature=y6ojNOuicVwdnAAMq3WNkurprXk%3D"
        ],
        "mark": None
    },
    {
        "id": "fac4c20ad8d84db08e4ac47fe359dc5f",
        "parent": "3bfbf47549e84fa2b05ca160e06dedcf",
        "order": 0,
        "dst_type": "image",
        "attributes": {
            "level": 10,
            "position": {
                "x1": 11219,
                "y1": 959,
                "x2": 13759,
                "y2": 1720
            },
            "page": 0,
            "hash": "82957bd9908ce72a43dd2fe21091bf762f3b82d5"
        },
        "content": [
            "http://zhai-platereduction.ks3-cn-beijing.ksyun.com/tmp/layout/2025-05-07/39232aa996f54aedacb2415410e20435/kdc_cut1.png?Expires=1746669844&AWSAccessKeyId=AKLThacEYfpQEiYtqqtfXFZP&Signature=nNfW%2BgjSRf1U4%2B7fr5KYGtaIUc4%3D"
        ],
        "mark": None
    },
    {
        "id": "d9c52ea6a2f04ffba6d556ec923b0545",
        "parent": "3bfbf47549e84fa2b05ca160e06dedcf",
        "order": 0,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "x1": 9859,
                "y1": 1489,
                "x2": 11119,
                "y2": 1769
            },
            "page": 0,
            "hash": "05545f0fc9067ab7e0d4d90d876214d50ee74f1e"
        },
        "content": [
            "合同编号："
        ],
        "mark": None
    },
    {
        "id": "c874b8685f7d46eb808da9a04ca1f4ea",
        "parent": "3bfbf47549e84fa2b05ca160e06dedcf",
        "order": 0,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "x1": 11429,
                "y1": 1650,
                "x2": 13430,
                "y2": 1809
            },
            "page": 0,
            "hash": "dcf74166b9d858259d41621f7f18a6baa4b344ae"
        },
        "content": [
            "HQDX02300684BCNOO-NM00140"
        ],
        "mark": None
    },
    {
        "id": "b5d6eb732bed4eccabc57cb2e8586b94",
        "parent": "3bfbf47549e84fa2b05ca160e06dedcf",
        "order": 0,
        "dst_type": "table",
        "attributes": {
            "level": 10,
            "position": {
                "x1": 2129,
                "y1": 1950,
                "x2": 15459,
                "y2": 10370
            },
            "page": 0,
            "hash": "0ea7ff937fb1efcb63c47f50d8bacd7ec1cc6222"
        },
        "content": [
            "<table><tr><td rowspan=\"1\" colspan=\"12\">中国电信PON设备(2023年)集中采购设备及相关服务采购框架协议一华为订单</td></tr><tr><td rowspan=\"1\" colspan=\"5\">设备框架协议合同编号：HQDX02300684BCNO0</td><td rowspan=\"1\" colspan=\"7\">设备采购订单合同编号：/</td></tr><tr><td rowspan=\"1\" colspan=\"5\">附属框架协议合同编号：HQDX02000290BCY00</td><td rowspan=\"1\" colspan=\"7\"></td></tr><tr><td rowspan=\"1\" colspan=\"5\">采购方：中国电信集团有限公司内蒙古分公司地址：/联系人：郭雷电话：1533558046</td><td rowspan=\"1\" colspan=\"7\">供应商(乙方):华为技术有限公司地址：/联系人：马龙电话：/089868548808统一社会信用代码：914403001922038216开户行：中国工商银行股份有限公司深圳华为支行银行地址：广东深圳中国工商银行股份有限公司深圳华为支行户名：华为技术有限公司账号：4000020309024500386</td></tr><tr><td rowspan=\"1\" colspan=\"3\">采购方财务信息采购方名称(发票抬头):中国电信集团有限公司内蒙古分公司统一社会信用代码：开户行：/银行地址：/户名：/账号：/</td><td rowspan=\"1\" colspan=\"4\">工程项目信息建设单位名称：阿拉善分公司*工程项目名称：内蒙古电信2024年西部盟市阿盟千兆网络升级项目工程项目编号：24NM001973001工程项目负责人：何瑞电话：/发货通知单编号：NMDD202412300040</td><td rowspan=\"1\" colspan=\"5\">送货信息物流承运商：中通服供应链管理有限公司到货时间：2025-01-25送货地址：巴彦浩特镇北环路宏达建筑公司电信大库收货人：何瑞电话：18947430905</td></tr><tr><td rowspan=\"1\" colspan=\"5\">货物</td><td rowspan=\"1\" colspan=\"7\">费明细</td></tr><tr><td rowspan=\"1\" colspan=\"1\">序号</td><td rowspan=\"1\" colspan=\"1\">货物名称(物料名称)</td><td rowspan=\"1\" colspan=\"1\">货物编码(物料编码)</td><td rowspan=\"1\" colspan=\"1\">原厂商规格(型号</td><td rowspan=\"1\" colspan=\"1\">备注</td><td rowspan=\"1\" colspan=\"1\">计量单位</td><td rowspan=\"1\" colspan=\"1\">单价价款</td><td rowspan=\"1\" colspan=\"1\">采购数量</td><td rowspan=\"1\" colspan=\"1\">价款小计</td><td rowspan=\"1\" colspan=\"1\">税率</td><td rowspan=\"1\" colspan=\"1\">税款小计</td><td rowspan=\"1\" colspan=\"1\">价税合计</td></tr><tr><td rowspan=\"1\" colspan=\"1\">1</td><td rowspan=\"1\" colspan=\"1\">OLT用户板-XGPON接口板-D2光模块-16端口</td><td rowspan=\"1\" colspan=\"1\">J03031000015</td><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\">/</td><td rowspan=\"1\" colspan=\"1\">块</td><td rowspan=\"1\" colspan=\"1\">22122.000000</td><td rowspan=\"1\" colspan=\"1\">3</td><td rowspan=\"1\" colspan=\"1\">81596.00</td><td rowspan=\"1\" colspan=\"1\">13.0%</td><td rowspan=\"1\" colspan=\"1\">11104.48</td><td rowspan=\"1\" colspan=\"1\">97740.48</td></tr><tr><td rowspan=\"1\" colspan=\"8\">货物费合计金额：</td><td rowspan=\"1\" colspan=\"1\">8156.00</td><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\">11445.48</td><td rowspan=\"1\" colspan=\"1\">97740.48</td></tr><tr><td rowspan=\"1\" colspan=\"12\">服务费明细</td></tr></table>"
        ],
        "mark": None
    },
    {
        "id": "6957a9dca04d41d2a545abe7624127ba",
        "parent": "3bfbf47549e84fa2b05ca160e06dedcf",
        "order": 0,
        "dst_type": "image",
        "attributes": {
            "level": 10,
            "position": {
                "x1": 14569,
                "y1": 890,
                "x2": 15500,
                "y2": 1819
            },
            "page": 1,
            "hash": "eaaf957ccc99ee7178d1d598ad805cd853ce4518"
        },
        "content": [
            "http://zhai-platereduction.ks3-cn-beijing.ksyun.com/tmp/layout/2025-05-07/39232aa996f54aedacb2415410e20435/kdc_cut2.png?Expires=1746669844&AWSAccessKeyId=AKLThacEYfpQEiYtqqtfXFZP&Signature=Gzz7AUDVFoIv3VlGqGTY2EVkaPY%3D"
        ],
        "mark": None
    },
    {
        "id": "925284befdde4011b7cea3654085baf1",
        "parent": "3bfbf47549e84fa2b05ca160e06dedcf",
        "order": 0,
        "dst_type": "image",
        "attributes": {
            "level": 10,
            "position": {
                "x1": 11060,
                "y1": 930,
                "x2": 13700,
                "y2": 1750
            },
            "page": 1,
            "hash": "6866405eb22de059fd47862dbaf10b8526214a50"
        },
        "content": [
            "http://zhai-platereduction.ks3-cn-beijing.ksyun.com/tmp/layout/2025-05-07/39232aa996f54aedacb2415410e20435/kdc_cut3.png?Expires=1746669844&AWSAccessKeyId=AKLThacEYfpQEiYtqqtfXFZP&Signature=qberSaGlhPpgB5vppfDWbP%2FwaNM%3D"
        ],
        "mark": None
    },
    {
        "id": "90ca84eb94854afc93c745b061a4a5dd",
        "parent": "3bfbf47549e84fa2b05ca160e06dedcf",
        "order": 0,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "x1": 9729,
                "y1": 1509,
                "x2": 10959,
                "y2": 1789
            },
            "page": 1,
            "hash": "05545f0fc9067ab7e0d4d90d876214d50ee74f1e"
        },
        "content": [
            "合同编号："
        ],
        "mark": None
    },
    {
        "id": "c724747e659448f6a104232849e7a6de",
        "parent": "3bfbf47549e84fa2b05ca160e06dedcf",
        "order": 0,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "x1": 11320,
                "y1": 1660,
                "x2": 13440,
                "y2": 1829
            },
            "page": 1,
            "hash": "dcf74166b9d858259d41621f7f18a6baa4b344ae"
        },
        "content": [
            "HQDX02300684BCNOO-NM00140"
        ],
        "mark": None
    },
    {
        "id": "a41b238fe6864dc49ec9eb6ea20353fc",
        "parent": "3bfbf47549e84fa2b05ca160e06dedcf",
        "order": 0,
        "dst_type": "table",
        "attributes": {
            "level": 10,
            "position": {
                "x1": 1820,
                "y1": 1960,
                "x2": 15390,
                "y2": 10539
            },
            "page": 1,
            "hash": "5f4d12df5c362302cd79fe130376e29a010fb4de"
        },
        "content": [
            "<table><tr><td rowspan=\"1\" colspan=\"1\">序号</td><td rowspan=\"1\" colspan=\"1\">费用类型</td><td rowspan=\"1\" colspan=\"1\">服务供应商</td><td rowspan=\"1\" colspan=\"1\">费率</td><td rowspan=\"1\" colspan=\"1\">单价价款</td><td rowspan=\"1\" colspan=\"1\">采购数量</td><td rowspan=\"1\" colspan=\"1\">价款小计</td><td rowspan=\"1\" colspan=\"1\">税率</td><td rowspan=\"1\" colspan=\"1\">税款小计</td><td rowspan=\"1\" colspan=\"1\">价税合计</td></tr><tr><td rowspan=\"1\" colspan=\"1\">1</td><td rowspan=\"1\" colspan=\"1\">安装督导费</td><td rowspan=\"1\" colspan=\"1\">华为技术有限公司</td><td rowspan=\"1\" colspan=\"1\">0.1%</td><td rowspan=\"1\" colspan=\"1\">/</td><td rowspan=\"1\" colspan=\"1\">/</td><td rowspan=\"1\" colspan=\"1\">86.50</td><td rowspan=\"1\" colspan=\"1\">13.0%</td><td rowspan=\"1\" colspan=\"1\">11.24</td><td rowspan=\"1\" colspan=\"1\">97.74</td></tr><tr><td rowspan=\"1\" colspan=\"1\">2</td><td rowspan=\"1\" colspan=\"1\">端到端配送费</td><td rowspan=\"1\" colspan=\"1\">中通服供应链管理有限公司</td><td rowspan=\"1\" colspan=\"1\">1.7%</td><td rowspan=\"1\" colspan=\"1\">/</td><td rowspan=\"1\" colspan=\"1\">/</td><td rowspan=\"1\" colspan=\"1\">1427.18</td><td rowspan=\"1\" colspan=\"1\">9.0%</td><td rowspan=\"1\" colspan=\"1\">128.45</td><td rowspan=\"1\" colspan=\"1\">1555.63</td></tr><tr><td rowspan=\"1\" colspan=\"6\">服务费合计金额：</td><td rowspan=\"1\" colspan=\"1\">86.50</td><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\">11.24</td><td rowspan=\"1\" colspan=\"1\">97.74</td></tr><tr><td rowspan=\"1\" colspan=\"10\">费用总计</td></tr><tr><td rowspan=\"1\" colspan=\"4\">费用类型</td><td rowspan=\"1\" colspan=\"1\">价款</td><td rowspan=\"1\" colspan=\"2\">税款</td><td rowspan=\"1\" colspan=\"3\">价税合计</td></tr><tr><td rowspan=\"1\" colspan=\"4\">货物费总金额：</td><td rowspan=\"1\" colspan=\"1\">86496.00</td><td rowspan=\"1\" colspan=\"2\">11244.48</td><td rowspan=\"1\" colspan=\"3\">97740.48</td></tr><tr><td rowspan=\"1\" colspan=\"4\">服务费总金额：</td><td rowspan=\"1\" colspan=\"1\">86.50</td><td rowspan=\"1\" colspan=\"2\">11.24</td><td rowspan=\"1\" colspan=\"3\">97.74</td></tr><tr><td rowspan=\"1\" colspan=\"4\">订单总金额(人民币小写):</td><td rowspan=\"1\" colspan=\"1\">86582.50</td><td rowspan=\"1\" colspan=\"2\">11255.72</td><td rowspan=\"1\" colspan=\"3\">97838.22</td></tr><tr><td rowspan=\"1\" colspan=\"4\">订单总金额(人民币大写):</td><td rowspan=\"1\" colspan=\"6\">价款：捌万陆仟伍佰捌拾贰元伍角税款：壹万壹仟贰佰伍拾伍元柒角贰分价税合计：玖万柒仟捌佰叁拾捌元贰角贰分</td></tr><tr><td rowspan=\"1\" colspan=\"10\">备注：、采购方和供应商(乙方)确认：本订单受框架协议的约束。与本订单有关的争议将按照框架协议的约定解决。2、下单配置明细信息表示该下单配置中具体组件，下单配置明细金额之和等于下单配置金额。3、实际发票抬头信息与收货信息以本订单约定的建设单位具体要求为准。4、本订单货币单位为人民币(元)。</td></tr><tr><td rowspan=\"1\" colspan=\"10\"></td></tr></table>"
        ],
        "mark": None
    },
    {
        "id": "860c60836b7448b0a6c97adc8c8cb36c",
        "parent": "3bfbf47549e84fa2b05ca160e06dedcf",
        "order": 0,
        "dst_type": "image",
        "attributes": {
            "level": 10,
            "position": {
                "x1": 14599,
                "y1": 880,
                "x2": 15550,
                "y2": 1829
            },
            "page": 2,
            "hash": "e5946156b818b570f18146a237edf908642f7e0f"
        },
        "content": [
            "http://zhai-platereduction.ks3-cn-beijing.ksyun.com/tmp/layout/2025-05-07/39232aa996f54aedacb2415410e20435/kdc_cut6.png?Expires=1746669844&AWSAccessKeyId=AKLThacEYfpQEiYtqqtfXFZP&Signature=TDfE3IVRQbGsjIE2nsPJEL6hO1w%3D"
        ],
        "mark": None
    },
    {
        "id": "1ab613b09f3c478a99c316637ce60547",
        "parent": "3bfbf47549e84fa2b05ca160e06dedcf",
        "order": 0,
        "dst_type": "image",
        "attributes": {
            "level": 10,
            "position": {
                "x1": 11089,
                "y1": 930,
                "x2": 13739,
                "y2": 1750
            },
            "page": 2,
            "hash": "e37e2abe240b9f5bbfdc7284c081a4e0a7414af3"
        },
        "content": [
            "http://zhai-platereduction.ks3-cn-beijing.ksyun.com/tmp/layout/2025-05-07/39232aa996f54aedacb2415410e20435/kdc_cut7.png?Expires=1746669844&AWSAccessKeyId=AKLThacEYfpQEiYtqqtfXFZP&Signature=IvzqdY2aYxePbC%2B6nTMQWxnuPPg%3D"
        ],
        "mark": None
    },
    {
        "id": "ea8729a5810448de846f36e810aba37d",
        "parent": "3bfbf47549e84fa2b05ca160e06dedcf",
        "order": 0,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "x1": 9750,
                "y1": 1499,
                "x2": 10980,
                "y2": 1779
            },
            "page": 2,
            "hash": "05545f0fc9067ab7e0d4d90d876214d50ee74f1e"
        },
        "content": [
            "合同编号："
        ],
        "mark": None
    },
    {
        "id": "13ec8ffeee7643bcaa071294a60dce21",
        "parent": "3bfbf47549e84fa2b05ca160e06dedcf",
        "order": 0,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "x1": 11340,
                "y1": 1640,
                "x2": 13460,
                "y2": 1809
            },
            "page": 2,
            "hash": "dcf74166b9d858259d41621f7f18a6baa4b344ae"
        },
        "content": [
            "HQDX02300684BCNOO-NM00140"
        ],
        "mark": None
    },
    {
        "id": "7a05b6265da4447a877c3e181ba16ed4",
        "parent": "3bfbf47549e84fa2b05ca160e06dedcf",
        "order": 0,
        "dst_type": "table",
        "attributes": {
            "level": 10,
            "position": {
                "x1": 1779,
                "y1": 1950,
                "x2": 15429,
                "y2": 5160
            },
            "page": 2,
            "hash": "fe9c4f17c9a1b8be99ac9bc24ea8e9171d024c08"
        },
        "content": [
            "<table><tr><td rowspan=\"1\" colspan=\"1\">采购方(盖章):中国电信集团有限公司内蒙古分公司法定代表人/负责人或授权代表(签字):</td><td rowspan=\"1\" colspan=\"1\">供应商(乙方)旦确认上述内容。供应商(乙方)(益章):华为技术有限公司法定代表人/负责人或授权代扳(签字)印</td></tr><tr><td rowspan=\"1\" colspan=\"2\">2024-12-31签署日期：</td></tr></table>"
        ],
        "mark": None
    },
    {
        "id": "25e6e050fc614296becb94e482ccaaca",
        "parent": "3bfbf47549e84fa2b05ca160e06dedcf",
        "order": 0,
        "dst_type": "image",
        "attributes": {
            "level": 10,
            "position": {
                "x1": 3790,
                "y1": 2510,
                "x2": 5859,
                "y2": 4569
            },
            "page": 2,
            "hash": "e7544828eba690089e8f47f3d3060541bf74c975"
        },
        "content": [
            "http://zhai-platereduction.ks3-cn-beijing.ksyun.com/tmp/layout/tmp/2025-05-07/png/bd52f132-7bae-44fd-bbf1-251b113dd729.png?AWSAccessKeyId=AKLThacEYfpQEiYtqqtfXFZP&Signature=121%2B061qRFXp4gM3EYg44DpuxMk%3D&Expires=1746669844"
        ],
        "mark": None
    },
    {
        "id": "7cb7788ba39146ec81b430607776e446",
        "parent": "3bfbf47549e84fa2b05ca160e06dedcf",
        "order": 0,
        "dst_type": "image",
        "attributes": {
            "level": 10,
            "position": {
                "x1": 9469,
                "y1": 2000,
                "x2": 11660,
                "y2": 4199
            },
            "page": 2,
            "hash": "1c41c0808ad1e0901e0ea56e2610655a92c15f6e"
        },
        "content": [
            "http://zhai-platereduction.ks3-cn-beijing.ksyun.com/tmp/layout/tmp/2025-05-07/png/9de445c1-6aca-49e4-84ac-1f0ca6f9fdab.png?AWSAccessKeyId=AKLThacEYfpQEiYtqqtfXFZP&Signature=jljoHXM0%2Bo8Gy5RCzKuuNoi1b9I%3D&Expires=1746669844"
        ],
        "mark": None
    }
]



class TestPDFChunk(TestCase):
    def setUp(self):
        self.parser = PDFChunk()
        self.kdc = get_pdf_table_dst_2()

    def test_process_chunks(self):
        dstList = convert_to_dst_list(self.kdc)
        chunks = self.parser.process_chunks(dstList, 2)

        print(dst_to_json(chunks))
