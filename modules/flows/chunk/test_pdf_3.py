import re
from typing import List
from unittest import TestCase

from modules.entity.dst_entity import dst_to_json, DSTType, DST
from modules.flows.chunk.pdf import PDFChunk
from modules.flows.chunk.test_docx import convert_to_dst_list


def get_pdf_table_dst_3():
    return [
            {
                "id": "fbfef97d5e1d45edb7af0af6e1dbdce6",
                "parent": "-1",
                "order": 0,
                "dst_type": "root",
                "attributes": {
                    "level": 0,
                    "position": {
                        "x1": 1,
                        "y1": 2,
                        "x2": 3,
                        "y2": 4
                    },
                    "page": 0,
                    "hash": "roothashhashhashhashhashhashhashhashhashhash"
                },
                "content": [
                    "根节点"
                ],
                "mark": None
            },
            {
                "id": "9bf00a244b844fcab798523fca26f472",
                "parent": "fbfef97d5e1d45edb7af0af6e1dbdce6",
                "order": 0,
                "dst_type": "text",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 1812,
                        "y1": 2116,
                        "x2": 2849,
                        "y2": 2325
                    },
                    "page": 0,
                    "hash": "c388cdc2f06e839c9562d1349fb8fff55bf77d41"
                },
                "content": [
                    "正文在哪里"
                ],
                "mark": None
            },
            {
                "id": "48d44df0be3248c4b8d4da522a303d83",
                "parent": "fbfef97d5e1d45edb7af0af6e1dbdce6",
                "order": 1,
                "dst_type": "text",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 5541,
                        "y1": 2428,
                        "x2": 6370,
                        "y2": 2637
                    },
                    "page": 0,
                    "hash": "5222aba97615966f4a4cdab21e0f1df7d636c468"
                },
                "content": [
                    "测试文件"
                ],
                "mark": None
            },
            {
                "id": "d60aa831771f4bf29f70fb3fb79862ec",
                "parent": "fbfef97d5e1d45edb7af0af6e1dbdce6",
                "order": 2,
                "dst_type": "text",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 1818,
                        "y1": 2740,
                        "x2": 3375,
                        "y2": 2949
                    },
                    "page": 0,
                    "hash": "fc4d23543167be2d405d05d1b0d54f2155600d4c"
                },
                "content": [
                    "1",
                    "、这是一个目录"
                ],
                "mark": None
            },
            {
                "id": "2d558f8e525a4e549808b5d25f8dc718",
                "parent": "fbfef97d5e1d45edb7af0af6e1dbdce6",
                "order": 3,
                "dst_type": "text",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 2957,
                        "y1": 3330,
                        "x2": 9785,
                        "y2": 3549
                    },
                    "page": 0,
                    "hash": "16fd943466bf4ce5dfeacf220e2359c13d047fa0"
                },
                "content": [
                    "具体内容如下：件、",
                    " pdf",
                    " 扫描件，会走自研的",
                    " pdfplusparser",
                    " 模块和",
                    " kdc",
                    " 模块"
                ],
                "mark": None
            },
            {
                "id": "9893b444beb34b3da1a0e0359f94b169",
                "parent": "fbfef97d5e1d45edb7af0af6e1dbdce6",
                "order": 4,
                "dst_type": "text",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 3675,
                        "y1": 3923,
                        "x2": 10059,
                        "y2": 4444
                    },
                    "page": 0,
                    "hash": "a1cdb6203ac6dfb657a324e586f4cab4c3b41fb1"
                },
                "content": [
                    "为了解决",
                    " kdc",
                    " 在表格上解析能力不足的问题，会先走",
                    " pdfplusparser",
                    " 模块，",
                    "识别表格页面"
                ],
                "mark": None
            },
            {
                "id": "32768fcbc99342f394b7674ca4e8c3cf",
                "parent": "fbfef97d5e1d45edb7af0af6e1dbdce6",
                "order": 5,
                "dst_type": "text",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 3674,
                        "y1": 4828,
                        "x2": 7255,
                        "y2": 5044
                    },
                    "page": 0,
                    "hash": "66ec5abb814cd2e6c92801d77e187ad9a0d8211b"
                },
                "content": [
                    "有表格的页面继续走",
                    " pdfplusparser",
                    " 模块"
                ],
                "mark": None
            },
            {
                "id": "fbcfc8a956de40d5b8989c0824a61a96",
                "parent": "fbfef97d5e1d45edb7af0af6e1dbdce6",
                "order": 6,
                "dst_type": "text",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 3070,
                        "y1": 5418,
                        "x2": 9931,
                        "y2": 6236
                    },
                    "page": 0,
                    "hash": "b02bbd631e9111d5507f6eccab0d744ad397e604"
                },
                "content": [
                    "无表格的页面走",
                    " kdc",
                    " 模块，因为",
                    " kdc",
                    " 比我们自研的",
                    " pdfplusparser",
                    " 要快",
                    "其次，将解析的内容按照规则切分"
                ],
                "mark": None
            },
            {
                "id": "973180c996024b178c973b2bf8be5137",
                "parent": "fbfef97d5e1d45edb7af0af6e1dbdce6",
                "order": 7,
                "dst_type": "text",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 2951,
                        "y1": 6604,
                        "x2": 5883,
                        "y2": 6813
                    },
                    "page": 0,
                    "hash": "24af56e03aa237300118dbd502f081038d9e4dad"
                },
                "content": [
                    "将解析后的内容，按照段落切分"
                ],
                "mark": None
            },
            {
                "id": "0550f69a21034be4a6e6e877f2d2aad6",
                "parent": "fbfef97d5e1d45edb7af0af6e1dbdce6",
                "order": 8,
                "dst_type": "text",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 2951,
                        "y1": 7194,
                        "x2": 6514,
                        "y2": 7403
                    },
                    "page": 0,
                    "hash": "7d81ba75920dad8207d90232f7fbeb62a97f3890"
                },
                "content": [
                    "将段落内的表格、图片与纯文本切分开"
                ],
                "mark": None
            },
            {
                "id": "93e3acba9cf04dda9ae9b2d7dee7e8a8",
                "parent": "fbfef97d5e1d45edb7af0af6e1dbdce6",
                "order": 9,
                "dst_type": "text",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 2952,
                        "y1": 7787,
                        "x2": 5404,
                        "y2": 7996
                    },
                    "page": 0,
                    "hash": "c2360309ca92cf8d2f74d01af6979ad52c165a23"
                },
                "content": [
                    "表格、图片自成一个",
                    " chunk"
                ],
                "mark": None
            },
            {
                "id": "ba78426f28cb4d92a557b7bcf0acf434",
                "parent": "fbfef97d5e1d45edb7af0af6e1dbdce6",
                "order": 10,
                "dst_type": "text",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 3069,
                        "y1": 8365,
                        "x2": 5940,
                        "y2": 8605
                    },
                    "page": 0,
                    "hash": "7de34cf8a25f00448592876a07ea3862517f772c"
                },
                "content": [
                    "第三，表格、图片信息增强"
                ],
                "mark": None
            },
            {
                "id": "3553ac5d659b4ee18d9326b9979448f3",
                "parent": "fbfef97d5e1d45edb7af0af6e1dbdce6",
                "order": 11,
                "dst_type": "text",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 2952,
                        "y1": 8970,
                        "x2": 4834,
                        "y2": 9179
                    },
                    "page": 0,
                    "hash": "f2b62c0d6b8da4bc4e8087a8d0be474ac840c5cc"
                },
                "content": [
                    "对表格生成表格向量"
                ],
                "mark": None
            },
            {
                "id": "4dde6becf7cd4975814f43fe8e4e5d9b",
                "parent": "fbfef97d5e1d45edb7af0af6e1dbdce6",
                "order": 12,
                "dst_type": "text",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 2952,
                        "y1": 9563,
                        "x2": 4834,
                        "y2": 9772
                    },
                    "page": 0,
                    "hash": "56db08429988a04e50f1800561f05cdfa9c1cb80"
                },
                "content": [
                    "对图片生成图片描述"
                ],
                "mark": None
            },
            {
                "id": "3174a171a2d743e6a58197102c205456",
                "parent": "fbfef97d5e1d45edb7af0af6e1dbdce6",
                "order": 13,
                "dst_type": "text",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 2952,
                        "y1": 10156,
                        "x2": 5254,
                        "y2": 10365
                    },
                    "page": 0,
                    "hash": "32e5ea3456c730827958961a73e528b5b9c02b75"
                },
                "content": [
                    "对图片描述生成图片向量"
                ],
                "mark": None
            },
            {
                "id": "a5a2973563db4888bc4d6f5eef3b93eb",
                "parent": "fbfef97d5e1d45edb7af0af6e1dbdce6",
                "order": 14,
                "dst_type": "text",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 1816,
                        "y1": 11224,
                        "x2": 2638,
                        "y2": 11433
                    },
                    "page": 0,
                    "hash": "625893e96cb5fc494009845ff7399b29b247da71"
                },
                "content": [
                    "☑",
                    "正确的"
                ],
                "mark": None
            },
            {
                "id": "50889ea5c6c14658b34148fc55198e57",
                "parent": "fbfef97d5e1d45edb7af0af6e1dbdce6",
                "order": 15,
                "dst_type": "text",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 1812,
                        "y1": 11728,
                        "x2": 1980,
                        "y2": 11865
                    },
                    "page": 0,
                    "hash": "6fe8fffc97792201fc0332cd69c36a8f9efb6826"
                },
                "content": [
                    "2",
                    "、"
                ],
                "mark": None
            },
            {
                "id": "acc5bdf435ff47ccb51c69f8033c50fd",
                "parent": "fbfef97d5e1d45edb7af0af6e1dbdce6",
                "order": 16,
                "dst_type": "text",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 1816,
                        "y1": 12160,
                        "x2": 2849,
                        "y2": 12369
                    },
                    "page": 0,
                    "hash": "2b0aa5ebb9646ac8f79a2f6d522c667c31f3d47a"
                },
                "content": [
                    "☑",
                    "错误的",
                    "的"
                ],
                "mark": None
            },
            {
                "id": "17e6828d7d6345da8ac3b300d95d19b7",
                "parent": "fbfef97d5e1d45edb7af0af6e1dbdce6",
                "order": 17,
                "dst_type": "text",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 1816,
                        "y1": 12784,
                        "x2": 2638,
                        "y2": 12993
                    },
                    "page": 0,
                    "hash": "625893e96cb5fc494009845ff7399b29b247da71"
                },
                "content": [
                    "☑",
                    "正确的"
                ],
                "mark": None
            },
            {
                "id": "666cb481d91e4ab0b8448a5916a0044d",
                "parent": "fbfef97d5e1d45edb7af0af6e1dbdce6",
                "order": 18,
                "dst_type": "text",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 1810,
                        "y1": 13288,
                        "x2": 1980,
                        "y2": 13426
                    },
                    "page": 0,
                    "hash": "7dd6ea5c8bca12dea17ff11b1ee0164cdfa21510"
                },
                "content": [
                    "3",
                    "、"
                ],
                "mark": None
            },
            {
                "id": "205ed20e267d4919ac739333916c68c6",
                "parent": "fbfef97d5e1d45edb7af0af6e1dbdce6",
                "order": 19,
                "dst_type": "table",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 1687,
                        "y1": 13816,
                        "x2": 10219,
                        "y2": 15092
                    },
                    "page": 0,
                    "hash": "4feb6a678e421c1db3793eaf9e1422c1736eb389"
                },
                "content": [
                    "<table><tr><td rowspan=\"1\" colspan=\"1\">这 是 一 个表格</td><td rowspan=\"1\" colspan=\"1\">☑正确的</td><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\">☑正确的</td><td rowspan=\"1\" colspan=\"1\"></td></tr><tr><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\"></td></tr></table>"
                ],
                "mark": None
            },
            {
                "id": "9932e93d96784480901595adff5e8023",
                "parent": "fbfef97d5e1d45edb7af0af6e1dbdce6",
                "order": 20,
                "dst_type": "table",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 1687,
                        "y1": 1440,
                        "x2": 10219,
                        "y2": 3040
                    },
                    "page": 1,
                    "hash": "32e424dd2411e8e380000a25c42bfa727bffc3d1"
                },
                "content": [
                    "<table><tr><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\"></td></tr><tr><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\"></td></tr><tr><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\">☑正确的</td><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\"></td></tr></table>"
                ],
                "mark": None
            },
            {
                "id": "344dd5e58ff0408f8efb6ec2d0315442",
                "parent": "fbfef97d5e1d45edb7af0af6e1dbdce6",
                "order": 21,
                "dst_type": "text",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 1812,
                        "y1": 3714,
                        "x2": 2949,
                        "y2": 3923
                    },
                    "page": 1,
                    "hash": "c75665988c55ec28a719e7eae5372cbba5d915b2"
                },
                "content": [
                    "正文内容呢？"
                ],
                "mark": None
            }
        ]


class TestPDFChunk(TestCase):
    def setUp(self):
        self.parser = PDFChunk()
        self.kdc = get_pdf_table_dst_3()

    def test_process_chunks(self):
        dstList = convert_to_dst_list(self.kdc)
        chunks = self.parser.process_chunks(dstList, 2)

        print(dst_to_json(chunks))
