from unittest import TestCase

from modules.entity.dst_entity import dst_to_json
from modules.flows.chunk.pdf import PDFChunk
from modules.flows.chunk.test_docx import convert_to_dst_list


def get_pdf_table_dst():
    return [
        {
            "id": "b06a64b839384b13a3f0f3092bdc1456",
            "parent": "-1",
            "order": 0,
            "dst_type": "root",
            "attributes": {
                "level": 0,
                "position": {
                    "x1": 1,
                    "y1": 2,
                    "x2": 3,
                    "y2": 4
                },
                "page": 0,
                "hash": "roothashhashhashhashhashhashhashhashhashhash"
            },
            "content": [
                "根节点"
            ]
        },
        {
            "id": "368f44c19c24446ca5e19969026a2a21",
            "parent": "b06a64b839384b13a3f0f3092bdc1456",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 1,
                "position": {
                    "x1": 4529,
                    "y1": 1659,
                    "x2": 7100,
                    "y2": 1880
                },
                "page": 0,
                "hash": "4064ba470b0238abb0b2cef599be2af73c5e1fb1"
            },
            "content": [
                "严重不良事件报告表(SAE)"
            ]
        },
        {
            "id": "2cbf56dc2740409fb0648d6f65892042",
            "parent": "368f44c19c24446ca5e19969026a2a21",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 1720,
                    "y1": 2120,
                    "x2": 3460,
                    "y2": 2329
                },
                "page": 0,
                "hash": "b3738ae370d8f4e6f4eae65b72854ed593a3d15a"
            },
            "content": [
                "新药临床批准文号："
            ]
        },
        {
            "id": "50d27e20df584949bceb522a81204169",
            "parent": "368f44c19c24446ca5e19969026a2a21",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 1730,
                    "y1": 2589,
                    "x2": 2610,
                    "y2": 2800
                },
                "page": 0,
                "hash": "accd586953f0a3af5146fd11c26bc3dcd6f54ea4"
            },
            "content": [
                "报告类型："
            ]
        },
        {
            "id": "c410dbaba2dc4c2da957ea1734ae491b",
            "parent": "368f44c19c24446ca5e19969026a2a21",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 3069,
                    "y1": 2589,
                    "x2": 3679,
                    "y2": 2809
                },
                "page": 0,
                "hash": "3a327ec24a380b7a7ccd050dd105f3788c9e2bbe"
            },
            "content": [
                "冈首次"
            ]
        },
        {
            "id": "84175567ea614d4e8a72b34c8117d6bc",
            "parent": "368f44c19c24446ca5e19969026a2a21",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 3879,
                    "y1": 2589,
                    "x2": 5159,
                    "y2": 2800
                },
                "page": 0,
                "hash": "a35c6f67bd4a3295ac2faef47aeb970b2b14c7c7"
            },
            "content": [
                "□随访□总结"
            ]
        },
        {
            "id": "2265e5a2ffd34ebc9799f5fc3ac02318",
            "parent": "368f44c19c24446ca5e19969026a2a21",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 6889,
                    "y1": 2600,
                    "x2": 9619,
                    "y2": 2809
                },
                "page": 0,
                "hash": "d383cec761ce2be00c71b7e8cdc0c30b3d17f1fd"
            },
            "content": [
                "报告时间：2023年10月30日"
            ]
        },
        {
            "id": "c6c819684c4b4075af7a835e91d1fd8b",
            "parent": "368f44c19c24446ca5e19969026a2a21",
            "order": 0,
            "dst_type": "table",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 1730,
                    "y1": 2940,
                    "x2": 9919,
                    "y2": 15270
                },
                "page": 0,
                "hash": "b68f8d30297c0272e4c1b1e70af100d3ff633b02"
            },
            "content": [
                "<table><tr><td rowspan=\"1\" colspan=\"4\">临床项目及报告单位信息</td></tr><tr><td rowspan=\"1\" colspan=\"1\">医疗机构及专业名称</td><td rowspan=\"1\" colspan=\"1\">测试医院骨髓移植科</td><td rowspan=\"1\" colspan=\"1\">电话</td><td rowspan=\"1\" colspan=\"1\">022-12345675</td></tr><tr><td rowspan=\"1\" colspan=\"1\">申报单位名称</td><td rowspan=\"1\" colspan=\"1\">凯诺医药</td><td rowspan=\"1\" colspan=\"1\">电话</td><td rowspan=\"1\" colspan=\"1\">+86-12345678</td></tr><tr><td rowspan=\"1\" colspan=\"1\">临床研究方案名称</td><td rowspan=\"1\" colspan=\"3\">测试试验药品治疗肿瘤的临床试验方案</td></tr><tr><td rowspan=\"1\" colspan=\"1\">临床研究方案号</td><td rowspan=\"1\" colspan=\"3\">Clin-001</td></tr><tr><td rowspan=\"1\" colspan=\"1\">临床适应症</td><td rowspan=\"1\" colspan=\"3\">肿瘤</td></tr><tr><td rowspan=\"1\" colspan=\"1\">临床研究分类</td><td rowspan=\"1\" colspan=\"3\">□Ⅲ期□Ⅱ期区I期□临床验□生物等效性试验□IV期证类试验</td></tr><tr><td rowspan=\"1\" colspan=\"1\">试验盲态情况</td><td rowspan=\"1\" colspan=\"3\">年 月 日)□盲态(□未破盲□已盲态-破盲时间：区非盲态</td></tr><tr><td rowspan=\"1\" colspan=\"4\">报告者信息</td></tr><tr><td rowspan=\"1\" colspan=\"1\">报告者姓名</td><td rowspan=\"1\" colspan=\"1\">张三</td><td rowspan=\"1\" colspan=\"1\">所在国家</td><td rowspan=\"1\" colspan=\"1\">中国</td></tr><tr><td rowspan=\"1\" colspan=\"1\">职业</td><td rowspan=\"1\" colspan=\"1\">医生</td><td rowspan=\"1\" colspan=\"1\">电话</td><td rowspan=\"1\" colspan=\"1\"></td></tr><tr><td rowspan=\"1\" colspan=\"1\">获知SAE时间</td><td rowspan=\"1\" colspan=\"3\">冈首次获知时间2023年  10月30日□随访信息获知时间___年___月__日</td></tr><tr><td rowspan=\"1\" colspan=\"4\">受试者信息</td></tr></table>"
            ]
        },
        {
            "id": "3d6531daba1c4de0be6ad5cf98cb7f00",
            "parent": "368f44c19c24446ca5e19969026a2a21",
            "order": 0,
            "dst_type": "table",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 1710,
                    "y1": 1559,
                    "x2": 9879,
                    "y2": 15249
                },
                "page": 1,
                "hash": "5c2d3ef608b1d36476d0ecbb7f562c389651d1b3"
            },
            "content": [
                "<table><tr><td rowspan=\"1\" colspan=\"1\">姓名缩写</td><td rowspan=\"1\" colspan=\"1\">LS</td><td rowspan=\"1\" colspan=\"1\">性别</td><td rowspan=\"1\" colspan=\"2\">男</td><td rowspan=\"1\" colspan=\"1\">出生日期</td><td rowspan=\"1\" colspan=\"1\">1965年11月27日</td><td rowspan=\"1\" colspan=\"2\">发生SAE时的年龄</td><td rowspan=\"1\" colspan=\"1\">58岁</td></tr><tr><td rowspan=\"1\" colspan=\"1\">受试者编号</td><td rowspan=\"1\" colspan=\"1\">1001</td><td rowspan=\"1\" colspan=\"1\">民族</td><td rowspan=\"1\" colspan=\"2\">汉</td><td rowspan=\"1\" colspan=\"1\">身高(cm)</td><td rowspan=\"1\" colspan=\"1\">180</td><td rowspan=\"1\" colspan=\"2\">体重(kg)</td><td rowspan=\"1\" colspan=\"1\">50</td></tr><tr><td rowspan=\"2\" colspan=\"1\">患者死亡</td><td rowspan=\"1\" colspan=\"9\">□否</td></tr><tr><td rowspan=\"1\" colspan=\"1\">☑是</td><td rowspan=\"1\" colspan=\"1\">死亡日期</td><td rowspan=\"1\" colspan=\"1\">2023年月25曰</td><td rowspan=\"1\" colspan=\"1\">死亡原因</td><td rowspan=\"1\" colspan=\"1\">未知</td><td rowspan=\"1\" colspan=\"2\">是否尸检</td><td rowspan=\"1\" colspan=\"2\">□否□是尸检结果</td></tr><tr><td rowspan=\"1\" colspan=\"10\">现病史(试验用药适应症以外，SAE发生时未恢复的疾病)</td></tr><tr><td rowspan=\"1\" colspan=\"1\">疾病名称</td><td rowspan=\"1\" colspan=\"2\">开始日期</td><td rowspan=\"1\" colspan=\"2\">结束日期</td><td rowspan=\"1\" colspan=\"2\">是否为家族史</td><td rowspan=\"1\" colspan=\"3\">备注</td></tr><tr><td rowspan=\"1\" colspan=\"1\">EBV血症</td><td rowspan=\"1\" colspan=\"2\">2023.4.28</td><td rowspan=\"1\" colspan=\"2\"></td><td rowspan=\"1\" colspan=\"2\">NA</td><td rowspan=\"1\" colspan=\"3\"></td></tr><tr><td rowspan=\"1\" colspan=\"1\">贫血</td><td rowspan=\"1\" colspan=\"2\">2023.4.22</td><td rowspan=\"1\" colspan=\"2\"></td><td rowspan=\"1\" colspan=\"2\">NA</td><td rowspan=\"1\" colspan=\"3\"></td></tr><tr><td rowspan=\"1\" colspan=\"1\">免疫力下降</td><td rowspan=\"1\" colspan=\"2\">2023.3.30</td><td rowspan=\"1\" colspan=\"2\"></td><td rowspan=\"1\" colspan=\"2\">NA</td><td rowspan=\"1\" colspan=\"3\"></td></tr><tr><td rowspan=\"1\" colspan=\"1\">急性淋巴细胞白血病</td><td rowspan=\"1\" colspan=\"2\">2022.11.UK</td><td rowspan=\"1\" colspan=\"2\"></td><td rowspan=\"1\" colspan=\"2\">NA</td><td rowspan=\"1\" colspan=\"3\"></td></tr><tr><td rowspan=\"1\" colspan=\"1\">泌尿系BK/JC病毒感染</td><td rowspan=\"1\" colspan=\"2\">2023.4.17</td><td rowspan=\"1\" colspan=\"2\"></td><td rowspan=\"1\" colspan=\"2\">NA</td><td rowspan=\"1\" colspan=\"3\"></td></tr><tr><td rowspan=\"1\" colspan=\"10\">既往病史(SAE发生前已恢复的疾病)</td></tr></table>"
            ]
        },
        {
            "id": "13bb8842237845cbb40fcae62ebbc41e",
            "parent": "368f44c19c24446ca5e19969026a2a21",
            "order": 0,
            "dst_type": "table",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 1700,
                    "y1": 1530,
                    "x2": 9899,
                    "y2": 14880
                },
                "page": 2,
                "hash": "1b71e6930d2303b5d7d726fe5a8c5ccd06586e98"
            },
            "content": [
                "<table><tr><td rowspan=\"1\" colspan=\"1\">疾病名称</td><td rowspan=\"1\" colspan=\"2\">开始日期</td><td rowspan=\"1\" colspan=\"1\">结束日期</td><td rowspan=\"1\" colspan=\"2\">是否为家族史</td><td rowspan=\"1\" colspan=\"3\">备注</td></tr><tr><td rowspan=\"1\" colspan=\"1\">肺结核</td><td rowspan=\"1\" colspan=\"2\">2001.5.26</td><td rowspan=\"1\" colspan=\"1\">2005.8.6</td><td rowspan=\"1\" colspan=\"2\"></td><td rowspan=\"1\" colspan=\"3\"></td></tr><tr><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"2\"></td><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"2\"></td><td rowspan=\"1\" colspan=\"3\"></td></tr><tr><td rowspan=\"1\" colspan=\"9\">过敏史：□有   (过敏原：区无_)</td></tr><tr><td rowspan=\"1\" colspan=\"9\">饮酒史：□无  区有  __饮酒10年</td></tr><tr><td rowspan=\"1\" colspan=\"9\">图无吸烟史：□有     吸烟20年，每日约5只____</td></tr><tr><td rowspan=\"1\" colspan=\"9\">□有其他：区无</td></tr><tr><td rowspan=\"1\" colspan=\"9\">相关实验室检查(如适用，选填)</td></tr><tr><td rowspan=\"1\" colspan=\"1\">检查项</td><td rowspan=\"1\" colspan=\"1\">检查日期</td><td rowspan=\"1\" colspan=\"3\">检查结果</td><td rowspan=\"1\" colspan=\"1\">单位</td><td rowspan=\"1\" colspan=\"1\">正常值上限</td><td rowspan=\"1\" colspan=\"1\">正常值下限</td><td rowspan=\"1\" colspan=\"1\">备注</td></tr><tr><td rowspan=\"1\" colspan=\"1\">白细胞计数</td><td rowspan=\"1\" colspan=\"1\">2023.4.10</td><td rowspan=\"1\" colspan=\"3\">1.61</td><td rowspan=\"1\" colspan=\"1\">10*9/L</td><td rowspan=\"1\" colspan=\"1\">4</td><td rowspan=\"1\" colspan=\"1\">10</td><td rowspan=\"1\" colspan=\"1\">↓</td></tr><tr><td rowspan=\"1\" colspan=\"1\">血红蛋白</td><td rowspan=\"1\" colspan=\"1\">2023.4.3</td><td rowspan=\"1\" colspan=\"3\">45</td><td rowspan=\"1\" colspan=\"1\">g/L</td><td rowspan=\"1\" colspan=\"1\">131</td><td rowspan=\"1\" colspan=\"1\">172</td><td rowspan=\"1\" colspan=\"1\">CS</td></tr><tr><td rowspan=\"1\" colspan=\"1\">肺部CT平扫</td><td rowspan=\"1\" colspan=\"1\">2023年8月10日</td><td rowspan=\"1\" colspan=\"3\">两肺炎症，两侧胸腔积液，右侧胸腔积血考虑，伴两肺萎缩，以右肺为主。</td><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\"></td></tr></table>"
            ]
        },
        {
            "id": "34ce1aadfd4449ec814328402bfc85ae",
            "parent": "368f44c19c24446ca5e19969026a2a21",
            "order": 0,
            "dst_type": "table",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 1710,
                    "y1": 2019,
                    "x2": 9990,
                    "y2": 10089
                },
                "page": 3,
                "hash": "a70cfc95a05e02317415e737c4fa705a452a4d57"
            },
            "content": [
                "<table><tr><td rowspan=\"1\" colspan=\"4\">试验用药信息 口未使用试验药品 区已使用试验药品(如有多个试验用药，请复制此表格添加。如果是盲态试验：1.未破盲：试验用药品中文名称、英文名称请填写NA 2.已破盲：试验用药品中文名称、英文名称请填写具体)。</td></tr><tr><td rowspan=\"1\" colspan=\"1\">药物中文名称</td><td rowspan=\"1\" colspan=\"1\">测试试验药品</td><td rowspan=\"1\" colspan=\"1\">药物英文名称</td><td rowspan=\"1\" colspan=\"1\">Test study drug</td></tr><tr><td rowspan=\"1\" colspan=\"1\">活性成分</td><td rowspan=\"1\" colspan=\"1\">注射用间充质干细胞</td><td rowspan=\"1\" colspan=\"1\">剂型</td><td rowspan=\"1\" colspan=\"1\">注射剂</td></tr><tr><td rowspan=\"1\" colspan=\"1\">给药剂量</td><td rowspan=\"1\" colspan=\"1\">1.0^6个细胞/kg</td><td rowspan=\"1\" colspan=\"1\">给药途径</td><td rowspan=\"1\" colspan=\"1\">静脉滴注</td></tr><tr><td rowspan=\"1\" colspan=\"1\">给药频次</td><td rowspan=\"1\" colspan=\"1\">3-4天/次</td><td rowspan=\"1\" colspan=\"1\">累计总剂量</td><td rowspan=\"1\" colspan=\"1\">100ml</td></tr><tr><td rowspan=\"1\" colspan=\"1\">药物编号</td><td rowspan=\"1\" colspan=\"1\">NA</td><td rowspan=\"1\" colspan=\"1\">规格</td><td rowspan=\"1\" colspan=\"1\">5ml:1.0x10^7细胞</td></tr><tr><td rowspan=\"1\" colspan=\"1\">生产厂家</td><td rowspan=\"1\" colspan=\"1\">凯诺医药</td><td rowspan=\"1\" colspan=\"1\">临床试验适应症</td><td rowspan=\"1\" colspan=\"1\">肿瘤</td></tr><tr><td rowspan=\"1\" colspan=\"1\">药品分类</td><td rowspan=\"1\" colspan=\"3\">□中药□化药  治疗用生物制品□预防用生物制品</td></tr><tr><td rowspan=\"1\" colspan=\"1\">用药时间</td><td rowspan=\"1\" colspan=\"3\">2023年04月03日10时40分-2023年04月20日10__时30分</td></tr><tr><td rowspan=\"1\" colspan=\"1\">用药处置</td><td rowspan=\"1\" colspan=\"3\">□不详  口停止用药□剂量不□增加剂量□减少剂量变 凶不适用</td></tr></table>"
            ]
        },
        {
            "id": "c749df754f134ed4803a4e40e230ae60",
            "parent": "368f44c19c24446ca5e19969026a2a21",
            "order": 0,
            "dst_type": "table",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 1700,
                    "y1": 10570,
                    "x2": 10010,
                    "y2": 15329
                },
                "page": 3,
                "hash": "ad1c36510687c4fdabc2fd9e221fff753e37a2b7"
            },
            "content": [
                "<table><tr><td rowspan=\"1\" colspan=\"5\">既往/合并用药信息(既往用药收集既往使用且在严重不良事件发生前已停用的药物，至少收集最早严重不良事件发生日期至14天前使用的药品/疫苗，无法明确得知使用期间的药品也记录在此；合并用药收集不良事件发生期间使用的药品；针对SAE的治疗用药，请填写在最后的“SAE发生及处理的详细情况”栏。)</td></tr><tr><td rowspan=\"1\" colspan=\"1\">药物通用名称</td><td rowspan=\"1\" colspan=\"1\">使用原因</td><td rowspan=\"1\" colspan=\"1\">剂量</td><td rowspan=\"1\" colspan=\"1\">开始日期</td><td rowspan=\"1\" colspan=\"1\">结束日期</td></tr><tr><td rowspan=\"1\" colspan=\"1\">异甘草酸镁针</td><td rowspan=\"1\" colspan=\"1\">护肝</td><td rowspan=\"1\" colspan=\"1\">100mg</td><td rowspan=\"1\" colspan=\"1\">2023.8.4</td><td rowspan=\"1\" colspan=\"1\">2023.8.20</td></tr><tr><td rowspan=\"1\" colspan=\"1\">谷胱甘肽针</td><td rowspan=\"1\" colspan=\"1\">护肝</td><td rowspan=\"1\" colspan=\"1\">600mg</td><td rowspan=\"1\" colspan=\"1\">2023.8.3</td><td rowspan=\"1\" colspan=\"1\">2023.8.20</td></tr><tr><td rowspan=\"1\" colspan=\"1\">奥美拉唑钠针</td><td rowspan=\"1\" colspan=\"1\">护胃</td><td rowspan=\"1\" colspan=\"1\">60mg</td><td rowspan=\"1\" colspan=\"1\">2023.8.4</td><td rowspan=\"1\" colspan=\"1\">2023.8.11</td></tr><tr><td rowspan=\"1\" colspan=\"1\">更昔洛韦胶囊</td><td rowspan=\"1\" colspan=\"1\">治疗EBV血症</td><td rowspan=\"1\" colspan=\"1\">500mg</td><td rowspan=\"1\" colspan=\"1\">2023.8.4</td><td rowspan=\"1\" colspan=\"1\">2023.8.20</td></tr></table>"
            ]
        },
        {
            "id": "fafa8a68b9c24219afa0dd255c613d4d",
            "parent": "368f44c19c24446ca5e19969026a2a21",
            "order": 0,
            "dst_type": "table",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 1710,
                    "y1": 1559,
                    "x2": 9990,
                    "y2": 6779
                },
                "page": 4,
                "hash": "572fcbf127db0c79078a0471d00361ee297ae9be"
            },
            "content": [
                "<table><tr><td rowspan=\"1\" colspan=\"1\">甲钴胺片</td><td rowspan=\"1\" colspan=\"1\">营养</td><td rowspan=\"1\" colspan=\"1\">0.5mg</td><td rowspan=\"1\" colspan=\"1\">2023.8.3</td><td rowspan=\"1\" colspan=\"1\">2023.8.20</td></tr><tr><td rowspan=\"1\" colspan=\"1\">氯化钾缓释片</td><td rowspan=\"1\" colspan=\"1\">预防低钾</td><td rowspan=\"1\" colspan=\"1\">0.5g</td><td rowspan=\"1\" colspan=\"1\">2023.8.4</td><td rowspan=\"1\" colspan=\"1\">2023.8.20</td></tr><tr><td rowspan=\"1\" colspan=\"1\">复方磺胺甲噁唑片</td><td rowspan=\"1\" colspan=\"1\">抗感染</td><td rowspan=\"1\" colspan=\"1\">0.96g</td><td rowspan=\"1\" colspan=\"1\">2023.8.3</td><td rowspan=\"1\" colspan=\"1\">2023.8.4</td></tr><tr><td rowspan=\"1\" colspan=\"1\">氟康唑胶囊</td><td rowspan=\"1\" colspan=\"1\">抗感染</td><td rowspan=\"1\" colspan=\"1\">100mg</td><td rowspan=\"1\" colspan=\"1\">2023.8.3</td><td rowspan=\"1\" colspan=\"1\">2023.8.17</td></tr><tr><td rowspan=\"1\" colspan=\"1\">复合维生素片</td><td rowspan=\"1\" colspan=\"1\">营养</td><td rowspan=\"1\" colspan=\"1\">1片</td><td rowspan=\"1\" colspan=\"1\">2023.8.3</td><td rowspan=\"1\" colspan=\"1\">2023.8.4</td></tr><tr><td rowspan=\"1\" colspan=\"1\">碳酸钙D3片</td><td rowspan=\"1\" colspan=\"1\">预防低钙</td><td rowspan=\"1\" colspan=\"1\">600mg</td><td rowspan=\"1\" colspan=\"1\">2023.8.4</td><td rowspan=\"1\" colspan=\"1\">2023.8.20</td></tr><tr><td rowspan=\"1\" colspan=\"1\">双歧杆菌三联活菌胶囊</td><td rowspan=\"1\" colspan=\"1\">调节肠道</td><td rowspan=\"1\" colspan=\"1\">420mg</td><td rowspan=\"1\" colspan=\"1\">2023.8.4</td><td rowspan=\"1\" colspan=\"1\">2023.8.20</td></tr><tr><td rowspan=\"1\" colspan=\"1\">康复新液</td><td rowspan=\"1\" colspan=\"1\">预防口腔感染</td><td rowspan=\"1\" colspan=\"1\">10ml</td><td rowspan=\"1\" colspan=\"1\">2023.8.4</td><td rowspan=\"1\" colspan=\"1\">2023.8.15</td></tr><tr><td rowspan=\"1\" colspan=\"1\">恩替卡韦分散片</td><td rowspan=\"1\" colspan=\"1\">治疗EBV血症</td><td rowspan=\"1\" colspan=\"1\">0.5mg</td><td rowspan=\"1\" colspan=\"1\">2023.8.5</td><td rowspan=\"1\" colspan=\"1\">2023.8.20</td></tr></table>"
            ]
        },
        {
            "id": "4373b0b9bfe1408ab32d8fb4129d10e7",
            "parent": "368f44c19c24446ca5e19969026a2a21",
            "order": 0,
            "dst_type": "table",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 1710,
                    "y1": 7270,
                    "x2": 10010,
                    "y2": 15320
                },
                "page": 4,
                "hash": "32ca1e6ba143058f759db7ec4df340a62fee511d"
            },
            "content": [
                "<table><tr><td rowspan=\"1\" colspan=\"4\">严重不良事件信息(如同时发生多个严重不良事件，请复制此表格添加)</td></tr><tr><td rowspan=\"1\" colspan=\"1\">不良事件名称(诊断)</td><td rowspan=\"1\" colspan=\"3\">不明原因死亡</td></tr><tr><td rowspan=\"1\" colspan=\"1\">起止时间</td><td rowspan=\"1\" colspan=\"3\">2023年10月25日UK时UK  分-2023年10月25日UK时  UK分</td></tr><tr><td rowspan=\"1\" colspan=\"1\">持续时间</td><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\">CTCAE分级</td><td rowspan=\"1\" colspan=\"1\">5</td></tr><tr><td rowspan=\"1\" colspan=\"1\">严重性标准</td><td rowspan=\"1\" colspan=\"1\">区导致死亡□功能丧失/致残□危及生命□导致住院/延长住院时间□导致先天性异常/出生缺陷□其他重要医学事件</td><td rowspan=\"1\" colspan=\"1\">转归</td><td rowspan=\"1\" colspan=\"1\">□痊愈□好转/缓解□未好转/未缓解/持续□痊愈伴有后遗症区致死□未知</td></tr><tr><td rowspan=\"1\" colspan=\"1\">国内SAE报道</td><td rowspan=\"1\" colspan=\"1\">□不详□有 区无</td><td rowspan=\"1\" colspan=\"1\">国外SAE报道</td><td rowspan=\"1\" colspan=\"1\">□有  区无  □不详</td></tr></table>"
            ]
        },
        {
            "id": "af787edc4ed14f5f8a018fec265d6266",
            "parent": "368f44c19c24446ca5e19969026a2a21",
            "order": 0,
            "dst_type": "table",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 1710,
                    "y1": 1550,
                    "x2": 9980,
                    "y2": 5810
                },
                "page": 5,
                "hash": "5b7eb7513a1263476a02ac57c0e40c567f1d1053"
            },
            "content": [
                "<table><tr><td rowspan=\"1\" colspan=\"3\">相关性评价(盲态试验未破盲时，名称填写试验用药；若为联合用药，对不同成分分别进行评价)</td></tr><tr><td rowspan=\"1\" colspan=\"1\">药品名</td><td rowspan=\"1\" colspan=\"2\">测试试验药品</td></tr><tr><td rowspan=\"1\" colspan=\"1\">相关性评价</td><td rowspan=\"1\" colspan=\"2\">□可能相关□很可能相关□肯定相关□可能无关区不相关</td></tr><tr><td rowspan=\"1\" colspan=\"2\">停药或减量后，反应是否消失或减轻?</td><td rowspan=\"1\" colspan=\"1\">□是□否□不详冈不适用</td></tr><tr><td rowspan=\"1\" colspan=\"2\">再次使用试验药品后，是否再次出现同样反应?</td><td rowspan=\"1\" colspan=\"1\">□是   □否   □不详  区不适用(未重新给药)</td></tr></table>"
            ]
        },
        {
            "id": "8828df9905ba4197a7a1d05b8d01abc7",
            "parent": "368f44c19c24446ca5e19969026a2a21",
            "order": 0,
            "dst_type": "table",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 1589,
                    "y1": 6290,
                    "x2": 9990,
                    "y2": 15229
                },
                "page": 5,
                "hash": "d2660bd404d94651575bc2b2baf6eacf1760ccef"
            },
            "content": [
                "<table><tr><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"2\">SAE发生及处理的详细情况(如同时发生多个严重不良事件，可统一记录在此处)</td></tr><tr><td rowspan=\"1\" colspan=\"2\">01中心，受试者LS,筛选号：S01001,入组号：1001,于2023年3月30日签署知情同意书(版本号：2.0(测试医院专用版),版本日期：2022年10月10号，于2023年4月3日入组“测试试验药品治疗肿瘤的临床试验方案”临床研究，2023年4月3日至2023年4月20日共行8次注射试验药品治疗，输注期间未见明显异常。2023年8月20日，行外科胸腔探查止血术，术后转ICU监护治疗，休克难以纠正，代酸加重，血色素进行性降低，腹腔诊断性穿刺为血性液体，考虑患者仍有活动性出血，立即行开腹探查术，术中见腹腔大量积血，见右肝穿刺点活动性出血，右侧胸壁穿刺点少量渗血，术后送ICU密切监测生命体征治疗。复查头颅CT提示右侧顶枕颞叶、两侧小脑半球多发低密度影，脑梗考虑。2023.8.25,予芦可替尼抗排异，并予甲泼尼龙针20mg qd联合抗排异治疗，予舒普深抗感染、肠内、肠外营养、化痰、抑酸、营养神经、补充肠道益生菌等对症支持治疗，行纤支镜检查吸痰，见气道壁病变，考虑真菌感染，加用卡泊芬净抗真菌治疗，因患者艰难梭菌感染，2023.8.31加用万古霉素抗感染治疗。后根据患者炎症指标及体温等相关感染症状调整抗生素使用，先后予头孢呋辛、舒普深、特治星、美罗培南、奥马环素抗感染治疗。2023.9.13腹部CT示：腹盆腔积液，腹膜系膜水肿，较2023.8.29CT:盆腔积液较前减少。受试者于2023.10.12出院。2023.10.30获知患者于2023.10.25死亡，患者家属拒绝提供患者出院后相关医疗材料及死亡证明，具体死亡原因未知，于2023.10.31上报不明原因SAE首次报告，CTCAE:</td><td rowspan=\"1\" colspan=\"1\"></td></tr></table>"
            ]
        },
        {
            "id": "d1bc47130e1344f9a17861c45433aa8c",
            "parent": "368f44c19c24446ca5e19969026a2a21",
            "order": 0,
            "dst_type": "table",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 1629,
                    "y1": 1530,
                    "x2": 9889,
                    "y2": 5170
                },
                "page": 6,
                "hash": "94debacf0669377446bcdef80dc57fae542d35d4"
            },
            "content": [
                "<table><tr><td rowspan=\"1\" colspan=\"1\">5级，与研究药物：不相关，对研究药物处理措施为：不适用。</td></tr></table>"
            ]
        },
        {
            "id": "060770a820994077ac2bc4933424e294",
            "parent": "368f44c19c24446ca5e19969026a2a21",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 3150,
                    "y1": 5190,
                    "x2": 3909,
                    "y2": 5589
                },
                "page": 6,
                "hash": "ced07fb42b05a2ed9efa330250e2bb9175f962ce"
            },
            "content": [
                "张三"
            ]
        },
        {
            "id": "9c429801d33e491981a92d8bb85200be",
            "parent": "368f44c19c24446ca5e19969026a2a21",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 5910,
                    "y1": 5219,
                    "x2": 7279,
                    "y2": 5539
                },
                "page": 6,
                "hash": "611e057fbc79e7c201215ba87b43c4d78eb63dab"
            },
            "content": [
                "、"
            ]
        },
        {
            "id": "57d9aae8c732451fa0e9e5d7ca55f4dc",
            "parent": "368f44c19c24446ca5e19969026a2a21",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 1730,
                    "y1": 5299,
                    "x2": 4789,
                    "y2": 5520
                },
                "page": 6,
                "hash": "eeb5e21dabb049aad0bd0f4f4fa805cd18af4981"
            },
            "content": [
                "报告者签名：                "
            ]
        },
        {
            "id": "8f774a2098174035b1f8a4654100cb6f",
            "parent": "368f44c19c24446ca5e19969026a2a21",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 5119,
                    "y1": 5299,
                    "x2": 7519,
                    "y2": 5520
                },
                "page": 6,
                "hash": "274f49fa0a841df79fefe80550d9d0b410c71d87"
            },
            "content": [
                "日期：        "
            ]
        }
    ]


class TestPDFChunk(TestCase):
    def setUp(self):
        self.parser = PDFChunk()
        self.kdc = get_pdf_table_dst()

    def test_process_chunks(self):
        dstList = convert_to_dst_list(self.kdc)
        chunks = self.parser.process_chunks(dstList, 2)

        print(dst_to_json(chunks))
