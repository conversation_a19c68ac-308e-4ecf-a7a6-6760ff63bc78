import re
from typing import List
from unittest import TestCase

from modules.entity.dst_entity import dst_to_json, DSTType, DST
from modules.flows.chunk.pdf import PDFChunk
from modules.flows.chunk.test_docx import convert_to_dst_list


def get_pdf_table_dst_1():
    return [
            {
                "id": "49c1da9e35d24f07a9717ca8b979e40f",
                "parent": "-1",
                "order": 0,
                "dst_type": "root",
                "attributes": {
                    "level": 0,
                    "position": {
                        "x1": 1,
                        "y1": 2,
                        "x2": 3,
                        "y2": 4
                    },
                    "page": 0,
                    "hash": "roothashhashhashhashhashhashhashhashhashhash"
                },
                "content": [
                    "根节点"
                ],
                "mark": None
            },
            {
                "id": "5874f039f7674ef8a7b0933a4922b78c",
                "parent": "49c1da9e35d24f07a9717ca8b979e40f",
                "order": 0,
                "dst_type": "image",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 840,
                        "y1": 319,
                        "x2": 2870,
                        "y2": 1709
                    },
                    "page": 0,
                    "hash": "39ee9a397277e592014e985968144abe8d395000"
                },
                "content": [
                    "http://zhai-platereduction.ks3-cn-beijing.ksyun.com/tmp/layout/2025-05-07/de32a31a9ade40cbab5ba81d7eef67c0/kdc_cut0.png?Expires=1746688953&AWSAccessKeyId=AKLThacEYfpQEiYtqqtfXFZP&Signature=32Xl09vLS7F2M%2BII44%2BYE3Y8hmM%3D"
                ],
                "mark": None
            },
            {
                "id": "85351f31387f49338f1b746776c70167",
                "parent": "49c1da9e35d24f07a9717ca8b979e40f",
                "order": 0,
                "dst_type": "text",
                "attributes": {
                    "level": 1,
                    "position": {
                        "x1": 1950,
                        "y1": 1950,
                        "x2": 10139,
                        "y2": 2329
                    },
                    "page": 0,
                    "hash": "3ab2b69a33fe23c95d0bb2695e212bb0ac0409d1"
                },
                "content": [
                    "严重不良事件(SAE)/特别关注不良事件(AESI)报告表"
                ],
                "mark": None
            },
            {
                "id": "2aebd027db204493b4fbc356b38a88f0",
                "parent": "85351f31387f49338f1b746776c70167",
                "order": 0,
                "dst_type": "text",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 1190,
                        "y1": 2550,
                        "x2": 5169,
                        "y2": 2749
                    },
                    "page": 0,
                    "hash": "2a89ec01832fa9be65d1ac7922accc2cae091899"
                },
                "content": [
                    "新药临床批准文号：2013L00943、2015L05101"
                ],
                "mark": None
            },
            {
                "id": "be965ef3aea540179e1578db5095870c",
                "parent": "85351f31387f49338f1b746776c70167",
                "order": 0,
                "dst_type": "text",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 1190,
                        "y1": 2970,
                        "x2": 2089,
                        "y2": 3169
                    },
                    "page": 0,
                    "hash": "accd586953f0a3af5146fd11c26bc3dcd6f54ea4"
                },
                "content": [
                    "报告类型："
                ],
                "mark": None
            },
            {
                "id": "6075dd6cb1f2497b885b795381a66947",
                "parent": "85351f31387f49338f1b746776c70167",
                "order": 0,
                "dst_type": "text",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 2530,
                        "y1": 3000,
                        "x2": 3730,
                        "y2": 3199
                    },
                    "page": 0,
                    "hash": "9162c3bab790ba4ea1fcce5afd48e51622a0703f"
                },
                "content": [
                    "☑首次□随访"
                ],
                "mark": None
            },
            {
                "id": "d4de4ad524c7461cb00cbc8f6c5bb460",
                "parent": "85351f31387f49338f1b746776c70167",
                "order": 0,
                "dst_type": "text",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 3989,
                        "y1": 2989,
                        "x2": 4589,
                        "y2": 3190
                    },
                    "page": 0,
                    "hash": "f9605145c7c72e304b7a49bd218106b451cd6c4d"
                },
                "content": [
                    "□总结"
                ],
                "mark": None
            },
            {
                "id": "b6181bab026f497d8b35bb85b14cd2e2",
                "parent": "85351f31387f49338f1b746776c70167",
                "order": 0,
                "dst_type": "text",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 7479,
                        "y1": 2959,
                        "x2": 9380,
                        "y2": 3160
                    },
                    "page": 0,
                    "hash": "e250cdca1ab861f1bb5f9707a79c7d7ea09e32f9"
                },
                "content": [
                    "报告时间：2024.8.22"
                ],
                "mark": None
            },
            {
                "id": "d92c170de0b14e1f8106ca8405f1c74c",
                "parent": "85351f31387f49338f1b746776c70167",
                "order": 0,
                "dst_type": "table",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 1120,
                        "y1": 3310,
                        "x2": 10920,
                        "y2": 14629
                    },
                    "page": 0,
                    "hash": "65df4617b524fd4fff84e3ed684c090f65966717"
                },
                "content": [
                    "<table><tr><td rowspan=\"1\" colspan=\"16\">临床项目及报告单位信息</td></tr><tr><td rowspan=\"1\" colspan=\"2\">医疗机构及专业名称</td><td rowspan=\"1\" colspan=\"7\">重庆市公共卫生医疗救治中心艾滋病专业组</td><td rowspan=\"1\" colspan=\"3\">电话</td><td rowspan=\"1\" colspan=\"4\"></td></tr><tr><td rowspan=\"1\" colspan=\"2\">申报单位名称</td><td rowspan=\"1\" colspan=\"7\">科</td><td rowspan=\"1\" colspan=\"3\">电话</td><td rowspan=\"1\" colspan=\"4\"></td></tr><tr><td rowspan=\"1\" colspan=\"2\">临床研究方案名称</td><td rowspan=\"1\" colspan=\"14\">评《     广人</td></tr><tr><td rowspan=\"1\" colspan=\"2\">临床研究方案号</td><td rowspan=\"1\" colspan=\"14\">GQ-FNC-301</td></tr><tr><td rowspan=\"1\" colspan=\"2\">临床适应症</td><td rowspan=\"1\" colspan=\"14\">HIV-1感染</td></tr><tr><td rowspan=\"1\" colspan=\"2\">临床研究分类</td><td rowspan=\"1\" colspan=\"14\">□生物等效性试验□IV期☑Ⅲ期□I期  □I期□验证类临床试验</td></tr><tr><td rowspan=\"1\" colspan=\"2\">试验盲态情况</td><td rowspan=\"1\" colspan=\"14\">□非盲态  ☑盲态(☑未破盲□巳破盲-破盲时间：年 月 日)</td></tr><tr><td rowspan=\"1\" colspan=\"16\">报告者信息</td></tr><tr><td rowspan=\"1\" colspan=\"2\">报告者姓名</td><td rowspan=\"1\" colspan=\"7\"></td><td rowspan=\"1\" colspan=\"3\">所在国家</td><td rowspan=\"1\" colspan=\"4\">中国</td></tr><tr><td rowspan=\"1\" colspan=\"2\">职业</td><td rowspan=\"1\" colspan=\"7\">研究医生</td><td rowspan=\"1\" colspan=\"3\">电话</td><td rowspan=\"1\" colspan=\"4\"></td></tr><tr><td rowspan=\"1\" colspan=\"2\">获知SAE时间</td><td rowspan=\"1\" colspan=\"14\">2024年08月22日□首次获知时间□随访信息获知时问_______年____月___日</td></tr><tr><td rowspan=\"1\" colspan=\"16\">受试者信息</td></tr><tr><td rowspan=\"1\" colspan=\"1\">姓名缩写</td><td rowspan=\"1\" colspan=\"3\">CYME</td><td rowspan=\"1\" colspan=\"2\">性别</td><td rowspan=\"1\" colspan=\"1\">女</td><td rowspan=\"1\" colspan=\"3\">出生日期</td><td rowspan=\"1\" colspan=\"3\">1977年9月4日</td><td rowspan=\"1\" colspan=\"2\">发生SAE时的年龄</td><td rowspan=\"1\" colspan=\"1\">47</td></tr><tr><td rowspan=\"1\" colspan=\"1\">受试者编号</td><td rowspan=\"1\" colspan=\"3\">08-S015</td><td rowspan=\"1\" colspan=\"2\">民族</td><td rowspan=\"1\" colspan=\"1\">汉</td><td rowspan=\"1\" colspan=\"3\">身高(cm)</td><td rowspan=\"1\" colspan=\"3\">152</td><td rowspan=\"1\" colspan=\"2\">体重(kg)</td><td rowspan=\"1\" colspan=\"1\">68</td></tr><tr><td rowspan=\"2\" colspan=\"1\">息者死亡</td><td rowspan=\"1\" colspan=\"15\">☑否</td></tr><tr><td rowspan=\"1\" colspan=\"2\">□是</td><td rowspan=\"1\" colspan=\"1\">死亡日期</td><td rowspan=\"1\" colspan=\"3\">年 月 日</td><td rowspan=\"1\" colspan=\"1\">死亡原因</td><td rowspan=\"1\" colspan=\"3\"></td><td rowspan=\"1\" colspan=\"3\">是否尸检</td><td rowspan=\"1\" colspan=\"2\">□否□是尸检结果_</td></tr><tr><td rowspan=\"1\" colspan=\"16\">现病史(试验用药适应症以外，SAE发生时未恢复的疾病)</td></tr><tr><td rowspan=\"1\" colspan=\"1\">疾病名称</td><td rowspan=\"1\" colspan=\"4\">开始日期</td><td rowspan=\"1\" colspan=\"3\">结束日期</td><td rowspan=\"1\" colspan=\"3\">是否为家族史</td><td rowspan=\"1\" colspan=\"5\">备注</td></tr><tr><td rowspan=\"1\" colspan=\"1\">肌酸酶同功酶升高</td><td rowspan=\"1\" colspan=\"4\">2023.12.30</td><td rowspan=\"1\" colspan=\"3\">持续</td><td rowspan=\"1\" colspan=\"3\">否</td><td rowspan=\"1\" colspan=\"5\"></td></tr><tr><td rowspan=\"1\" colspan=\"1\">血β2微球蛋白升高</td><td rowspan=\"1\" colspan=\"4\">2024.2.22</td><td rowspan=\"1\" colspan=\"3\">持续</td><td rowspan=\"1\" colspan=\"3\">否</td><td rowspan=\"1\" colspan=\"5\"></td></tr><tr><td rowspan=\"1\" colspan=\"1\">Y-谷氨酰基转移酶升高</td><td rowspan=\"1\" colspan=\"4\">2024.2.22</td><td rowspan=\"1\" colspan=\"3\">持续</td><td rowspan=\"1\" colspan=\"3\">否</td><td rowspan=\"1\" colspan=\"5\"></td></tr><tr><td rowspan=\"1\" colspan=\"1\">尿酸升商</td><td rowspan=\"1\" colspan=\"4\">2024.5.21</td><td rowspan=\"1\" colspan=\"3\">持续</td><td rowspan=\"1\" colspan=\"3\">否</td><td rowspan=\"1\" colspan=\"5\"></td></tr><tr><td rowspan=\"1\" colspan=\"1\">血乳敌升高</td><td rowspan=\"1\" colspan=\"4\">2024.5.21</td><td rowspan=\"1\" colspan=\"3\">持续</td><td rowspan=\"1\" colspan=\"3\">否</td><td rowspan=\"1\" colspan=\"5\"></td></tr><tr><td rowspan=\"1\" colspan=\"1\">窦性心动过缓</td><td rowspan=\"1\" colspan=\"4\">2024.5.21</td><td rowspan=\"1\" colspan=\"3\">持续</td><td rowspan=\"1\" colspan=\"3\">否</td><td rowspan=\"1\" colspan=\"5\"></td></tr><tr><td rowspan=\"1\" colspan=\"1\">总胆固醇升高</td><td rowspan=\"1\" colspan=\"4\">2023.11.30</td><td rowspan=\"1\" colspan=\"3\">持续</td><td rowspan=\"1\" colspan=\"3\">否</td><td rowspan=\"1\" colspan=\"5\"></td></tr><tr><td rowspan=\"1\" colspan=\"16\">既往病史(SAE发生前已恢复的疾病)</td></tr><tr><td rowspan=\"1\" colspan=\"1\">疾病名称</td><td rowspan=\"1\" colspan=\"4\">开始日期</td><td rowspan=\"1\" colspan=\"3\">结束日期</td><td rowspan=\"1\" colspan=\"3\">是否为家族史</td><td rowspan=\"1\" colspan=\"5\">备注</td></tr><tr><td rowspan=\"1\" colspan=\"1\">血糖升高</td><td rowspan=\"1\" colspan=\"4\">2022.12.30</td><td rowspan=\"1\" colspan=\"3\">2023.3.23</td><td rowspan=\"1\" colspan=\"3\">否</td><td rowspan=\"1\" colspan=\"5\"></td></tr></table>"
                ],
                "mark": None
            },
            {
                "id": "dc3d119572594e628056b97e683beeee",
                "parent": "85351f31387f49338f1b746776c70167",
                "order": 0,
                "dst_type": "text",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 1219,
                        "y1": 14909,
                        "x2": 3669,
                        "y2": 15080
                    },
                    "page": 0,
                    "hash": "f6533f81fe3d2d52f59d7e2a26139e0c882777df"
                },
                "content": [
                    "Clin-nov Medical Confidential"
                ],
                "mark": None
            },
            {
                "id": "6874b88fdc2247edb5ebf91d34276582",
                "parent": "85351f31387f49338f1b746776c70167",
                "order": 0,
                "dst_type": "text",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 5119,
                        "y1": 14930,
                        "x2": 7650,
                        "y2": 15130
                    },
                    "page": 0,
                    "hash": "fef71185765ee78a3daed8c53dfe2ed60792fbfc"
                },
                "content": [
                    "CS-TP-PV-005_2.0(12/29/2023)"
                ],
                "mark": None
            },
            {
                "id": "879c2aa6a24949b4a949969ff396ff2c",
                "parent": "85351f31387f49338f1b746776c70167",
                "order": 0,
                "dst_type": "text",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 9279,
                        "y1": 14920,
                        "x2": 10149,
                        "y2": 15130
                    },
                    "page": 0,
                    "hash": "547525b648aec05adfbfc21b24fd526195620d6b"
                },
                "content": [
                    "Page 1 of 5"
                ],
                "mark": None
            },
            {
                "id": "761e3f1cf8de4fcf850d81de32adc069",
                "parent": "85351f31387f49338f1b746776c70167",
                "order": 0,
                "dst_type": "image",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 8870,
                        "y1": 16029,
                        "x2": 9490,
                        "y2": 16650
                    },
                    "page": 0,
                    "hash": "24e8a686ed65267e91cbd842645e067cdef7aba3"
                },
                "content": [
                    "http://zhai-platereduction.ks3-cn-beijing.ksyun.com/tmp/layout/2025-05-07/de32a31a9ade40cbab5ba81d7eef67c0/kdc_cut1.png?Expires=1746688953&AWSAccessKeyId=AKLThacEYfpQEiYtqqtfXFZP&Signature=kombXVfuG1dvQXcjgoseLCHMz7E%3D"
                ],
                "mark": None
            },
            {
                "id": "c04b5a552a8448d79f168fe5c0383b61",
                "parent": "85351f31387f49338f1b746776c70167",
                "order": 0,
                "dst_type": "text",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 9559,
                        "y1": 16219,
                        "x2": 11490,
                        "y2": 16470
                    },
                    "page": 0,
                    "hash": "1ab418d4a57fdf4f9dbcfcc82d0e777b0b780e7f"
                },
                "content": [
                    "扫描全能王创建"
                ],
                "mark": None
            },
            {
                "id": "87b7a06efda447e487511ba147bcb821",
                "parent": "85351f31387f49338f1b746776c70167",
                "order": 0,
                "dst_type": "image",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 959,
                        "y1": 949,
                        "x2": 2620,
                        "y2": 1779
                    },
                    "page": 1,
                    "hash": "a05e63093b8b39bd9b7afb61eeeb1a8513a01753"
                },
                "content": [
                    "http://zhai-platereduction.ks3-cn-beijing.ksyun.com/tmp/layout/2025-05-07/de32a31a9ade40cbab5ba81d7eef67c0/kdc_cut3.png?Expires=1746688953&AWSAccessKeyId=AKLThacEYfpQEiYtqqtfXFZP&Signature=vOqwIdujejkwACyL6J4TKNlk6Q8%3D"
                ],
                "mark": None
            },
            {
                "id": "f5c3ca6f30db4319a419dbdf9565cf06",
                "parent": "85351f31387f49338f1b746776c70167",
                "order": 0,
                "dst_type": "table",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 909,
                        "y1": 1930,
                        "x2": 11109,
                        "y2": 6469
                    },
                    "page": 1,
                    "hash": "b32704878e7d3c1c62869e598c9ec5dfaa5ade16"
                },
                "content": [
                    "<table><tr><td rowspan=\"1\" colspan=\"1\">尿白细胞升高</td><td rowspan=\"1\" colspan=\"2\">2022.10.7</td><td rowspan=\"1\" colspan=\"2\">2023.9.8</td><td rowspan=\"1\" colspan=\"2\">否</td><td rowspan=\"1\" colspan=\"3\"></td></tr><tr><td rowspan=\"1\" colspan=\"1\">上呼吸道感染</td><td rowspan=\"1\" colspan=\"2\">2023.3.8</td><td rowspan=\"1\" colspan=\"2\">2023.3.15</td><td rowspan=\"1\" colspan=\"2\">否</td><td rowspan=\"1\" colspan=\"3\"></td></tr><tr><td rowspan=\"1\" colspan=\"1\">血乳酸升高</td><td rowspan=\"1\" colspan=\"2\">2023.6.16</td><td rowspan=\"1\" colspan=\"2\">2023.11.30</td><td rowspan=\"1\" colspan=\"2\">否</td><td rowspan=\"1\" colspan=\"3\"></td></tr><tr><td rowspan=\"1\" colspan=\"1\">血侯降低</td><td rowspan=\"1\" colspan=\"2\">2023.9.8</td><td rowspan=\"1\" colspan=\"2\">2023.11.30</td><td rowspan=\"1\" colspan=\"2\">否</td><td rowspan=\"1\" colspan=\"3\"></td></tr><tr><td rowspan=\"1\" colspan=\"1\">白细胞升高</td><td rowspan=\"1\" colspan=\"2\">2023.11.30</td><td rowspan=\"1\" colspan=\"2\">2024.2.22</td><td rowspan=\"1\" colspan=\"2\">否</td><td rowspan=\"1\" colspan=\"3\"></td></tr><tr><td rowspan=\"1\" colspan=\"10\">过敏史：(过敏原：_)□有☑无</td></tr><tr><td rowspan=\"1\" colspan=\"10\">饮酒史：□有☑无</td></tr><tr><td rowspan=\"1\" colspan=\"10\">吸烟史：□有☑无</td></tr><tr><td rowspan=\"1\" colspan=\"10\">□有其他：☑无</td></tr><tr><td rowspan=\"1\" colspan=\"10\">相关实验室检查(如适用，选填)</td></tr><tr><td rowspan=\"1\" colspan=\"1\">检查项</td><td rowspan=\"1\" colspan=\"1\">检查日期</td><td rowspan=\"1\" colspan=\"2\">检查结果</td><td rowspan=\"1\" colspan=\"2\">单位</td><td rowspan=\"1\" colspan=\"2\">正常值下限</td><td rowspan=\"1\" colspan=\"1\">正常值上限</td><td rowspan=\"1\" colspan=\"1\">备注</td></tr><tr><td rowspan=\"1\" colspan=\"1\">乳酸脱氢酶</td><td rowspan=\"1\" colspan=\"1\">2024.2.22</td><td rowspan=\"1\" colspan=\"2\">253</td><td rowspan=\"1\" colspan=\"2\">U/L</td><td rowspan=\"1\" colspan=\"2\">120</td><td rowspan=\"1\" colspan=\"1\">250</td><td rowspan=\"1\" colspan=\"1\"></td></tr><tr><td rowspan=\"1\" colspan=\"1\">乳酸脱氢酶</td><td rowspan=\"1\" colspan=\"1\">2024.5.21</td><td rowspan=\"1\" colspan=\"2\">258</td><td rowspan=\"1\" colspan=\"2\">U/L</td><td rowspan=\"1\" colspan=\"2\">120</td><td rowspan=\"1\" colspan=\"1\">250</td><td rowspan=\"1\" colspan=\"1\"></td></tr><tr><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"2\"></td><td rowspan=\"1\" colspan=\"2\"></td><td rowspan=\"1\" colspan=\"2\"></td><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\"></td></tr></table>"
                ],
                "mark": None
            },
            {
                "id": "aabe7ee34a954fe9b735fce496b36e80",
                "parent": "85351f31387f49338f1b746776c70167",
                "order": 0,
                "dst_type": "table",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 909,
                        "y1": 6790,
                        "x2": 11119,
                        "y2": 10870
                    },
                    "page": 1,
                    "hash": "4b9ccc0e9cbc0cf1688903141063b39c56c4f586"
                },
                "content": [
                    "<table><tr><td rowspan=\"1\" colspan=\"4\">试验用药信息 口未使用试验药品☑已使用试验药品(如有多个试验用药，请复制此表格添加)。</td></tr><tr><td rowspan=\"1\" colspan=\"1\">药物中文名称</td><td rowspan=\"1\" colspan=\"1\">十</td><td rowspan=\"1\" colspan=\"1\">药物英文名称</td><td rowspan=\"1\" colspan=\"1\"></td></tr><tr><td rowspan=\"1\" colspan=\"1\">活性成分</td><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\">剂型</td><td rowspan=\"1\" colspan=\"1\">片剂</td></tr><tr><td rowspan=\"1\" colspan=\"1\">给药剂量</td><td rowspan=\"1\" colspan=\"1\">3mg</td><td rowspan=\"1\" colspan=\"1\">给药途径</td><td rowspan=\"1\" colspan=\"1\">口服</td></tr><tr><td rowspan=\"1\" colspan=\"1\">给药频次</td><td rowspan=\"1\" colspan=\"3\">每日一次</td></tr><tr><td rowspan=\"1\" colspan=\"1\">药物编号</td><td rowspan=\"1\" colspan=\"1\">T00613</td><td rowspan=\"1\" colspan=\"1\">规格</td><td rowspan=\"1\" colspan=\"1\">3mg/片</td></tr><tr><td rowspan=\"1\" colspan=\"1\">生产厂家</td><td rowspan=\"1\" colspan=\"1\">业一六    二 士古</td><td rowspan=\"1\" colspan=\"1\">临床试验适应症</td><td rowspan=\"1\" colspan=\"1\">HIV-1感染者</td></tr><tr><td rowspan=\"1\" colspan=\"1\">药品分类</td><td rowspan=\"1\" colspan=\"3\">□中药☑化药□治疗用生物制品  □预防用生物制品</td></tr><tr><td rowspan=\"1\" colspan=\"1\">用药时间</td><td rowspan=\"1\" colspan=\"3\">2022年7月13日22时17分-2024年5月20日22时17分</td></tr><tr><td rowspan=\"1\" colspan=\"1\">用药处置</td><td rowspan=\"1\" colspan=\"3\">□减少剂量☑剂量不变□不适用□增加剂量□停止用药□不详</td></tr></table>"
                ],
                "mark": None
            },
            {
                "id": "9df1e5956c4c4ce0a32a723b3ede34af",
                "parent": "85351f31387f49338f1b746776c70167",
                "order": 0,
                "dst_type": "table",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 939,
                        "y1": 11169,
                        "x2": 11170,
                        "y2": 14339
                    },
                    "page": 1,
                    "hash": "779f2a175ce68fc3d2cac68ca1ccbb6f7aa6e032"
                },
                "content": [
                    "<table><tr><td rowspan=\"1\" colspan=\"1\">药物中文名称</td><td rowspan=\"1\" colspan=\"1\">出业I地土业土定之</td><td rowspan=\"1\" colspan=\"1\">药物英文名称</td><td rowspan=\"1\" colspan=\"1\">3</td></tr><tr><td rowspan=\"1\" colspan=\"1\">活性成分</td><td rowspan=\"1\" colspan=\"1\">拉米夫定</td><td rowspan=\"1\" colspan=\"1\">剂型</td><td rowspan=\"1\" colspan=\"1\">片剂</td></tr><tr><td rowspan=\"1\" colspan=\"1\">给药剂量</td><td rowspan=\"1\" colspan=\"1\">300mg</td><td rowspan=\"1\" colspan=\"1\">给药途径</td><td rowspan=\"1\" colspan=\"1\">口服</td></tr><tr><td rowspan=\"1\" colspan=\"1\">给药频次</td><td rowspan=\"1\" colspan=\"3\">每日一次</td></tr><tr><td rowspan=\"1\" colspan=\"1\">药物编号</td><td rowspan=\"1\" colspan=\"1\">R00229</td><td rowspan=\"1\" colspan=\"1\">规格</td><td rowspan=\"1\" colspan=\"1\">300mg/片</td></tr><tr><td rowspan=\"1\" colspan=\"1\">生产厂家</td><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\">临床试验适应症</td><td rowspan=\"1\" colspan=\"1\">HIV-1感染者</td></tr><tr><td rowspan=\"1\" colspan=\"1\">药品分类</td><td rowspan=\"1\" colspan=\"3\">□中药☑化药  □治疗用生物制品□预防用生物制品</td></tr><tr><td rowspan=\"1\" colspan=\"1\">用药时间</td><td rowspan=\"1\" colspan=\"3\">2022年7月13日22时17分-2024年5月20日22时17分</td></tr><tr><td rowspan=\"1\" colspan=\"1\">用药处置</td><td rowspan=\"1\" colspan=\"3\">□剂量不变□不适用□不详  □停止用药  □减少剂量□增加剂量</td></tr></table>"
                ],
                "mark": None
            },
            {
                "id": "b966d0108b7e4a13b8d7ebd109d94772",
                "parent": "85351f31387f49338f1b746776c70167",
                "order": 0,
                "dst_type": "text",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 9359,
                        "y1": 14959,
                        "x2": 10249,
                        "y2": 15149
                    },
                    "page": 1,
                    "hash": "559bff303c685629e1b4e27feb0f99a6358e0f0f"
                },
                "content": [
                    "Page 2 of 5"
                ],
                "mark": None
            },
            {
                "id": "3e6a82c719194c9faccb08e3f177157d",
                "parent": "85351f31387f49338f1b746776c70167",
                "order": 0,
                "dst_type": "image",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 8870,
                        "y1": 16029,
                        "x2": 9490,
                        "y2": 16650
                    },
                    "page": 1,
                    "hash": "8c43f2c9b354a7f27f1d48d89b67447f6720e09c"
                },
                "content": [
                    "http://zhai-platereduction.ks3-cn-beijing.ksyun.com/tmp/layout/2025-05-07/de32a31a9ade40cbab5ba81d7eef67c0/kdc_cut2.png?Expires=1746688953&AWSAccessKeyId=AKLThacEYfpQEiYtqqtfXFZP&Signature=fMBk4aBOfGHw8k8iu8jCIDjL3DE%3D"
                ],
                "mark": None
            },
            {
                "id": "385d4cab7edd4120aed1742e2f18ce32",
                "parent": "85351f31387f49338f1b746776c70167",
                "order": 0,
                "dst_type": "text",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 9559,
                        "y1": 16219,
                        "x2": 11490,
                        "y2": 16470
                    },
                    "page": 1,
                    "hash": "1ab418d4a57fdf4f9dbcfcc82d0e777b0b780e7f"
                },
                "content": [
                    "扫描全能王创建"
                ],
                "mark": None
            },
            {
                "id": "de85a82a2af0402fbdc8ca999c6ff91d",
                "parent": "85351f31387f49338f1b746776c70167",
                "order": 0,
                "dst_type": "table",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 989,
                        "y1": 1980,
                        "x2": 11089,
                        "y2": 5159
                    },
                    "page": 2,
                    "hash": "75792926a8960b3c0d0d680c055f8e69e6143421"
                },
                "content": [
                    "<table><tr><td rowspan=\"1\" colspan=\"1\">药物中文名称</td><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\">药物英文名称</td><td rowspan=\"1\" colspan=\"1\">Tenofovir Disoproxil Fumarate,TDF</td></tr><tr><td rowspan=\"1\" colspan=\"1\">活性成分</td><td rowspan=\"1\" colspan=\"1\">富马酸替诺福韦二毗呋酯</td><td rowspan=\"1\" colspan=\"1\">剂型</td><td rowspan=\"1\" colspan=\"1\">片剂</td></tr><tr><td rowspan=\"1\" colspan=\"1\">给药剂量</td><td rowspan=\"1\" colspan=\"1\">300mg</td><td rowspan=\"1\" colspan=\"1\">给药途径</td><td rowspan=\"1\" colspan=\"1\">口服</td></tr><tr><td rowspan=\"1\" colspan=\"1\">给药频次</td><td rowspan=\"1\" colspan=\"3\">每日一次</td></tr><tr><td rowspan=\"1\" colspan=\"1\">药物编号</td><td rowspan=\"1\" colspan=\"1\">NA</td><td rowspan=\"1\" colspan=\"1\">规格</td><td rowspan=\"1\" colspan=\"1\">300mg/片</td></tr><tr><td rowspan=\"1\" colspan=\"1\">生产厂家</td><td rowspan=\"1\" colspan=\"1\">家庄龙泽制药股份有限公司</td><td rowspan=\"1\" colspan=\"1\">临床试验适应症</td><td rowspan=\"1\" colspan=\"1\">HIV-1感染者</td></tr><tr><td rowspan=\"1\" colspan=\"1\">药品分类</td><td rowspan=\"1\" colspan=\"3\">□中药☑化药□治疗用生物制品□预防用生物制品</td></tr><tr><td rowspan=\"1\" colspan=\"1\">用药时问</td><td rowspan=\"1\" colspan=\"3\">2022年7月13日22时17分-2024年5月20日22_时17  分</td></tr><tr><td rowspan=\"1\" colspan=\"1\">用药处置</td><td rowspan=\"1\" colspan=\"3\">□增加剂量□减少剂量□不详  □停止用药□不适用☑剂量不变</td></tr></table>"
                ],
                "mark": None
            },
            {
                "id": "97fcacae09f5447ea5cb2660e5740761",
                "parent": "85351f31387f49338f1b746776c70167",
                "order": 0,
                "dst_type": "table",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 919,
                        "y1": 5439,
                        "x2": 11069,
                        "y2": 8590
                    },
                    "page": 2,
                    "hash": "512c5c3a7f95d4d1c899e6912e0cbcc41b5573e8"
                },
                "content": [
                    "<table><tr><td rowspan=\"1\" colspan=\"1\">药物中文名称</td><td rowspan=\"1\" colspan=\"1\">依非韦伦片</td><td rowspan=\"1\" colspan=\"1\">药物英文名称</td><td rowspan=\"1\" colspan=\"1\">Efavirenz,EFV</td></tr><tr><td rowspan=\"1\" colspan=\"1\">活性成分</td><td rowspan=\"1\" colspan=\"1\">依非韦伦</td><td rowspan=\"1\" colspan=\"1\">剂型</td><td rowspan=\"1\" colspan=\"1\">片剂</td></tr><tr><td rowspan=\"1\" colspan=\"1\">给药剂量</td><td rowspan=\"1\" colspan=\"1\">200mg*2</td><td rowspan=\"1\" colspan=\"1\">给药途径</td><td rowspan=\"1\" colspan=\"1\">口服</td></tr><tr><td rowspan=\"1\" colspan=\"1\">给药频次</td><td rowspan=\"1\" colspan=\"3\">每日一次</td></tr><tr><td rowspan=\"1\" colspan=\"1\">药物编号</td><td rowspan=\"1\" colspan=\"1\">NA</td><td rowspan=\"1\" colspan=\"1\">规格</td><td rowspan=\"1\" colspan=\"1\">200mg/片</td></tr><tr><td rowspan=\"1\" colspan=\"1\">生产厂家</td><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\">临床试验适应症</td><td rowspan=\"1\" colspan=\"1\">HIV-1感染者</td></tr><tr><td rowspan=\"1\" colspan=\"1\">药品分类</td><td rowspan=\"1\" colspan=\"3\">□中药☑化药  □治疗用生物制品□预防用生物制品</td></tr><tr><td rowspan=\"1\" colspan=\"1\">用药时间</td><td rowspan=\"1\" colspan=\"3\">2022年7月13日22时17分-2024年5月20日22_时17分</td></tr><tr><td rowspan=\"1\" colspan=\"1\">用药处置</td><td rowspan=\"1\" colspan=\"3\">□剂量不变□不适用□增加剂量□减少剂量□不详□停止用药</td></tr></table>"
                ],
                "mark": None
            },
            {
                "id": "7d177e9366c44a9e8e1be9733ce2fd38",
                "parent": "85351f31387f49338f1b746776c70167",
                "order": 0,
                "dst_type": "table",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 909,
                        "y1": 8930,
                        "x2": 11059,
                        "y2": 11219
                    },
                    "page": 2,
                    "hash": "947958ed0ada7c4b85fb551468fcdbb6e8c473bb"
                },
                "content": [
                    "<table><tr><td rowspan=\"1\" colspan=\"5\">既往用药信息(既往用药收集既往使用且在严重不良事件发生前已停用的药物。)</td></tr><tr><td rowspan=\"1\" colspan=\"1\">药物通用名称</td><td rowspan=\"1\" colspan=\"1\">使用原因</td><td rowspan=\"1\" colspan=\"1\">剂量</td><td rowspan=\"1\" colspan=\"1\">开始日期</td><td rowspan=\"1\" colspan=\"1\">结束日期</td></tr><tr><td rowspan=\"1\" colspan=\"1\">阿莫西林胶囊</td><td rowspan=\"1\" colspan=\"1\">咳嗽</td><td rowspan=\"1\" colspan=\"1\">0.5g</td><td rowspan=\"1\" colspan=\"1\">2022.7.31</td><td rowspan=\"1\" colspan=\"1\">2022.8.2</td></tr><tr><td rowspan=\"1\" colspan=\"1\">咳特灵</td><td rowspan=\"1\" colspan=\"1\">咳嗽</td><td rowspan=\"1\" colspan=\"1\">2颗</td><td rowspan=\"1\" colspan=\"1\">2022.7.31</td><td rowspan=\"1\" colspan=\"1\">2022.8.2</td></tr><tr><td rowspan=\"1\" colspan=\"1\">复方磺胺甲嘎唑片</td><td rowspan=\"1\" colspan=\"1\">预防性用药</td><td rowspan=\"1\" colspan=\"1\">0.96g</td><td rowspan=\"1\" colspan=\"1\">2022.7.11</td><td rowspan=\"1\" colspan=\"1\">2022.7.11</td></tr><tr><td rowspan=\"1\" colspan=\"1\">唐草片</td><td rowspan=\"1\" colspan=\"1\">提升免疫力</td><td rowspan=\"1\" colspan=\"1\">3.2g</td><td rowspan=\"1\" colspan=\"1\">2022.7.11</td><td rowspan=\"1\" colspan=\"1\">2.22.8.2</td></tr><tr><td rowspan=\"1\" colspan=\"1\">阿莫西林胶囊</td><td rowspan=\"1\" colspan=\"1\">上呼吸道感染</td><td rowspan=\"1\" colspan=\"1\">0.25g</td><td rowspan=\"1\" colspan=\"1\">2023.3.8</td><td rowspan=\"1\" colspan=\"1\">2023.3.10</td></tr></table>"
                ],
                "mark": None
            },
            {
                "id": "ed77dc970d1a4cd5811e3c0808fbcfd5",
                "parent": "85351f31387f49338f1b746776c70167",
                "order": 0,
                "dst_type": "table",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 929,
                        "y1": 11509,
                        "x2": 11069,
                        "y2": 12780
                    },
                    "page": 2,
                    "hash": "121f8c5eb04b95d297110ed432cc34d7c6fb947d"
                },
                "content": [
                    "<table><tr><td rowspan=\"1\" colspan=\"5\">合并用药信息(是指SAE发生前开始使用，SAE发生时正在使用的药品：针SAE的治疗用药，请填写在最后的“SAE发生及处理的详细情况”栏。)</td></tr><tr><td rowspan=\"1\" colspan=\"1\">药物通用名称</td><td rowspan=\"1\" colspan=\"1\">使用原因</td><td rowspan=\"1\" colspan=\"1\">剂量</td><td rowspan=\"1\" colspan=\"1\">开始日期</td><td rowspan=\"1\" colspan=\"1\">结束日期</td></tr><tr><td rowspan=\"1\" colspan=\"1\">无</td><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\"></td></tr></table>"
                ],
                "mark": None
            },
            {
                "id": "49b3610777e64893affcbcea24423414",
                "parent": "85351f31387f49338f1b746776c70167",
                "order": 0,
                "dst_type": "table",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 939,
                        "y1": 13100,
                        "x2": 11069,
                        "y2": 14950
                    },
                    "page": 2,
                    "hash": "6c11cbec80f3c377889ab223393b118465d26caf"
                },
                "content": [
                    "<table><tr><td rowspan=\"1\" colspan=\"4\">严重不良事件信息1(如同时发生多个严重不良事件，请复制此表格添加)</td></tr><tr><td rowspan=\"1\" colspan=\"1\">不良事件名称(诊断)</td><td rowspan=\"1\" colspan=\"3\">乳酸脱氢酶</td></tr><tr><td rowspan=\"1\" colspan=\"1\">是否为严重不良事件</td><td rowspan=\"1\" colspan=\"1\">口是☑否</td><td rowspan=\"1\" colspan=\"1\">是否为AESI</td><td rowspan=\"1\" colspan=\"1\">☑是  □否</td></tr><tr><td rowspan=\"1\" colspan=\"1\">开始时问</td><td rowspan=\"1\" colspan=\"1\">2024年2月22日</td><td rowspan=\"1\" colspan=\"1\">若痊愈/痊愈伴有后遗症或死亡，结束时问</td><td rowspan=\"1\" colspan=\"1\">__年___月__日</td></tr></table>"
                ],
                "mark": None
            },
            {
                "id": "67461558a20240baaa9101263eef72fe",
                "parent": "85351f31387f49338f1b746776c70167",
                "order": 0,
                "dst_type": "text",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 9269,
                        "y1": 15019,
                        "x2": 10259,
                        "y2": 15199
                    },
                    "page": 2,
                    "hash": "1e0cd7c2c3c9211ca17210fa79b738903290ca7c"
                },
                "content": [
                    "Page 3 of 5"
                ],
                "mark": None
            },
            {
                "id": "b5682f2f15f1496d8338cf76ea1a359e",
                "parent": "85351f31387f49338f1b746776c70167",
                "order": 0,
                "dst_type": "image",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 8870,
                        "y1": 16029,
                        "x2": 9490,
                        "y2": 16650
                    },
                    "page": 2,
                    "hash": "4850d1940d9567008c344c05550ebbf42f2dd79a"
                },
                "content": [
                    "http://zhai-platereduction.ks3-cn-beijing.ksyun.com/tmp/layout/2025-05-07/de32a31a9ade40cbab5ba81d7eef67c0/kdc_cut4.png?Expires=1746688953&AWSAccessKeyId=AKLThacEYfpQEiYtqqtfXFZP&Signature=Gojnt4hUGBEFBeG6cG5%2FZpz%2F2Bc%3D"
                ],
                "mark": None
            },
            {
                "id": "30764503c1204919a833aa6144a69299",
                "parent": "85351f31387f49338f1b746776c70167",
                "order": 0,
                "dst_type": "text",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 9559,
                        "y1": 16219,
                        "x2": 11490,
                        "y2": 16470
                    },
                    "page": 2,
                    "hash": "1ab418d4a57fdf4f9dbcfcc82d0e777b0b780e7f"
                },
                "content": [
                    "扫描全能王创建"
                ],
                "mark": None
            },
            {
                "id": "c9c4e8bd92cf43dd9a00183daf417c76",
                "parent": "85351f31387f49338f1b746776c70167",
                "order": 0,
                "dst_type": "table",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 1009,
                        "y1": 2010,
                        "x2": 11069,
                        "y2": 10500
                    },
                    "page": 3,
                    "hash": "1f59d5e522278b9d15f7fd8b75b7954db614644a"
                },
                "content": [
                    "<table><tr><td rowspan=\"1\" colspan=\"1\">持续时问</td><td rowspan=\"1\" colspan=\"2\"></td><td rowspan=\"1\" colspan=\"1\">DAIDS分级</td><td rowspan=\"1\" colspan=\"1\">1级</td></tr><tr><td rowspan=\"1\" colspan=\"1\">严重性标准(根据实际情况勾选，对于非严重的AESI请不要选择)</td><td rowspan=\"1\" colspan=\"2\">□导致死亡□功能丧失/致残□危及生命□导致住院/延长住院时问□导致先天性异常/出生缺陷□其他重要医学事件</td><td rowspan=\"1\" colspan=\"1\">转归</td><td rowspan=\"1\" colspan=\"1\">□痊愈□好转/级解☑未好转/未缓解/持续□痊愈伴有后遣症□致死□未知</td></tr><tr><td rowspan=\"1\" colspan=\"1\">国内SAE报道</td><td rowspan=\"1\" colspan=\"2\">□有  □无□不详</td><td rowspan=\"1\" colspan=\"1\">国外SAE报道</td><td rowspan=\"1\" colspan=\"1\">□有  ☑无  □不详</td></tr><tr><td rowspan=\"1\" colspan=\"5\">相关性评价(盲态试验未破盲时，名称填写试验用药；若为联合用药，对不同成分分别进行评价)</td></tr><tr><td rowspan=\"1\" colspan=\"1\">药品名</td><td rowspan=\"1\" colspan=\"4\"></td></tr><tr><td rowspan=\"1\" colspan=\"1\">相关性评价</td><td rowspan=\"1\" colspan=\"4\">□肯定相关  □很可能相关  ☑可能相关  □可能无关□不相关</td></tr><tr><td rowspan=\"1\" colspan=\"2\">停药或减景后，反应是否消失或减轻?</td><td rowspan=\"1\" colspan=\"3\">□是□否 口不详□不适用</td></tr><tr><td rowspan=\"1\" colspan=\"2\">再次使用试验药品后，是否再次出现同样反应?</td><td rowspan=\"1\" colspan=\"3\">□是□否□不详□不适用(未重新给药)</td></tr><tr><td rowspan=\"1\" colspan=\"1\">药品名</td><td rowspan=\"1\" colspan=\"4\">拉米夫定片/拉米夫定片模拟剂</td></tr><tr><td rowspan=\"1\" colspan=\"1\">相关性评价</td><td rowspan=\"1\" colspan=\"4\">□不相关□肯定相关  □很可能相关   ☑可能相关   □可能无关</td></tr><tr><td rowspan=\"1\" colspan=\"2\">停药或减量后，反应是否消失或减轻?</td><td rowspan=\"1\" colspan=\"3\">□是  □否  □不详  ☑不适用</td></tr><tr><td rowspan=\"1\" colspan=\"2\">再次使用试验药品后，是否再次出现同样反应?</td><td rowspan=\"1\" colspan=\"3\">□是  □否  □不详  ☑不适用(未重新给药)</td></tr><tr><td rowspan=\"1\" colspan=\"1\">药品名</td><td rowspan=\"1\" colspan=\"4\">富马酸替诺福韦二吡呋酯片</td></tr><tr><td rowspan=\"1\" colspan=\"1\">相关性评价</td><td rowspan=\"1\" colspan=\"4\">□可能无关□不相关□肯定相关□很可能相关☑可能相关</td></tr><tr><td rowspan=\"1\" colspan=\"2\">停药或减量后，反应是否消失或减轻?</td><td rowspan=\"1\" colspan=\"3\">□不适用□否  □不详□是</td></tr><tr><td rowspan=\"1\" colspan=\"2\">再次使用试验药品后，是否再次出现同样反应?</td><td rowspan=\"1\" colspan=\"3\">☑不适用(未重新给药)□否  □不详□是</td></tr><tr><td rowspan=\"1\" colspan=\"1\">药品名</td><td rowspan=\"1\" colspan=\"4\">依非韦伦片</td></tr><tr><td rowspan=\"1\" colspan=\"1\">相关性评价</td><td rowspan=\"1\" colspan=\"4\">□不相关□肯定相关  □很可能相关☑可能相关□可能无关</td></tr><tr><td rowspan=\"1\" colspan=\"2\">停药或减量后，反应是否消失或减轻?</td><td rowspan=\"1\" colspan=\"3\">□是□否□不详☑不适用</td></tr><tr><td rowspan=\"1\" colspan=\"2\">再次使用试验药品后，是否再次出现同样反应?</td><td rowspan=\"1\" colspan=\"3\">□不适用(未重新给药)□是  □否  □不详</td></tr><tr><td rowspan=\"1\" colspan=\"1\">相关性评估依据</td><td rowspan=\"1\" colspan=\"4\">受试者2027年07月13日开始服用药物，2024年2月22日出现乳酸脱氢酶升高，该A发生在用药之后，因此与研究药物的关系判定为可能有关。</td></tr></table>"
                ],
                "mark": None
            },
            {
                "id": "2fc958bf303e460882441b8bb71ce208",
                "parent": "85351f31387f49338f1b746776c70167",
                "order": 0,
                "dst_type": "table",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 959,
                        "y1": 11429,
                        "x2": 11059,
                        "y2": 14909
                    },
                    "page": 3,
                    "hash": "12801c32554d1a370700e1111f72d70677c038cf"
                },
                "content": [
                    "<table><tr><td rowspan=\"1\" colspan=\"1\">SAE发生及处理的详细情况(如同时发生多个严重不良事件，可统一记录在此处)</td></tr><tr><td rowspan=\"1\" colspan=\"1\">本报告于2024年8月22日获得首次信息。受试者CYME,46岁，汉族，女，受试者于2022年7月11日签署知情同意书(版本号：1.1,版本日期：2022年3月21日，自愿参与“-   进豆士一l吐与位非韦价在未接受过抗病毒治疗的HV临庆研容(G方：-经筛选后符合所有入选标准，不符合所有排除标准，于2022年7月12日随机入组。受试者自2022年7月13日开始服用研究药物，阿兹夫定片(模拟剂)QD 3mg,拉米夫定片(模拟剂)QD 300mg,依非韦伦片QD 400mg,富马酸替诺福韦片吡呋酯片QD 300mg,口服。1.AE名称：乳酸脱氢酶升高，DAIDS分级为1级，开始日期：2024年2月22日，考虑与FNC/3TC可能有关，与EFV可能有关，与TDF可能有关，对研究药物的影响：无影响，对不良事件采取的措施：未采取任何治疗措施，该AE目前转归情况：持续。据临床研究方案(V1.3/2023.12.14)中新增的本研究者特别关注的不良事件(Adverse event of special interest,AESI)</td></tr></table>"
                ],
                "mark": None
            },
            {
                "id": "65cb7d0f418b4c5bb9f05b58f6c9a80c",
                "parent": "85351f31387f49338f1b746776c70167",
                "order": 0,
                "dst_type": "text",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 9239,
                        "y1": 14980,
                        "x2": 10229,
                        "y2": 15160
                    },
                    "page": 3,
                    "hash": "2e5eecb61da73a8cd80528104a2bd69a67232f18"
                },
                "content": [
                    "Page 4 of 5"
                ],
                "mark": None
            },
            {
                "id": "bb0566e00c1144d4a0f6324c74ac7328",
                "parent": "85351f31387f49338f1b746776c70167",
                "order": 0,
                "dst_type": "image",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 8870,
                        "y1": 16029,
                        "x2": 9490,
                        "y2": 16650
                    },
                    "page": 3,
                    "hash": "c6ea8118d93113ca7122ae8d25add1a2c70732f5"
                },
                "content": [
                    "http://zhai-platereduction.ks3-cn-beijing.ksyun.com/tmp/layout/2025-05-07/de32a31a9ade40cbab5ba81d7eef67c0/kdc_cut5.png?Expires=1746688953&AWSAccessKeyId=AKLThacEYfpQEiYtqqtfXFZP&Signature=%2BNBsE3Wrdh7yR4Qq2E4oIoMWc88%3D"
                ],
                "mark": None
            },
            {
                "id": "5305e33125fd4a77ad25da81f91a2662",
                "parent": "85351f31387f49338f1b746776c70167",
                "order": 0,
                "dst_type": "text",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 9559,
                        "y1": 16219,
                        "x2": 11490,
                        "y2": 16470
                    },
                    "page": 3,
                    "hash": "1ab418d4a57fdf4f9dbcfcc82d0e777b0b780e7f"
                },
                "content": [
                    "扫描全能王创建"
                ],
                "mark": None
            },
            {
                "id": "69d2ebbfe7084ef29dbeb1450b8eb4e0",
                "parent": "85351f31387f49338f1b746776c70167",
                "order": 0,
                "dst_type": "image",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 1400,
                        "y1": 1239,
                        "x2": 2870,
                        "y2": 1809
                    },
                    "page": 4,
                    "hash": "08506e3344863b17860404a178c0a79a96683e39"
                },
                "content": [
                    "http://zhai-platereduction.ks3-cn-beijing.ksyun.com/tmp/layout/2025-05-07/de32a31a9ade40cbab5ba81d7eef67c0/kdc_cut7.png?Expires=1746688953&AWSAccessKeyId=AKLThacEYfpQEiYtqqtfXFZP&Signature=1%2FVgR2bf3jkB2LpfOu3Fshp8N80%3D"
                ],
                "mark": None
            },
            {
                "id": "21d5071050f74eb3839f652446f1df20",
                "parent": "85351f31387f49338f1b746776c70167",
                "order": 0,
                "dst_type": "table",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 1050,
                        "y1": 1980,
                        "x2": 11009,
                        "y2": 3500
                    },
                    "page": 4,
                    "hash": "bcb4ccddf40f307b301f0818edb0d0e93222cef8"
                },
                "content": [
                    "<table><tr><td rowspan=\"1\" colspan=\"1\">5AESI的上报要求：“本研究中特别关注的不良事件包括：3级及以上肝功能异常(包括涉及肝脏的各类检查、体任和症状)、肌酸激酶升高、乳酸脱氢酶升高、肌无力、肌肉疼痛。注：对于特别关注的不良事件，无论其是严重的或非严重的，都应按照严重不良事件的上报时限和要求进行收集。研究者应在获知AESI后的24小时内报告给申办者的药物警戒部门。”评估该受试者的以上AE符合AESI要求，故上报首次报告报告。</td></tr></table>"
                ],
                "mark": None
            },
            {
                "id": "24944cf562cc4da28f89878468e07335",
                "parent": "85351f31387f49338f1b746776c70167",
                "order": 0,
                "dst_type": "text",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 7100,
                        "y1": 3550,
                        "x2": 9090,
                        "y2": 3929
                    },
                    "page": 4,
                    "hash": "da483c0044d826ee6a005261940dbea52984b95b"
                },
                "content": [
                    "2024.8.22"
                ],
                "mark": None
            },
            {
                "id": "9069ec7c67934e4ca9068ec9c368ffbc",
                "parent": "85351f31387f49338f1b746776c70167",
                "order": 0,
                "dst_type": "text",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 3210,
                        "y1": 3660,
                        "x2": 4269,
                        "y2": 3860
                    },
                    "page": 4,
                    "hash": "bd472afea593b75a2acd1111cf661bd0db0b58c3"
                },
                "content": [
                    "报告者签名："
                ],
                "mark": None
            },
            {
                "id": "b4a50571d3cd4ffbbfbb36620942ae54",
                "parent": "85351f31387f49338f1b746776c70167",
                "order": 0,
                "dst_type": "text",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 6369,
                        "y1": 3660,
                        "x2": 8580,
                        "y2": 3860
                    },
                    "page": 4,
                    "hash": "274f49fa0a841df79fefe80550d9d0b410c71d87"
                },
                "content": [
                    "日期：        "
                ],
                "mark": None
            },
            {
                "id": "a4a23f1245aa42379298491ecbe1854d",
                "parent": "85351f31387f49338f1b746776c70167",
                "order": 0,
                "dst_type": "text",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 9299,
                        "y1": 14900,
                        "x2": 10169,
                        "y2": 15089
                    },
                    "page": 4,
                    "hash": "8a4ffdce358e8fff60d1c031b5307093e7b0a318"
                },
                "content": [
                    "Page 5 of 5"
                ],
                "mark": None
            },
            {
                "id": "d4936ef34efa413d912090c9706659fc",
                "parent": "85351f31387f49338f1b746776c70167",
                "order": 0,
                "dst_type": "image",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 8870,
                        "y1": 16029,
                        "x2": 9500,
                        "y2": 16650
                    },
                    "page": 4,
                    "hash": "a541e7a7e9bc57e34566f0f3bc78b9ac04e8a7fb"
                },
                "content": [
                    "http://zhai-platereduction.ks3-cn-beijing.ksyun.com/tmp/layout/2025-05-07/de32a31a9ade40cbab5ba81d7eef67c0/kdc_cut6.png?Expires=1746688953&AWSAccessKeyId=AKLThacEYfpQEiYtqqtfXFZP&Signature=XBp8roQkStdjUDq8fubvnRyHTjA%3D"
                ],
                "mark": None
            },
            {
                "id": "3ac2087615fb4b76bcb18b95b54ec32f",
                "parent": "85351f31387f49338f1b746776c70167",
                "order": 0,
                "dst_type": "text",
                "attributes": {
                    "level": 10,
                    "position": {
                        "x1": 9559,
                        "y1": 16219,
                        "x2": 11490,
                        "y2": 16470
                    },
                    "page": 4,
                    "hash": "1ab418d4a57fdf4f9dbcfcc82d0e777b0b780e7f"
                },
                "content": [
                    "扫描全能王创建"
                ],
                "mark": None
            }
        ]



class TestPDFChunk(TestCase):
    def setUp(self):
        self.parser = PDFChunk()
        self.kdc = get_pdf_table_dst_1()

    def test_process_chunks(self):
        dstList = convert_to_dst_list(self.kdc)
        chunks = self.parser.process_chunks(dstList, 2)

        print(dst_to_json(chunks))
