import re
import uuid
from collections import defaultdict
from typing import List, Optional, Dict, Tuple

from modules.entity.chunk_entity import DS<PERSON>Node, Chunk
from modules.entity.dst_entity import DST, DSTType, MarkType, BBox
from modules.entity.chunk_entity import LabelType
from modules.pipeline.context import ChunkInfo
from modules.entity.kdc_enttiy import OutlineLevel

# CHUNK_SIZE = 512
# MIN_CHUNK_SIZE = 256
# OVERLAP_SIZE = 50
OUTLINE_SIZE = 50


def calculate_tree_depth(node, current_depth=0):
    """
    Recursively calculates the depth of the tree.

    :param node: The current DSTNode.
    :param current_depth: The current depth in the recursion.
    :return: The maximum depth of the tree.
    """
    if not node.children:
        return current_depth
    return max(calculate_tree_depth(child, current_depth + 1) for child in node.children)


def build_dst_tree(dst_list: List[DST]) -> DSTNode:
    # Group nodes by their parent
    parent_map = defaultdict(list)
    for node in dst_list:
        parent_map[node.parent].append(node)

    # Recursive function to build the tree
    def build_tree(parent_id: str) -> List[DSTNode]:
        children = []
        for child in parent_map.get(parent_id, []):
            node = DSTNode(
                blocks=[child],
                children=build_tree(child.id)  # Recursively build child nodes
            )
            children.append(node)
        return children

    # Build the tree starting from the root node (parent == -1)
    root_blocks = parent_map.get("-1", [])
    tree = DSTNode(
        blocks=root_blocks,
        children=build_tree("-1")
    )
    return tree


def process_text_blocks(node):
    content = ""
    level = 0
    for block in node.blocks:
        if block.dst_type == DSTType.TEXT:  # Filter blocks with dst_type = text
            content += "".join(block.content)
            level = block.attributes.level  # Concatenate content
    return len(content), content, level


def are_all_dst_images(dst_list):
    """
    Check if all elements in the dst list have dst_type as DSTType.IMAGE.

    :param dst_list: List of DST objects.
    :return: True if all dst objects have dst_type as DSTType.IMAGE, False otherwise.
    """
    return all(dst.dst_type == DSTType.IMAGE for dst in dst_list)


def find_last_semantic_symbol(content: str, return_details: bool = False):
    """
    查找内容中最后一个语义结束符号（。！？）的位置

    :param content: 要搜索的文本内容
    :param return_details: 是否返回详细信息，False时只返回位置索引
    :return: 如果return_details=True，返回包含exists、position、symbol的字典；
             否则返回位置索引（找不到时返回-1）
    """
    if not content:
        return {"exists": False, "position": -1, "symbol": None} if return_details else -1

    semantic_symbols = ['。', '！', '？']
    last_pos = -1
    last_symbol = None

    for symbol in semantic_symbols:
        pos = content.rfind(symbol)
        if pos > last_pos:
            last_pos = pos
            last_symbol = symbol

    if return_details:
        return {
            "exists": last_pos > -1,
            "position": last_pos,
            "symbol": last_symbol
        }
    else:
        return last_pos


def check_semantic_symbols(text):
    """检查是否存在语义符号并返回最后一个位置（保持向后兼容）"""
    return find_last_semantic_symbol(text, return_details=True)


def _find_last_semantic_ending(content: str) -> int:
    """查找内容中最后一个语义结束符号的位置（保持向后兼容）"""
    return find_last_semantic_symbol(content, return_details=False)


def _update_outline(depth: int, level: int, content: str, first_outline: str, outline: str) -> Tuple[str, str]:
    """
    根据深度和层级更新大纲信息

    Args:
        depth: 当前节点在树中的深度
        level: 当前内容的层级（<10表示标题）
        content: 当前节点的文本内容
        first_outline: 第一级大纲
        outline: 当前大纲

    Returns:
        tuple: (更新后的first_outline, 更新后的outline)
    """
    new_first_outline = first_outline
    new_outline = outline

    if depth == 1 and level < OutlineLevel.l10:
        # 第一层且为标题，设置为第一级大纲
        new_outline = content
        new_first_outline = content
    elif depth == 2 and level < OutlineLevel.l10:
        # 第二层且为标题，组合第一级和第二级大纲
        new_outline = first_outline + " " + content

    # 限制大纲长度，避免过长
    if len(new_outline) > OUTLINE_SIZE:
        new_outline = new_outline[:OUTLINE_SIZE]

    return new_first_outline, new_outline


def _create_chunk_from_dsts(
        chunk_id: str,
        content: str,
        dsts: List[DST],
        page_size: int,
        label: LabelType = None,
        mark: MarkType = None
) -> Chunk:
    """
    从DST列表创建Chunk对象的工具方法

    Args:
        chunk_id: chunk的唯一标识
        content: chunk的文本内容
        dsts: DST对象列表
        page_size: 页面大小
        label: chunk的标签类型，如果为None则自动判断
        mark: chunk的标记类型

    Returns:
        Chunk: 创建的chunk对象
    """
    if label is None:
        label = LabelType.IMAGE if are_all_dst_images(dsts) else LabelType.TEXT

    return Chunk(
        chunk_id=chunk_id,
        content=content,
        page_size=page_size,
        label=label,
        page_num=sorted(set(d.attributes.page for d in dsts)),
        block=[d.id for d in dsts],
        dsts=dsts,
        mark=mark
    )


def _find_semantic_split_point(chunk: Chunk) -> Tuple[int, bool]:
    """
    在chunk的DST列表中找到语义拆分点

    Args:
        chunk: 要查找拆分点的chunk

    Returns:
        tuple: (DST索引, 语义符号是否在DST末尾)
                返回(-1, False)表示没有找到合适的拆分点
    """
    for i in range(len(chunk.dsts) - 1, -1, -1):
        dst = chunk.dsts[i]
        dst_content = dst.content[0]
        semantic_result = check_semantic_symbols(dst_content)

        if semantic_result["exists"]:
            pos = semantic_result["position"]
            # 检查语义符号是否在DST的末尾
            symbol_at_end = (pos == len(dst_content) - 1)
            return i, symbol_at_end

    return -1, False


def _split_chunk_semantically(
        last_chunk: Chunk,
        page_size: int,
        outline: str
) -> Tuple[List[Chunk], str, List[int], List[str], List[DST]]:
    """
    按语义符号拆分chunk

    Args:
        last_chunk: 需要拆分的chunk
        page_size: 页面大小

    Returns:
        tuple: (创建的chunk列表, 剩余内容, 剩余页面, 剩余block_ids, 剩余DSTs)
    """
    chunks = []
    last_semantic_pos = _find_last_semantic_ending(last_chunk.content)

    if last_semantic_pos <= 0 or last_semantic_pos >= len(last_chunk.content) - 1:
        # 没有找到合适的拆分位置，直接返回原chunk
        chunks.append(last_chunk)
        return chunks, "", [], [], []

    # 按语义位置拆分内容
    semantic_content = last_chunk.content[:last_semantic_pos + 1]
    remaining_content = last_chunk.content[last_semantic_pos + 1:].strip()

    # 找到对应的DST拆分点
    dst_split_index, symbol_at_dst_end = _find_semantic_split_point(last_chunk)

    if dst_split_index == -1:
        # 没有找到DST拆分点，直接保存原chunk
        chunks.append(last_chunk)
        return chunks, "", [], [], []

    if symbol_at_dst_end:
        # 语义符号在DST末尾，包含到拆分点（含）
        semantic_dsts = last_chunk.dsts[:dst_split_index + 1]
        remaining_dsts = last_chunk.dsts[dst_split_index + 1:]
    else:
        # 语义符号不在DST末尾，包含该DST到前一个chunk
        semantic_dsts = last_chunk.dsts[:dst_split_index + 1]
        remaining_dsts = last_chunk.dsts[dst_split_index:]

    # 创建语义拆分后的chunk
    semantic_chunk = _create_chunk_from_dsts(
        chunk_id=uuid.uuid4().hex,
        content=semantic_content,
        dsts=semantic_dsts,
        page_size=page_size,
        label=last_chunk.label
    )
    chunks.append(semantic_chunk)

    # 准备剩余内容的数据
    if remaining_content and remaining_dsts:
        remaining_pages = sorted(set(d.attributes.page for d in remaining_dsts))
        remaining_blocks = [d.id for d in remaining_dsts]
        return chunks, f"{outline} {remaining_content}".strip(), remaining_pages, remaining_blocks, remaining_dsts

    return chunks, "", [], [], []


def _process_last_chunk(
        last_chunk: Optional[Chunk],
        block_content_len: int,
        chunk_size: int,
        page_size: int,
        outline: str
) -> Tuple[List[Chunk], str, List[int], List[str], List[DST]]:
    """
    处理上一个chunk的合并或拆分逻辑

    Args:
        last_chunk: 上一个未完成的chunk
        block_content_len: 当前节点的内容长度
        chunk_size: chunk大小限制
        page_size: 页面大小

    Returns:
        tuple: (创建的chunk列表, 当前chunk内容, 当前页面列表, 当前block列表, 当前DST列表)
    """
    if not last_chunk:
        return [], "", [], [], []

    if block_content_len + len(last_chunk.content) <= chunk_size:
        # 可以合并，返回last_chunk的内容用于合并
        return [], last_chunk.content, last_chunk.page_num, last_chunk.block, last_chunk.dsts

    # 需要单独处理last_chunk
    # 检查最后一个DST是否为图片
    last_dst_is_image = False
    if last_chunk.dsts and len(last_chunk.dsts) > 0:
        last_dst = last_chunk.dsts[-1]
        last_dst_is_image = last_dst.dst_type == DSTType.IMAGE

    if last_dst_is_image:
        # 最后一个DST是图片，不进行语义拆分，直接保存
        return [last_chunk], "", [], [], []

    # 尝试按语义符号拆分
    return _split_chunk_semantically(last_chunk, page_size, outline)


def _should_create_new_chunk_for_title(
        current_chunk_content: str,
        outline: str,
        last_chunk: Optional[Chunk],
        dst_level: int
) -> bool:
    """
    判断是否应该为标题创建新的chunk

    Args:
        current_chunk_content: 当前chunk的内容
        outline: 大纲内容
        last_chunk: 上一个chunk
        dst_level: 当前DST的级别

    Returns:
        bool: 是否应该创建新chunk
    """
    return (
            current_chunk_content and
            current_chunk_content != outline and
            last_chunk and
            last_chunk.dsts[-1].attributes.level > dst_level
    )


def _handle_title_dst(
        dst_level: int,
        current_chunk_content: str,
        outline: str,
        last_chunk: Optional[Chunk],
        current_chunk_pages: List[int],
        current_chunk_blocks: List[str],
        current_chunk_dsts: List[DST],
        page_size: int
) -> Tuple[Optional[Chunk], str, List[int], List[str], List[DST], int]:
    """
    处理标题类型的DST

    Returns:
        tuple: (创建的chunk或None, 更新后的内容, 更新后的页面列表, 更新后的block列表, 更新后的DST列表, chunk级别)
    """
    chunk_to_add = None

    if _should_create_new_chunk_for_title(current_chunk_content, outline, last_chunk, dst_level):
        # 创建新chunk来保存当前内容
        chunk_to_add = _create_chunk_from_dsts(
            chunk_id=uuid.uuid4().hex,
            content=current_chunk_content,
            dsts=current_chunk_dsts,
            page_size=page_size
        )

        # 重置状态，开始新chunk
        current_chunk_content = outline
        current_chunk_pages = []
        current_chunk_blocks = []
        current_chunk_dsts = []

    return chunk_to_add, current_chunk_content, current_chunk_pages, current_chunk_blocks, current_chunk_dsts, dst_level


def _handle_image_dst(
        dst: DST,
        current_chunk_pages: List[int],
        current_chunk_blocks: List[str],
        current_chunk_dsts: List[DST]
) -> Tuple[Optional[str], List[int], List[str], List[DST], bool]:
    """
    处理图片类型的DST

    Returns:
        tuple: (图片的文本内容或None, 更新后的页面列表, 更新后的block列表, 更新后的DST列表, 是否应该继续处理)
    """
    if len(dst.content) <= 1:
        # 纯图片内容，直接添加到当前chunk
        current_chunk_pages.append(dst.attributes.page)
        current_chunk_blocks.append(dst.id)
        current_chunk_dsts.append(dst)
        return None, current_chunk_pages, current_chunk_blocks, current_chunk_dsts, False
    else:
        # 图片带有描述文本，返回描述文本继续处理
        return dst.content[1], current_chunk_pages, current_chunk_blocks, current_chunk_dsts, True


def _handle_special_dst(
        dst: DST,
        current_chunk_pages: List[int],
        current_chunk_blocks: List[str],
        current_chunk_dsts: List[DST]
) -> Tuple[List[int], List[str], List[DST]]:
    """
    处理特殊类型的DST（MINDMAP, SPREADSHEET, FLOWCHART, OTHER）

    Returns:
        tuple: (更新后的页面列表, 更新后的block列表, 更新后的DST列表)
    """
    current_chunk_pages.append(dst.attributes.page)
    current_chunk_blocks.append(dst.id)
    current_chunk_dsts.append(dst)
    return current_chunk_pages, current_chunk_blocks, current_chunk_dsts


def _handle_table_dst(
        dst: DST,
        dst_content: str,
        current_chunk_content: str,
        outline: str,
        current_chunk_pages: List[int],
        current_chunk_blocks: List[str],
        current_chunk_dsts: List[DST],
        page_size: int
) -> Tuple[List[Chunk], str, List[int], List[str], List[DST]]:
    """
    处理表格类型的DST

    Returns:
        tuple: (创建的chunk列表, 重置后的内容, 重置后的页面列表, 重置后的block列表, 重置后的DST列表)
    """
    chunks = []

    # 首先完成当前正在构建的chunk（如果有内容）
    should_finalize_current = (
            (current_chunk_content and current_chunk_content != outline) or
            (current_chunk_content == "" and len(current_chunk_dsts) > 0)
    )

    if should_finalize_current:
        current_chunk = _create_chunk_from_dsts(
            chunk_id=uuid.uuid4().hex,
            content=current_chunk_content,
            dsts=current_chunk_dsts,
            page_size=page_size
        )
        chunks.append(current_chunk)

    # 为表格创建单独的chunk
    table_chunk = _create_chunk_from_dsts(
        chunk_id=uuid.uuid4().hex,
        content=dst_content,
        dsts=[dst],
        page_size=page_size,
        label=LabelType.TABLE,
        mark=dst.mark
    )
    chunks.append(table_chunk)

    # 重置状态
    return chunks, outline, [], [], []


def _build_text_content(
        current_chunk_content: str,
        dst_content: str,
        outline: str,
        last_chunk: Optional[Chunk]
) -> str:
    """
    构建文本内容

    Args:
        current_chunk_content: 当前chunk的内容
        dst_content: 当前DST的内容
        outline: 大纲内容
        last_chunk: 上一个chunk

    Returns:
        str: 构建后的内容
    """
    # current_chunk_content为空出现在加上图片ocr内容之后超出了chunk_size大小的情况
    if not current_chunk_content:
        # 当前chunk为空，开始新的内容
        if last_chunk is not None:
            return outline + " " + dst_content
        else:
            return dst_content
    else:
        if current_chunk_content == outline and dst_content in current_chunk_content:
            # 当前chunk与大纲相同，添加DST内容
            return current_chunk_content
        else:
            # 追加到现有内容
            return current_chunk_content + " " + dst_content


def _create_mergeable_chunk(
        current_chunk_content: str,
        current_chunk_pages: List[int],
        current_chunk_blocks: List[str],
        current_chunk_dsts: List[DST],
        page_size: int,
        has_children: bool
) -> Optional[Chunk]:
    """
    创建可合并的chunk

    Returns:
        Optional[Chunk]: 创建的可合并chunk，如果没有内容则返回None
    """
    if not current_chunk_dsts or len(current_chunk_dsts) == 0:
        return None

    mergeable_chunk = _create_chunk_from_dsts(
        chunk_id=uuid.uuid4().hex,
        content=current_chunk_content,
        dsts=current_chunk_dsts,
        page_size=page_size
    )

    # 如果有子节点，在内容末尾添加分隔符
    if has_children:
        mergeable_chunk.content = mergeable_chunk.content + " \n\n "

    return mergeable_chunk


def _add_separator_to_last_chunk(chunks: List[Chunk], has_children: bool) -> None:
    """
    为最后一个chunk添加分隔符（如果有子节点）

    Args:
        chunks: chunk列表
        has_children: 是否有子节点
    """
    if has_children and chunks and len(chunks) > 0:
        chunks[-1].content = chunks[-1].content + " \n\n "


def build_chunk(node: DSTNode, page_size: int, depth: int, first_outline: str, outline: str, chunks_info: ChunkInfo,
                last_chunk: Optional[Chunk] = None) -> (
        List[Chunk], Optional[Chunk]):
    """
    构建文档块(chunk)的核心方法，支持递归处理文档树结构

    主要功能：
    1. 从DST节点树构建文档块
    2. 处理大纲和标题的层级关系
    3. 支持与上一个chunk的合并或拆分
    4. 按照语义符号进行智能拆分
    5. 特殊处理图片、表格等不同类型的内容

    Args:
        node: 当前处理的DST节点
        page_size: 页面大小
        depth: 当前节点在树中的深度
        first_outline: 第一级大纲标题
        outline: 当前大纲标题
        chunks_info: 包含chunk_size和min_chunk_size的配置信息
        last_chunk: 上一个未完成的chunk，可能需要与当前内容合并

    Returns:
        tuple: (已完成的chunk列表, 可能需要与后续内容合并的chunk)
    """
    # 1. 初始化配置参数
    chunk_size = chunks_info.chunk_size
    min_chunk_size = chunks_info.min_chunk_size
    chunks = []
    current_chunk_content = ""
    current_chunk_pages = []
    current_chunk_blocks = []
    current_chunk_dsts = []

    # 2. 处理大纲更新
    block_content_len, content, level = process_text_blocks(node)
    first_outline, outline = _update_outline(depth, level, content, first_outline, outline)

    # 3. 处理上一个chunk的合并或拆分
    last_chunk_results = _process_last_chunk(last_chunk, block_content_len, chunk_size, page_size, outline)
    last_chunks, current_chunk_content, current_chunk_pages, current_chunk_blocks, current_chunk_dsts = last_chunk_results
    chunks.extend(last_chunks)

    # 4. 处理当前节点的blocks
    current_chunk_level = None  # 追踪当前chunk的level类型

    for dst in node.blocks or []:
        # 跳过根节点、页眉、页脚
        if dst.dst_type == DSTType.ROOT or (dst.mark == MarkType.HEADER or dst.mark == MarkType.FOOTER or dst.mark == MarkType.CATALOG):
            continue

        dst_content = "".join(dst.content)
        dst_level = dst.attributes.level if hasattr(dst.attributes, 'level') else OutlineLevel.l10

        # 4.1 处理标题类型的DST
        if dst_level != OutlineLevel.l10:  # 遇到标题
            title_result = _handle_title_dst(
                dst_level, current_chunk_content, outline, last_chunk,
                current_chunk_pages, current_chunk_blocks, current_chunk_dsts, page_size
            )
            chunk_to_add, current_chunk_content, current_chunk_pages, current_chunk_blocks, current_chunk_dsts, current_chunk_level = title_result
            if chunk_to_add:
                chunks.append(chunk_to_add)
        elif current_chunk_level is not None and current_chunk_level != OutlineLevel.l10:
            # 当前chunk以标题开始，现在遇到正文，这是正常的连接
            current_chunk_level = OutlineLevel.l10  # 更新为正文模式

        # 4.2 处理图片类型的DST
        if dst.dst_type == DSTType.IMAGE:
            image_result = _handle_image_dst(dst, current_chunk_pages, current_chunk_blocks, current_chunk_dsts)
            image_content, current_chunk_pages, current_chunk_blocks, current_chunk_dsts, should_continue = image_result

            if not should_continue:
                # 纯图片内容，已经添加到chunk中，继续下一个DST
                continue
            else:
                # 图片带有描述文本，使用描述文本作为dst_content继续处理
                dst_content = image_content

        # 4.3 处理其他特殊类型的DST
        elif dst.dst_type in [DSTType.MINDMAP, DSTType.SPREADSHEET, DSTType.FLOWCHART, DSTType.OTHER]:
            current_chunk_pages, current_chunk_blocks, current_chunk_dsts = _handle_special_dst(
                dst, current_chunk_pages, current_chunk_blocks, current_chunk_dsts
            )
            continue

        # 4.4 处理表格类型的DST
        elif dst.dst_type == DSTType.TABLE:
            table_result = _handle_table_dst(
                dst, dst_content, current_chunk_content, outline,
                current_chunk_pages, current_chunk_blocks, current_chunk_dsts, page_size
            )
            table_chunks, current_chunk_content, current_chunk_pages, current_chunk_blocks, current_chunk_dsts = table_result
            chunks.extend(table_chunks)
            continue

        # 4.5 处理文本内容
        current_chunk_content = _build_text_content(current_chunk_content, dst_content, outline, last_chunk)

        # 添加DST信息到当前chunk
        current_chunk_pages.append(dst.attributes.page)
        current_chunk_blocks.append(dst.id)
        current_chunk_dsts.append(dst)

        # 设置当前chunk的level类型
        if current_chunk_level is None:
            current_chunk_level = dst_level

    # 5. 创建可合并的chunk
    has_children = node.children and len(node.children) > 0
    mergeable_chunk = _create_mergeable_chunk(
        current_chunk_content, current_chunk_pages, current_chunk_blocks,
        current_chunk_dsts, page_size, has_children
    )

    # 为没有mergeable_chunk但有children的情况添加分隔符
    if not mergeable_chunk:
        _add_separator_to_last_chunk(chunks, has_children)

    # 6. 递归处理子节点
    for child in node.children or []:
        child_chunks, child_mergeable_chunk = build_chunk(
            child, page_size, depth + 1, first_outline, outline,
            chunks_info, mergeable_chunk
        )

        if child_chunks:
            mergeable_chunk = None  # 重置mergeable_chunk
            chunks.extend(child_chunks)

        # 更新mergeable_chunk为子节点返回的可合并chunk
        mergeable_chunk = child_mergeable_chunk or mergeable_chunk

    return chunks, mergeable_chunk


def get_closest_to_overlap(strings: List[str], dsts: List[DST], overlap_size: int) -> Tuple[List[str], List[DST]]:
    """
    从输入列表末尾开始，收集字符串和对应的DST对象，直到总长度接近目标重叠大小。

    工作原理：
    1. 从末尾开始遍历字符串和DST
    2. 检查每个字符串是否存在于当前DST的内容中
    3. 如果存在，将字符串和DST(不重复)添加到结果中
    4. 如果不存在，尝试前一个DST

    :param strings: 字符串列表
    :param dsts: 对应的DST对象列表
    :param overlap_size: 目标重叠大小
    :return: 元组(收集的字符串列表, 对应的DST对象列表)
    """
    # 初始化结果列表和跟踪变量
    result_strings = []  # 存储符合条件的字符串
    result_dsts = []  # 存储对应的DST对象
    added_dst_ids = set()  # 用于跟踪已添加的DST ID，避免重复
    total_length = 0  # 当前累积的字符总长度

    # 初始化索引指针，从末尾开始遍历
    string_index = len(strings) - 1
    dst_index = len(dsts) - 1

    # 只要还有字符串需要处理且有DST可供匹配
    while string_index >= 0 and dst_index >= 0:
        # 获取当前处理的字符串及其长度
        current_string = strings[string_index]
        string_length = len(current_string)

        # 如果添加此字符串会超出目标大小，则停止处理
        if total_length + string_length > overlap_size:
            break

        # 获取当前DST及其内容
        current_dst = dsts[dst_index]
        dst_content = "".join(current_dst.content)

        # 判断当前字符串是否在当前DST的内容中
        if current_string in dst_content:
            # 字符串匹配成功，添加到结果列表前端(保持原顺序)
            result_strings.insert(0, current_string)
            total_length += string_length

            # 如果此DST尚未添加过，则添加到结果列表中
            if current_dst.id not in added_dst_ids:
                result_dsts.insert(0, current_dst)
                added_dst_ids.add(current_dst.id)

            # 处理下一个字符串
            string_index -= 1
        else:
            # 当前DST不包含该字符串，尝试检查前一个DST
            dst_index -= 1

    return result_strings, result_dsts


def split_semantically(
        dst_list: List[DST],
        max_length: int,
        page_size: int,
        chunk_overlap: int
) -> (List[Chunk], Optional[Chunk]):
    # Define the semantic delimiters
    delimiters = r"(?<=。|？|！)|\n\n  \n"
    # Build the map
    dst_content_map: Dict[str, List[str]] = {
        dst.id: (
            [dst.content[1]]  # Add the entire content as a single segment for images
            if dst.dst_type == DSTType.IMAGE and len(dst.content) > 1
            else [
                segment.strip()
                for segment in re.split(
                    delimiters,
                    " ".join(dst.content)
                )
                if segment.strip()
            ]
        )
        for dst in dst_list
    }
    # 初始化变量
    split_chunks = []
    remaining_chunks = None
    current_chunk_content = ""
    current_chunk_pages = []
    current_chunk_dsts = []
    before_content = []
    before_dst = []

    # 使用集合跟踪当前chunk中已添加的DST ID
    current_chunk_dst_ids = set()

    for dst in dst_list:
        dst_id = dst.id
        split_content = dst_content_map[dst_id]
        for word in split_content:
            if len(current_chunk_content) > 0 and len(current_chunk_content) + len(word) > max_length:
                split_chunks.append(Chunk(
                    chunk_id=uuid.uuid4().hex,
                    page_size=page_size,
                    content=current_chunk_content,
                    label=LabelType.TEXT,
                    page_num=list(set(current_chunk_pages)),
                    block=[d.id for d in current_chunk_dsts],
                    dsts=current_chunk_dsts
                ))

                # 重置集合和列表
                current_chunk_dst_ids.clear()

                # 添加overlap内容
                overlap_content, overlap_dst = get_closest_to_overlap(before_content, before_dst, chunk_overlap)
                current_chunk_content = " ".join(overlap_content) + " " + word

                # 添加overlap的DSTs（去重）
                current_chunk_dsts = []
                current_chunk_pages = []

                for d in overlap_dst:
                    if d.id not in current_chunk_dst_ids:
                        current_chunk_dsts.append(d)
                        current_chunk_pages.append(d.attributes.page)
                        current_chunk_dst_ids.add(d.id)

                # 添加当前DST（如果不重复）
                if dst.id not in current_chunk_dst_ids:
                    current_chunk_dsts.append(dst)
                    current_chunk_pages.append(dst.attributes.page)
                    current_chunk_dst_ids.add(dst.id)

                before_dst = current_chunk_dsts.copy()
                before_content = overlap_content + [word]
                continue

            before_content.append(word)
            before_dst.append(dst)
            current_chunk_content = current_chunk_content + ' ' + word

            # 只在DST未添加时才添加
            if dst.id not in current_chunk_dst_ids:
                current_chunk_pages.append(dst.attributes.page)
                current_chunk_dsts.append(dst)
                current_chunk_dst_ids.add(dst.id)

    # 处理剩余内容
    if current_chunk_content:
        remaining_chunks = Chunk(chunk_id=uuid.uuid4().hex,
                                 content=current_chunk_content,
                                 label=LabelType.TEXT,
                                 page_size=page_size,
                                 page_num=list(set(current_chunk_pages)),
                                 block=[d.id for d in current_chunk_dsts],
                                 dsts=current_chunk_dsts
                                 )

    return split_chunks, remaining_chunks


def extract_bbox_values(dst_list):
    if not dst_list:
        return None, None
    first_dst = dst_list[0].attributes.position.bbox
    last_dst = dst_list[-1].attributes.position.bbox

    # Ensure the position is of type BBox
    if isinstance(first_dst, BBox) and isinstance(last_dst, BBox):
        # 实际上应该是第一个y1最小，最后一个y2最大
        y1_latest = min(first_dst.y1, last_dst.y1)
        y2_largest = max(first_dst.y2, last_dst.y2)
        return y1_latest, y2_largest
    return None, None


def get_bbox_height(dst):
    """
    Calculate the height of the bbox (y2 - y1) from dst's attributes.position.

    :param dst: A dst object containing attributes.position with bbox coordinates.
    :return: The height of the bbox (y2 - y1) or None if position is not available.
    """
    position = getattr(dst.attributes, 'position', None)
    if isinstance(position, BBox):
        return position.y2 - position.y1
    return 0


def get_extreme_bbox_values(previous_page_dsts, next_page_dsts):
    """
    Get the latest y1 and largest y2 values from the first and last dsts of the given pages.

    :param previous_page_dsts: List of dst objects from the previous page.
    :param next_page_dsts: List of dst objects from the next page.
    :return: A dictionary with the latest y1 and largest y2 values.
    """

    # Extract values for both pages
    prev_y1, prev_y2 = extract_bbox_values(previous_page_dsts)
    next_y1, next_y2 = extract_bbox_values(next_page_dsts)

    # Combine results
    return {
        "y1_latest": min(prev_y1, next_y1) if prev_y1 is not None and next_y1 is not None else None,
        "y2_largest": max(prev_y2, next_y2) if prev_y2 is not None and next_y2 is not None else None,
    }
