# coding: utf-8
from commons.trace.tracer import trace_span
from modules.cross_table.cross_page_merge import cross_table_merge, cross_table_merge_v2
from modules.entity.chunk_entity import Chunk
from modules.entity.dst_entity import MarkType
from modules.entity.chunk_entity import LabelType
from modules.flows.chunk.chunk_template import ChunkTemplate

from typing import List
from modules.entity.dst_entity import DST
from modules.flows.chunk.common import build_dst_tree, build_chunk
from modules.flows.filters.header_tail_filter import  merge_header_footer_into_chunk
from modules.pipeline.context import ChunkInfo


def merge_table_chunks(chunks):
    # Step 1: Merge TABLE chunks, skipping HEADER/FOOTER chunks
    i = 0
    final_chunks = []
    while i < len(chunks):
        current_chunk = chunks[i]
        temp_chunks = []
        if current_chunk.label == LabelType.TABLE:
            merged_page_nums = set(current_chunk.page_num)
            temp_chunks.append(current_chunk)
            cross_dst_count = 0
            while (
                    i + 1 < len(chunks)
                    and ((chunks[i + 1].label == LabelType.TABLE and abs(
                chunks[i + 1].page_num[0] - max(merged_page_nums)) == 1) or chunks[i + 1].mark in {
                             MarkType.HEADER, MarkType.FOOTER})
            ):
                # Skip HEADER/FOOTER chunks
                if chunks[i + 1].mark in {MarkType.HEADER, MarkType.FOOTER}:
                    i += 1
                    continue
                i += 1
                temp_chunks.append(chunks[i])
                merged_page_nums.update(chunks[i].page_num)
        for idx, chunk in enumerate(temp_chunks):
            if idx > 0:
                chunk.pre_chunk = temp_chunks[idx - 1].chunk_id  # Set the previous chunk's ID
            else:
                chunk.pre_chunk = None  # No previous chunk for the first chunk

            if idx < len(temp_chunks) - 1:
                chunk.next_chunk = temp_chunks[idx + 1].chunk_id  # Set the next chunk's ID
            else:
                chunk.next_chunk = None
        if len(temp_chunks) == 0:
            final_chunks.append(current_chunk)
        else:
            final_chunks.extend(temp_chunks)
        i += 1

    return final_chunks


class PDFChunk(ChunkTemplate):
    """
    Implementation of ChunkTemplate for DOCX files.
    Handles the logic for processing chunks specific to DOCX format.
    """

    def _build_initial_chunks(self, dst_list: List[DST], page_size: int, chunks_info: ChunkInfo) -> List[Chunk]:
        """
        Build initial chunks from DST list.
        This is the common logic shared between sync and async methods.
        """
        tree = build_dst_tree(dst_list)
        # print("Tree structure:", tree)
        chunks = []
        chunk, merge_chunk = build_chunk(tree, page_size, -1, "", "", chunks_info)
        if chunk:
            chunks.extend(chunk)
        if merge_chunk:
            chunks.append(merge_chunk)
        return chunks

    def _add_header_footer_chunks(self, merge_chunks: List[Chunk], dst_list: List[DST], page_size: int) -> List[Chunk]:
        """
        Add header and footer chunks to the merged chunks.
        This is the common logic shared between sync and async methods.
        """
        header_tail_chunk = merge_header_footer_into_chunk(dst_list, page_size)
        if header_tail_chunk:
            merge_chunks.append(header_tail_chunk)
        return merge_chunks

    @trace_span
    def process_chunks(self, dst_list: List[DST], page_size: int, chunks_info: ChunkInfo) -> List[Chunk]:
        chunks = self._build_initial_chunks(dst_list, page_size, chunks_info)
        merge_chunks = cross_table_merge(chunks)
        return self._add_header_footer_chunks(merge_chunks, dst_list, page_size)

    @trace_span
    async def process_chunks_async(self, dst_list: List[DST], page_size: int, chunks_info: ChunkInfo) -> List[Chunk]:
        chunks = self._build_initial_chunks(dst_list, page_size, chunks_info)
        merge_chunks = await cross_table_merge_v2(chunks)
        return self._add_header_footer_chunks(merge_chunks, dst_list, page_size)
