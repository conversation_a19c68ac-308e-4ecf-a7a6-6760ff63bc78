from unittest import TestCase

from modules.entity.dst_entity import dst_to_json
from modules.flows.chunk.ppt import PPTChunk
from modules.flows.chunk.test_docx import convert_to_dst_list


def dst_data():
    return [
    {
        "id": "0e1d5b4a220349faac16680a87e10c94",
        "parent": "-1",
        "order": 0,
        "dst_type": "root",
        "attributes": {
            "level": 0,
            "position": {
                "x1": 1,
                "y1": 2,
                "x2": 3,
                "y2": 4
            },
            "page": 0,
            "hash": "roothashhashhashhashhashhashhashhashhashhash"
        },
        "content": [
            "根节点"
        ]
    },
    {
        "id": "8bd1609716954e6789cc29b3473ed56d",
        "parent": "0e1d5b4a220349faac16680a87e10c94",
        "order": 0,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "x1": 2443,
                "y1": 1723,
                "x2": 10332,
                "y2": 2122
            },
            "page": 0,
            "hash": "cf11926543f10af457726b8de08aa7cc5f71d54e"
        },
        "content": [
            "合同编号：",
            "JZFW",
            "2024726",
            "66"
        ]
    },
    {
        "id": "8994208f7703496489566415dd1a32fb",
        "parent": "0e1d5b4a220349faac16680a87e10c94",
        "order": 0,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "x1": 2803,
                "y1": 2523,
                "x2": 10332,
                "y2": 2922
            },
            "page": 0,
            "hash": "02d8dfb064d413dfdb2409e0a3add9b956d75b29"
        },
        "content": [
            "月嫂服务合同"
        ]
    },
    {
        "id": "22959bbb3b8041df9cf0ac742719a8c0",
        "parent": "0e1d5b4a220349faac16680a87e10c94",
        "order": 0,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "x1": 2503,
                "y1": 3323,
                "x2": 10332,
                "y2": 3722
            },
            "page": 0,
            "hash": "fb26fc38d0f285e385e821f7458f8a61e51ba0b8"
        },
        "content": [
            "甲方：",
            "张伟"
        ]
    },
    {
        "id": "ec91c7c7b8464ec290438eebb77cad4d",
        "parent": "0e1d5b4a220349faac16680a87e10c94",
        "order": 0,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "x1": 2503,
                "y1": 3723,
                "x2": 10332,
                "y2": 4122
            },
            "page": 1,
            "hash": "16b32400e4e506785b5a5a9897084792f5ac9b2c"
        },
        "content": [
            "住所：",
            "滨海市沿江大道101号七里河畔花园小区"
        ]
    },
    {
        "id": "6f8b0c4ae08c4b1eaa63a6253d5272dd",
        "parent": "0e1d5b4a220349faac16680a87e10c94",
        "order": 0,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "x1": 2503,
                "y1": 4123,
                "x2": 10332,
                "y2": 4522
            },
            "page": 1,
            "hash": "df85d6a8a9aa7bf517e83d7bc567c4e4337b0009"
        },
        "content": [
            "联系电话：",
            "123451236352"
        ]
    },
    {
        "id": "7b107158d69748d48fb40a5f97b450b5",
        "parent": "0e1d5b4a220349faac16680a87e10c94",
        "order": 0,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "x1": 2503,
                "y1": 4523,
                "x2": 10332,
                "y2": 4922
            },
            "page": 1,
            "hash": "1c238a7455f6a151aea1c18ec4ca86968b76e4ed"
        },
        "content": [
            "身份证号码：",
            "7894588456464644654545"
        ]
    },
    {
        "id": "da6393afec594e6b8bd5e03299748839",
        "parent": "0e1d5b4a220349faac16680a87e10c94",
        "order": 0,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "x1": 2503,
                "y1": 4923,
                "x2": 10332,
                "y2": 5322
            },
            "page": 1,
            "hash": "17b029fd82032e43d6068eb95229415809ceee8b"
        },
        "content": [
            "月嫂（乙方）：",
            "李华"
        ]
    },
    {
        "id": "cfceed2833164cef9bbd16a79acc7477",
        "parent": "0e1d5b4a220349faac16680a87e10c94",
        "order": 0,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "x1": 2503,
                "y1": 5323,
                "x2": 10332,
                "y2": 5722
            },
            "page": 2,
            "hash": "679d4dd183b8b429aa8f471de9a705c541590a6a"
        },
        "content": [
            "住所：",
            "东海市中关村大街115号利民小区"
        ]
    },
    {
        "id": "682a604eaf7b45528c9fde91e2d5e6ec",
        "parent": "0e1d5b4a220349faac16680a87e10c94",
        "order": 0,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "x1": 2503,
                "y1": 5723,
                "x2": 10332,
                "y2": 6122
            },
            "page": 2,
            "hash": "48974a3a248031ee000fe539b43adc0f89db1d84"
        },
        "content": [
            "联系电话：",
            "12378545254"
        ]
    },
    {
        "id": "7ea3ac9a10134bdfbf971eee79e0d6a6",
        "parent": "0e1d5b4a220349faac16680a87e10c94",
        "order": 0,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "x1": 2503,
                "y1": 6123,
                "x2": 10332,
                "y2": 6522
            },
            "page": 2,
            "hash": "29cae7847a3a4a0ca13018e46016e6e060b8051f"
        },
        "content": [
            "身份证号码：",
            "4654564654645616553223"
        ]
    },
    {
        "id": "cdf1deed4ef94634a94b48dfc96aa0f3",
        "parent": "0e1d5b4a220349faac16680a87e10c94",
        "order": 0,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "x1": 2503,
                "y1": 6523,
                "x2": 10332,
                "y2": 7722
            },
            "page": 3,
            "hash": "f7cb44d57933bebb2a067ccd3c4ccd7f91d8b43a"
        },
        "content": [
            "甲方有雇用母婴护理员（简称月嫂）方面的需求，乙方愿意为甲方提供月嫂服务，并承诺为甲方提供专业、优质、安全的月嫂服务。为了明确双方权利和义务，经平等协商，特订立如下合同，双方共同遵守："
        ]
    },
    {
        "id": "e68fd26f002543839c292cb188a5e798",
        "parent": "0e1d5b4a220349faac16680a87e10c94",
        "order": 0,
        "dst_type": "text",
        "attributes": {
            "level": 1,
            "position": {
                "x1": 2503,
                "y1": 8123,
                "x2": 10332,
                "y2": 8522
            },
            "page": 3,
            "hash": "ac1845342b91d6aae29a4a6ba51e7f61260480b8"
        },
        "content": [
            "第一条 ",
            "甲乙双方约定事项"
        ]
    }
]


class TestPPTChunk(TestCase):

    def setUp(self):
        self.parser = PPTChunk()
        self.kdc = dst_data()

    def test_process_chunks(self):
        dstList = convert_to_dst_list(self.kdc)
        chunks = self.parser.process_chunks(dstList, 2)

        print(dst_to_json(chunks))
