# coding: utf-8
import re
import uuid
from typing import Dict

from commons.trace.tracer import trace_span
from modules.entity.chunk_entity import DSTNode, Chunk
from modules.entity.dst_entity import DSTType
from modules.flows.chunk.chunk_template import ChunkTemplate

from collections import defaultdict
from typing import List, Optional
from modules.entity.dst_entity import DST
from modules.flows.chunk.common import split_semantically, build_dst_tree, build_chunk
from modules.pipeline.context import ChunkInfo, FileInfo

MIN_PAGE_SIZE = 1024
MAX_PAGE_SIZE = 2000


class OtlChunk(ChunkTemplate):
    """
    Implementation of ChunkTemplate for DOCX files.
    Handles the logic for processing chunks specific to DOCX format.
    """

    @trace_span
    def process_chunks(self, dst_list: List[DST], page_size: int, chunks_info: ChunkInfo) -> List[Chunk]:
        tree = build_dst_tree(
            dst_list)
        # print("Tree structure:", tree)
        chunks = []
        chunk, merge_chunk = build_chunk(tree, page_size,-1,"", "", chunks_info)
        if chunk:
            chunks.extend(chunk)
        if merge_chunk:
            chunks.append(merge_chunk)
        chunks = self.otl_page_split(chunks)
        return chunks

    def otl_page_split(self, chunks: List[Chunk]) -> List[Chunk]:
        """
        根据内容长度将chunks划分到不同页面
        规则:
        1. 尽量将chunks合并至长度在MIN_PAGE_SIZE和MAX_PAGE_SIZE之间
        2. 如果添加新chunk后总长度超过MAX_PAGE_SIZE，则将已有chunks分到当前页面，新chunk作为下一页面的起始
        3. 特殊处理大型table（长度超过MAX_PAGE_SIZE的table单独占一页）
        """

        def update_chunks(temp_chunks, page):
            """更新chunks及其包含的dst的页码"""
            for chunk in temp_chunks:
                for dst in chunk.dsts:
                    dst.attributes.page = page
                chunk.page_num = [page]

        current_page = 0
        combined_content = ""
        temp_chunks = []

        idx = 0
        while idx < len(chunks):
            chunk = chunks[idx]

            if chunk.label == "table":
                chunk_content_length = len(re.sub(r'<.*?>|\s', '', chunk.content))
                # 特殊处理大型table
                if chunk_content_length > MAX_PAGE_SIZE:
                    # 先处理已累积的chunks
                    if temp_chunks:
                        update_chunks(temp_chunks, current_page)
                        current_page += 1
                        temp_chunks = []
                        combined_content = ""

                    # 单独处理大型table，作为单独的一页
                    update_chunks([chunk], current_page)
                    current_page += 1
                    idx += 1
                    continue
            else:
                chunk_content_length = len(chunk.content)

            # 计算添加当前chunk后的总长度
            new_combined_length = len(combined_content) + chunk_content_length

            # 情况1: 添加后长度仍小于MIN_PAGE_SIZE，继续添加
            if new_combined_length < MIN_PAGE_SIZE:
                temp_chunks.append(chunk)
                combined_content += chunk.content
                idx += 1

            # 情况2: 添加后长度在理想范围内
            elif MIN_PAGE_SIZE <= new_combined_length <= MAX_PAGE_SIZE:
                temp_chunks.append(chunk)
                combined_content += chunk.content
                update_chunks(temp_chunks, current_page)
                current_page += 1
                temp_chunks = []
                combined_content = ""
                idx += 1

            # 情况3: 添加后长度超过MAX_PAGE_SIZE
            else:
                # 如果当前没有累积的chunks，单个chunk就超过限制，将当前chunk作为单独一页
                if not temp_chunks:
                    temp_chunks.append(chunk)
                    update_chunks(temp_chunks, current_page)
                    current_page += 1
                    temp_chunks = []
                    combined_content = ""
                    idx += 1
                else:
                    # 将当前累积的chunks分配到当前页面
                    update_chunks(temp_chunks, current_page)
                    current_page += 1
                    temp_chunks = []
                    combined_content = ""
                    # 不增加idx，下一轮处理当前chunk

        # 处理最后剩余的chunks
        if temp_chunks:
            update_chunks(temp_chunks, current_page)

        for chunk in chunks:
            chunk.page_size = current_page
        return chunks

