import json
from unittest import TestCase

from modules.entity.dst_entity import DST, dst_to_json, DSTType, DSTAttribute
from modules.flows.chunk.common import split_semantically
from modules.flows.chunk.docx import DocxChunk


def dst_list_json_data():
    return [
        {
            "id": "35f04dad95f541ea8f05b351d961d52d",
            "parent": "-1",
            "order": 0,
            "dst_type": "root",
            "attributes": {
                "level": 0,
                "position": {
                    "x1": 1,
                    "y1": 2,
                    "x2": 3,
                    "y2": 4
                },
                "page": 0,
                "hash": "roothashhashhashhashhashhashhashhashhashhash"
            },
            "content": [
                "根节点"
            ]
        },
        {
            "id": "3ac4b1794f914bb099375475abb4b89b",
            "parent": "35f04dad95f541ea8f05b351d961d52d",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 1,
                "position": {
                    "x1": 1700,
                    "y1": 6798,
                    "x2": 10771,
                    "y2": 8045
                },
                "page": 0,
                "hash": "f0b5a07170b27c68c9d37bb5ec78715193a6fe1f"
            },
            "content": [
                "WPS",
                "邮箱",
                "归档系统操作",
                "手册"
            ]
        },
        {
            "id": "d1c88feb11834fef841a91633058908d",
            "parent": "3ac4b1794f914bb099375475abb4b89b",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 1700,
                    "y1": 18615,
                    "x2": 10771,
                    "y2": 19238
                },
                "page": 1,
                "hash": "b858cb282617fb0956d960215c8e84d1ccf909c6"
            },
            "content": [
                " "
            ]
        },
        {
            "id": "c67a7160a8f144d692a4511a69a94c2f",
            "parent": "3ac4b1794f914bb099375475abb4b89b",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 1700,
                    "y1": 19239,
                    "x2": 10771,
                    "y2": 19862
                },
                "page": 1,
                "hash": "a9c9f43b154efed5dac685f201f5c2266aaae7ca"
            },
            "content": [
                "\u0013",
                "TOC \\o \"1-3\" \\h \\u ",
                "\u0014",
                "\u0013",
                " HYPERLINK \\l _Toc1401783719 ",
                "\u0014",
                "WPS",
                "邮箱",
                "归档系统操作",
                "手册",
                "\t",
                "\u0013",
                " PAGEREF _Toc1401783719 \\h ",
                "\u0014",
                "1",
                "\u0015",
                "\u0015"
            ]
        },
        {
            "id": "414ce5df042b4702831468f0e71ac931",
            "parent": "3ac4b1794f914bb099375475abb4b89b",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 1700,
                    "y1": 19863,
                    "x2": 10771,
                    "y2": 20486
                },
                "page": 1,
                "hash": "35b6597322f0399e789f438f7bf552b105d1e845"
            },
            "content": [
                "\u0013",
                " HYPERLINK \\l _Toc709047230 ",
                "\u0014",
                "WPS",
                "邮箱",
                "归档系统",
                "简介",
                "\t",
                "\u0013",
                " PAGEREF _Toc709047230 \\h ",
                "\u0014",
                "2",
                "\u0015",
                "\u0015"
            ]
        },
        {
            "id": "0fa8651c21354f74819dae310fe4ac62",
            "parent": "3ac4b1794f914bb099375475abb4b89b",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 1700,
                    "y1": 20487,
                    "x2": 10771,
                    "y2": 21110
                },
                "page": 1,
                "hash": "3fb32597ce0ac24e8662282da9cdc91dc3c78e52"
            },
            "content": [
                "\u0013",
                " HYPERLINK \\l _Toc1756342386 ",
                "\u0014",
                "WPS",
                "邮箱",
                "归档系统操作说明",
                "\t",
                "\u0013",
                " PAGEREF _Toc1756342386 \\h ",
                "\u0014",
                "2",
                "\u0015",
                "\u0015"
            ]
        },
        {
            "id": "845c0204933d49a3a0be94cf140a1921",
            "parent": "3ac4b1794f914bb099375475abb4b89b",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 2120,
                    "y1": 21111,
                    "x2": 10771,
                    "y2": 21734
                },
                "page": 1,
                "hash": "6f907f9eb95ee9d402d2307cefa890cd8b2c9b60"
            },
            "content": [
                "\u0013",
                " HYPERLINK \\l _Toc1301109345 ",
                "\u0014",
                "权限设置",
                "\t",
                "\u0013",
                " PAGEREF _Toc1301109345 \\h ",
                "\u0014",
                "2",
                "\u0015",
                "\u0015"
            ]
        },
        {
            "id": "5aecc5414eb24e06a10f1e86c2281e00",
            "parent": "3ac4b1794f914bb099375475abb4b89b",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 2120,
                    "y1": 21735,
                    "x2": 10771,
                    "y2": 22358
                },
                "page": 1,
                "hash": "1f110e0fda03839fe2499cf0316df00646ba3a8d"
            },
            "content": [
                "\u0013",
                " HYPERLINK \\l _Toc1769866204 ",
                "\u0014",
                "归档设置",
                "\t",
                "\u0013",
                " PAGEREF _Toc1769866204 \\h ",
                "\u0014",
                "3",
                "\u0015",
                "\u0015"
            ]
        },
        {
            "id": "a59b9348cfee4342b48b71c386ba18d8",
            "parent": "3ac4b1794f914bb099375475abb4b89b",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 2540,
                    "y1": 22359,
                    "x2": 10771,
                    "y2": 22982
                },
                "page": 1,
                "hash": "8163012c29accb31b6a71adff4cbe86db5cfc9f0"
            },
            "content": [
                "\u0013",
                " HYPERLINK \\l _Toc1462116986 ",
                "\u0014",
                "修改归档保留时间",
                "\t",
                "\u0013",
                " PAGEREF _Toc1462116986 \\h ",
                "\u0014",
                "4",
                "\u0015",
                "\u0015"
            ]
        },
        {
            "id": "b990d44d948043b2bcd5d07f151ebf5e",
            "parent": "3ac4b1794f914bb099375475abb4b89b",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 2540,
                    "y1": 22983,
                    "x2": 10771,
                    "y2": 23606
                },
                "page": 1,
                "hash": "fe119855583388d72d6457a4d3fe32f6dcf7defa"
            },
            "content": [
                "\u0013",
                " HYPERLINK \\l _Toc987940486 ",
                "\u0014",
                "修改归档范围",
                "\t",
                "\u0013",
                " PAGEREF _Toc987940486 \\h ",
                "\u0014",
                "5",
                "\u0015",
                "\u0015"
            ]
        },
        {
            "id": "0f9d42e5d11b44b2857ae089f3efcfc2",
            "parent": "3ac4b1794f914bb099375475abb4b89b",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 2120,
                    "y1": 23607,
                    "x2": 10771,
                    "y2": 24230
                },
                "page": 1,
                "hash": "846bcb5d7957ed4c443d600d6c901db46298b47b"
            },
            "content": [
                "\u0013",
                " HYPERLINK \\l _Toc98415285 ",
                "\u0014",
                "归档库",
                "\t",
                "\u0013",
                " PAGEREF _Toc98415285 \\h ",
                "\u0014",
                "6",
                "\u0015",
                "\u0015"
            ]
        },
        {
            "id": "de7ac5ac0b134db294fe9175c1e4841c",
            "parent": "3ac4b1794f914bb099375475abb4b89b",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 2540,
                    "y1": 24231,
                    "x2": 10771,
                    "y2": 24854
                },
                "page": 1,
                "hash": "5ede448398ecfb08465c1514c963a0d060eabe74"
            },
            "content": [
                "\u0013",
                " HYPERLINK \\l _Toc1576546427 ",
                "\u0014",
                "查看归档邮件",
                "\t",
                "\u0013",
                " PAGEREF _Toc1576546427 \\h ",
                "\u0014",
                "6",
                "\u0015",
                "\u0015"
            ]
        },
        {
            "id": "9371c4f88ded429aade0fcd5cb0a830a",
            "parent": "3ac4b1794f914bb099375475abb4b89b",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 2540,
                    "y1": 24855,
                    "x2": 10771,
                    "y2": 25478
                },
                "page": 1,
                "hash": "020e2d84b116611522ebaeaf5bdb7b8196f06d92"
            },
            "content": [
                "\u0013",
                " HYPERLINK \\l _Toc1207463869 ",
                "\u0014",
                "重新投递归档邮件",
                "\t",
                "\u0013",
                " PAGEREF _Toc1207463869 \\h ",
                "\u0014",
                "7",
                "\u0015",
                "\u0015"
            ]
        },
        {
            "id": "ae0e4c512c044fda89209d3ffa8697e0",
            "parent": "3ac4b1794f914bb099375475abb4b89b",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 2540,
                    "y1": 25479,
                    "x2": 10771,
                    "y2": 26102
                },
                "page": 1,
                "hash": "dfca9a1628e11f57c15d9c9cd6d44036c6e8431d"
            },
            "content": [
                "\u0013",
                " HYPERLINK \\l _Toc1105446768 ",
                "\u0014",
                "导出归档邮件",
                "\t",
                "\u0013",
                " PAGEREF _Toc1105446768 \\h ",
                "\u0014",
                "8",
                "\u0015",
                "\u0015"
            ]
        },
        {
            "id": "7a709bb69243436caf23a5ec850c48a5",
            "parent": "3ac4b1794f914bb099375475abb4b89b",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 2120,
                    "y1": 26103,
                    "x2": 10771,
                    "y2": 26726
                },
                "page": 1,
                "hash": "4312b45fe4f5ee3b10273ef982cf32869ef3b449"
            },
            "content": [
                "\u0013",
                " HYPERLINK \\l _Toc917093359 ",
                "\u0014",
                "操作日志",
                "\t",
                "\u0013",
                " PAGEREF _Toc917093359 \\h ",
                "\u0014",
                "9",
                "\u0015",
                "\u0015"
            ]
        },
        {
            "id": "7dcd77cfff8649b69ae1b217df783cf4",
            "parent": "3ac4b1794f914bb099375475abb4b89b",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 1700,
                    "y1": 26727,
                    "x2": 10771,
                    "y2": 27350
                },
                "page": 1,
                "hash": "7762eabf9387fe8ec5d648cd3b1d9eb6d820caa2"
            },
            "content": [
                "\u0015"
            ]
        },
        {
            "id": "8f86dcf2dd1a43d2a5ca69eac4e6c7eb",
            "parent": "35f04dad95f541ea8f05b351d961d52d",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 1,
                "position": {
                    "x1": 2083,
                    "y1": 36225,
                    "x2": 10388,
                    "y2": 36998
                },
                "page": 2,
                "hash": "000ebd5fbe2e63c39f55b2eb18305b2ab9c24368"
            },
            "content": [
                "WPS",
                "邮箱",
                "归档系统",
                "简介"
            ]
        },
        {
            "id": "c36cb08ae2814d64b5650987b9950a3c",
            "parent": "8f86dcf2dd1a43d2a5ca69eac4e6c7eb",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 2083,
                    "y1": 37219,
                    "x2": 10388,
                    "y2": 38766
                },
                "page": 2,
                "hash": "8da0490c59cf0878ca7f86330e68f7c6eb0478a8"
            },
            "content": [
                "WPS邮箱归档系统主要用于管理和存储电子邮件，能有效解决企业邮件在用户邮箱中占用存储大的问题。同时支持提供给企业管理者对企业邮件资源的访问和管理权限。企业管理者可以通过归档范围管控敏感邮箱、备份关键邮件，也可通过邮件归档系统满足数据保留、审计等合规性要求。"
            ]
        },
        {
            "id": "660daeeaf06a4efa82770b12de50ace6",
            "parent": "8f86dcf2dd1a43d2a5ca69eac4e6c7eb",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 2083,
                    "y1": 38767,
                    "x2": 10388,
                    "y2": 39153
                },
                "page": 2,
                "hash": "4e88392ecba700f7580915137229478a1bcc152a"
            },
            "content": [
                "访问地址：https://email.wps.cn/kmail-archive"
            ]
        },
        {
            "id": "ccd8cb5818484e409f0022b4fe88f3ff",
            "parent": "35f04dad95f541ea8f05b351d961d52d",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 1,
                "position": {
                    "x1": 2083,
                    "y1": 39414,
                    "x2": 10388,
                    "y2": 40187
                },
                "page": 2,
                "hash": "2f29e1dceebcc45e073ae650ac39caca967b58be"
            },
            "content": [
                "WPS",
                "邮箱",
                "归档系统操作说明"
            ]
        },
        {
            "id": "8656c8fb77fd4f2996049f997a432b37",
            "parent": "ccd8cb5818484e409f0022b4fe88f3ff",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 2,
                "position": {
                    "x1": 2083,
                    "y1": 40448,
                    "x2": 10388,
                    "y2": 41221
                },
                "page": 2,
                "hash": "6a054167582f1cbe45b024d8678eaedf1b59ec5f"
            },
            "content": [
                "权限设置"
            ]
        },
        {
            "id": "cada058e955349b4a7b8248a4e4db8b3",
            "parent": "8656c8fb77fd4f2996049f997a432b37",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 2083,
                    "y1": 41422,
                    "x2": 10388,
                    "y2": 42195
                },
                "page": 2,
                "hash": "4f5e8a482aeb4a8f51d391f7d72ce245ef97bddb"
            },
            "content": [
                "初始化状态下，仅企业超级管理员可以访问归档系统。超级管理员可为企业成员赋权。系统审计管理员可访问归档设置、归档库和操作日志三个菜单。"
            ]
        },
        {
            "id": "15ce4a9ae36745a78c6f3b8f180e9000",
            "parent": "8656c8fb77fd4f2996049f997a432b37",
            "order": 0,
            "dst_type": "image",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 2083,
                    "y1": 43572,
                    "x2": 10371,
                    "y2": 48230
                },
                "page": 2,
                "hash": "da39a3ee5e6b4b0d3255bfef95601890afd80709"
            },
            "content": [
                ""
            ]
        },
        {
            "id": "18fa1faff79e47c28e71c2bd4d541d4d",
            "parent": "8656c8fb77fd4f2996049f997a432b37",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 2083,
                    "y1": 48428,
                    "x2": 10388,
                    "y2": 48814
                },
                "page": 2,
                "hash": "9ea2d4d1f49cf529d80b79797a5c5af84461bba7"
            },
            "content": [
                "点击【添加】，可从组织架构中选择具体的成员作为审计管理员。"
            ]
        },
        {
            "id": "2161dd04ce5845188af9e35ef7a6363c",
            "parent": "8656c8fb77fd4f2996049f997a432b37",
            "order": 0,
            "dst_type": "image",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 2083,
                    "y1": 53261,
                    "x2": 10376,
                    "y2": 57919
                },
                "page": 3,
                "hash": "da39a3ee5e6b4b0d3255bfef95601890afd80709"
            },
            "content": [
                ""
            ]
        },
        {
            "id": "6c54da7217b44508ab7b2329f4dbfe9a",
            "parent": "8656c8fb77fd4f2996049f997a432b37",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 2083,
                    "y1": 58117,
                    "x2": 10388,
                    "y2": 58503
                },
                "page": 3,
                "hash": "bb95776f1960f020a9f8592da4bca35af4f60bc4"
            },
            "content": [
                "删除已有审计管理员，只需点击标签的【✖️】"
            ]
        },
        {
            "id": "e3ae00609a8247cc86bf048586b7f734",
            "parent": "8656c8fb77fd4f2996049f997a432b37",
            "order": 0,
            "dst_type": "image",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 2083,
                    "y1": 58679,
                    "x2": 10376,
                    "y2": 63337
                },
                "page": 3,
                "hash": "da39a3ee5e6b4b0d3255bfef95601890afd80709"
            },
            "content": [
                ""
            ]
        },
        {
            "id": "ab9866a8f1a74a4dae3747fdfe6ea499",
            "parent": "ccd8cb5818484e409f0022b4fe88f3ff",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 2,
                "position": {
                    "x1": 2083,
                    "y1": 63795,
                    "x2": 10388,
                    "y2": 64568
                },
                "page": 3,
                "hash": "eb9f16105a03bf28e49dce890c9851cda4f34164"
            },
            "content": [
                "归档设置"
            ]
        },
        {
            "id": "e76c7ca479274a2eb6b06a17444584c1",
            "parent": "ab9866a8f1a74a4dae3747fdfe6ea499",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 2083,
                    "y1": 64769,
                    "x2": 10388,
                    "y2": 65155
                },
                "page": 3,
                "hash": "aadda5553016d03ccdfe86c31b5382195faaa8e2"
            },
            "content": [
                "用于设置归档开关状态、归档保留时间和归档范围等基础信息。"
            ]
        },
        {
            "id": "a97142d6b22d44eb88d2db28011aece7",
            "parent": "ab9866a8f1a74a4dae3747fdfe6ea499",
            "order": 0,
            "dst_type": "image",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 2083,
                    "y1": 71960,
                    "x2": 10388,
                    "y2": 76618
                },
                "page": 4,
                "hash": "da39a3ee5e6b4b0d3255bfef95601890afd80709"
            },
            "content": [
                ""
            ]
        },
        {
            "id": "1f449b7f4e744a338070739e58aaf703",
            "parent": "ab9866a8f1a74a4dae3747fdfe6ea499",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 3,
                "position": {
                    "x1": 2083,
                    "y1": 77076,
                    "x2": 10388,
                    "y2": 77849
                },
                "page": 4,
                "hash": "5bb20d2baed3174566914a632cfcd24446da69a3"
            },
            "content": [
                "修改归档保留时间"
            ]
        },
        {
            "id": "90ca0ef1e8eb4b39b210ba417d0dd475",
            "parent": "1f449b7f4e744a338070739e58aaf703",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 2083,
                    "y1": 78030,
                    "x2": 10388,
                    "y2": 78416
                },
                "page": 4,
                "hash": "e390d32f3e1b17c0326cebdbbb0932323b269a70"
            },
            "content": [
                "默认为5年，通过点击选项框来修改保留期限。注意："
            ]
        },
        {
            "id": "00034be56173491cb9b81860d91b5ad5",
            "parent": "1f449b7f4e744a338070739e58aaf703",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 2083,
                    "y1": 78417,
                    "x2": 10388,
                    "y2": 79190
                },
                "page": 4,
                "hash": "d1e9e6dc7654ccacb275eeaaf4dc7d8233e8c816"
            },
            "content": [
                "1、",
                "此设置针对的是后续入归档库的新邮件，已有邮件的归档期限以此邮件进入归档库时的设置为准。"
            ]
        },
        {
            "id": "7948016c64e449948e09dc3f1e8e09c4",
            "parent": "1f449b7f4e744a338070739e58aaf703",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 2083,
                    "y1": 79191,
                    "x2": 10388,
                    "y2": 79577
                },
                "page": 4,
                "hash": "cc47aec49304416760b7d99dc11adcd645a2b772"
            },
            "content": [
                "2、",
                "修改后请点击保存，否则不生效"
            ]
        },
        {
            "id": "43576276fd57427cb3b9b7612594ab34",
            "parent": "1f449b7f4e744a338070739e58aaf703",
            "order": 0,
            "dst_type": "image",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 2083,
                    "y1": 79665,
                    "x2": 10030,
                    "y2": 84123
                },
                "page": 4,
                "hash": "da39a3ee5e6b4b0d3255bfef95601890afd80709"
            },
            "content": [
                ""
            ]
        },
        {
            "id": "4c9bfaba4323452dbe8ce65199cf34cd",
            "parent": "ab9866a8f1a74a4dae3747fdfe6ea499",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 3,
                "position": {
                    "x1": 2083,
                    "y1": 87328,
                    "x2": 10388,
                    "y2": 88101
                },
                "page": 5,
                "hash": "f318f53fa882e41206d44a1add30385f0fa0740f"
            },
            "content": [
                "修改归档范围"
            ]
        },
        {
            "id": "b892db1e777e47e09edc7ba012866fa0",
            "parent": "4c9bfaba4323452dbe8ce65199cf34cd",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 2083,
                    "y1": 88282,
                    "x2": 10388,
                    "y2": 89055
                },
                "page": 5,
                "hash": "d6e30260a5f01a22e88a5903b1dad51ad9b994d4"
            },
            "content": [
                "选择本组织全部邮箱，默认会将所有成员和公共邮箱的收发件纳入归档库。若只需管控特定邮箱的邮件，只需选择【本组织部分邮箱】："
            ]
        },
        {
            "id": "0da85fb91f484b7e9672ec09d0b73d21",
            "parent": "4c9bfaba4323452dbe8ce65199cf34cd",
            "order": 0,
            "dst_type": "image",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 2083,
                    "y1": 89231,
                    "x2": 10365,
                    "y2": 93883
                },
                "page": 5,
                "hash": "da39a3ee5e6b4b0d3255bfef95601890afd80709"
            },
            "content": [
                ""
            ]
        },
        {
            "id": "aa8c17b94d314528b93672bda5123905",
            "parent": "4c9bfaba4323452dbe8ce65199cf34cd",
            "order": 0,
            "dst_type": "image",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 2083,
                    "y1": 94262,
                    "x2": 10371,
                    "y2": 98920
                },
                "page": 5,
                "hash": "da39a3ee5e6b4b0d3255bfef95601890afd80709"
            },
            "content": [
                ""
            ]
        },
        {
            "id": "66e78bd73c5c494983bf29c9fe0afaf0",
            "parent": "4c9bfaba4323452dbe8ce65199cf34cd",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 2083,
                    "y1": 99118,
                    "x2": 10388,
                    "y2": 99504
                },
                "page": 5,
                "hash": "d6e49e6cd3d13978f4f1c216155c100d680e57ab"
            },
            "content": [
                "添加完毕记得点击【保存】："
            ]
        },
        {
            "id": "656499ca1f804af782cd7d7b3c8e6667",
            "parent": "4c9bfaba4323452dbe8ce65199cf34cd",
            "order": 0,
            "dst_type": "image",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 2083,
                    "y1": 104624,
                    "x2": 10371,
                    "y2": 109293
                },
                "page": 6,
                "hash": "da39a3ee5e6b4b0d3255bfef95601890afd80709"
            },
            "content": [
                ""
            ]
        },
        {
            "id": "ec96e28155ee4ba8864685080e5304a6",
            "parent": "ccd8cb5818484e409f0022b4fe88f3ff",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 2,
                "position": {
                    "x1": 2083,
                    "y1": 109740,
                    "x2": 10388,
                    "y2": 110513
                },
                "page": 6,
                "hash": "17b59d03fc5ed1b6a9f01a622e7f8dbdd408b176"
            },
            "content": [
                "归档库"
            ]
        },
        {
            "id": "5460515d7c6148a9a8fcafe6e4b2ce67",
            "parent": "ec96e28155ee4ba8864685080e5304a6",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 2083,
                    "y1": 110714,
                    "x2": 10388,
                    "y2": 111100
                },
                "page": 6,
                "hash": "dddf726ab370f0a4382c7eda545475bef5e6b666"
            },
            "content": [
                "归档库用于存放归档邮箱的出入邮件。"
            ]
        },
        {
            "id": "bc97b32de3ab48f39e95422b4a64f8dc",
            "parent": "ec96e28155ee4ba8864685080e5304a6",
            "order": 0,
            "dst_type": "image",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 2083,
                    "y1": 111261,
                    "x2": 10382,
                    "y2": 115948
                },
                "page": 6,
                "hash": "da39a3ee5e6b4b0d3255bfef95601890afd80709"
            },
            "content": [
                ""
            ]
        },
        {
            "id": "8bc053a22c0b4b008ddc162310e7d333",
            "parent": "ec96e28155ee4ba8864685080e5304a6",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 3,
                "position": {
                    "x1": 2083,
                    "y1": 116392,
                    "x2": 10388,
                    "y2": 117165
                },
                "page": 6,
                "hash": "e25a888216bc0d4e448502617bd63724366fb99c"
            },
            "content": [
                "查看归档邮件"
            ]
        },
        {
            "id": "99df8e42061b4f869221a2c94852994d",
            "parent": "8bc053a22c0b4b008ddc162310e7d333",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 2083,
                    "y1": 117346,
                    "x2": 10388,
                    "y2": 117732
                },
                "page": 6,
                "hash": "79a764ba5b6be1cf0c8c4b472f91b56599b17d03"
            },
            "content": [
                "归档库中的邮件可以直接预览。若邮件携带附件，可在线预览附件或下载查看。"
            ]
        },
        {
            "id": "4bc50de37b354f28955b983849ccb6b6",
            "parent": "8bc053a22c0b4b008ddc162310e7d333",
            "order": 0,
            "dst_type": "image",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 2083,
                    "y1": 121745,
                    "x2": 10376,
                    "y2": 126403
                },
                "page": 7,
                "hash": "da39a3ee5e6b4b0d3255bfef95601890afd80709"
            },
            "content": [
                ""
            ]
        },
        {
            "id": "ff63bd823ee644039b13aadae3ccc3c0",
            "parent": "8bc053a22c0b4b008ddc162310e7d333",
            "order": 0,
            "dst_type": "image",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 2083,
                    "y1": 126776,
                    "x2": 10388,
                    "y2": 131427
                },
                "page": 7,
                "hash": "da39a3ee5e6b4b0d3255bfef95601890afd80709"
            },
            "content": [
                ""
            ]
        },
        {
            "id": "a714f2b08a92433caacdc54ea7edd5a3",
            "parent": "ec96e28155ee4ba8864685080e5304a6",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 3,
                "position": {
                    "x1": 2083,
                    "y1": 131892,
                    "x2": 10388,
                    "y2": 132665
                },
                "page": 7,
                "hash": "2e83fcf00473374312e85b12cdfc79efa7ace966"
            },
            "content": [
                "重新投递归档邮件"
            ]
        },
        {
            "id": "b727c898a6294f008b6171d56f80f4db",
            "parent": "a714f2b08a92433caacdc54ea7edd5a3",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 2083,
                    "y1": 132846,
                    "x2": 10388,
                    "y2": 133232
                },
                "page": 7,
                "hash": "f974982724b0712b76b543626ce7627c0b9ccd16"
            },
            "content": [
                "支持将邮件重新投递给【原收件人】。通过列表或在预览时通过按钮操作。"
            ]
        },
        {
            "id": "71f471c45e3f4b17b21fee233b8dfed5",
            "parent": "a714f2b08a92433caacdc54ea7edd5a3",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 2083,
                    "y1": 133233,
                    "x2": 10388,
                    "y2": 134006
                },
                "page": 7,
                "hash": "3912282dcfd5db1c7c9d816b3f0fde729c997055"
            },
            "content": [
                "重新投递不会改变邮件基础信息（如收发件人、主题等），收件人收到的邮件除投递时间外，其他信息与原邮件一致。"
            ]
        },
        {
            "id": "2e9272cc477a4926ac4afcce8d7690b7",
            "parent": "a714f2b08a92433caacdc54ea7edd5a3",
            "order": 0,
            "dst_type": "image",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 2083,
                    "y1": 138881,
                    "x2": 10371,
                    "y2": 143516
                },
                "page": 8,
                "hash": "da39a3ee5e6b4b0d3255bfef95601890afd80709"
            },
            "content": [
                ""
            ]
        },
        {
            "id": "d5a1653a0b764cf09dbd3f0dfd5a5f34",
            "parent": "a714f2b08a92433caacdc54ea7edd5a3",
            "order": 1,
            "dst_type": "image",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 2083,
                    "y1": 143897,
                    "x2": 10382,
                    "y2": 148549
                },
                "page": 8,
                "hash": "da39a3ee5e6b4b0d3255bfef95601890afd80709"
            },
            "content": [
                ""
            ]
        },
        {
            "id": "79aef11cfd5c418aa151ee721c86fe5d",
            "parent": "ec96e28155ee4ba8864685080e5304a6",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 3,
                "position": {
                    "x1": 2083,
                    "y1": 149013,
                    "x2": 10388,
                    "y2": 149786
                },
                "page": 8,
                "hash": "165b2d87807ba05daf61ff8650666f1e0a1ff93e"
            },
            "content": [
                "导出归档邮件"
            ]
        },
        {
            "id": "d62350fe782742c08feb806b3883632c",
            "parent": "79aef11cfd5c418aa151ee721c86fe5d",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 2083,
                    "y1": 149967,
                    "x2": 10388,
                    "y2": 150353
                },
                "page": 8,
                "hash": "238f341425b5ac760f714dbf4fbd1b3871367c14"
            },
            "content": [
                "支持将邮件按eml格式导出到本地。通过列表或在预览时通过按钮操作。"
            ]
        },
        {
            "id": "dcb4255c4bf548129d16fa4924345c1c",
            "parent": "79aef11cfd5c418aa151ee721c86fe5d",
            "order": 0,
            "dst_type": "image",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 2083,
                    "y1": 155987,
                    "x2": 10376,
                    "y2": 160639
                },
                "page": 9,
                "hash": "da39a3ee5e6b4b0d3255bfef95601890afd80709"
            },
            "content": [
                ""
            ]
        },
        {
            "id": "54eed9328cc64e8c93fe03a1d842a837",
            "parent": "ccd8cb5818484e409f0022b4fe88f3ff",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 2,
                "position": {
                    "x1": 2083,
                    "y1": 161103,
                    "x2": 10388,
                    "y2": 161876
                },
                "page": 9,
                "hash": "f4bc877cd2823d3127a7b1e30d812b5e641f0262"
            },
            "content": [
                "操作日志"
            ]
        },
        {
            "id": "554f432089944b4dbf247af9b53ff264",
            "parent": "54eed9328cc64e8c93fe03a1d842a837",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 2083,
                    "y1": 162077,
                    "x2": 10388,
                    "y2": 162463
                },
                "page": 9,
                "hash": "dd4e3a9a5c624ab11bbe3ee70321ad4e2589e00c"
            },
            "content": [
                "归档操作日志记录了超级管理员和审计管理员的各类操作，包含查询操作和查询条件。"
            ]
        },
        {
            "id": "56428389c8e94fa09e344324638e3b3c",
            "parent": "54eed9328cc64e8c93fe03a1d842a837",
            "order": 0,
            "dst_type": "image",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 2083,
                    "y1": 162639,
                    "x2": 10371,
                    "y2": 167297
                },
                "page": 9,
                "hash": "da39a3ee5e6b4b0d3255bfef95601890afd80709"
            },
            "content": [
                ""
            ]
        },
        {
            "id": "d1ee7bdb45c44d10bdb393c17d41a75c",
            "parent": "54eed9328cc64e8c93fe03a1d842a837",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 2083,
                    "y1": 167495,
                    "x2": 10388,
                    "y2": 167881
                },
                "page": 9,
                "hash": "2bc4790851cb47c510abc8790e9ce5257fd1d609"
            },
            "content": [
                "点击单条日志查看详情："
            ]
        },
        {
            "id": "f6c3077330924784a78ac67fe4a9ffbc",
            "parent": "54eed9328cc64e8c93fe03a1d842a837",
            "order": 0,
            "dst_type": "image",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 2083,
                    "y1": 173123,
                    "x2": 10376,
                    "y2": 177769
                },
                "page": 10,
                "hash": "da39a3ee5e6b4b0d3255bfef95601890afd80709"
            },
            "content": [
                ""
            ]
        },
        {
            "id": "536bfab151ea4bf1b2d82c60c07e69d8",
            "parent": "54eed9328cc64e8c93fe03a1d842a837",
            "order": 0,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 2083,
                    "y1": 177964,
                    "x2": 10388,
                    "y2": 178350
                },
                "page": 10,
                "hash": "b86ea6ee2fa9e4708a6e5caeaa242ebd7d829b60"
            },
            "content": [
                "筛选操作类型："
            ]
        },
        {
            "id": "590376523ef64f6191e6e6c8c87d3b22",
            "parent": "54eed9328cc64e8c93fe03a1d842a837",
            "order": 0,
            "dst_type": "image",
            "attributes": {
                "level": 10,
                "position": {
                    "x1": 2083,
                    "y1": 178526,
                    "x2": 10376,
                    "y2": 183189
                },
                "page": 10,
                "hash": "da39a3ee5e6b4b0d3255bfef95601890afd80709"
            },
            "content": [
                ""
            ]
        }
    ]


def convert_to_dst_list(dst_dict_list):
    """
    Converts a list of dictionaries to a list of DST objects.

    :param dst_dict_list: List of dictionaries representing DST objects
    :return: List of DST instances
    """
    return [DST.model_validate(dst_dict) for dst_dict in dst_dict_list]


class TestDocxChunk(TestCase):

    def setUp(self):
        self.parser = DocxChunk()
        self.kdc = dst_list_json_data()
        self.dst_list = [
            DST(
                id="1",
                parent="0",
                order=0,
                dst_type=DSTType.TEXT,
                attributes=DSTAttribute(level=1, position={
                    "x1": 2083,
                    "y1": 178526,
                    "x2": 10376,
                    "y2": 183189
                }, page=1, hash="hashewwetwtewsfsdgsgdgsdsggssdggsdsgdgsgsdgsdggsddsgdgs1"),
                content=["This is the first sentence。 This is the second sentence。",
                         "This is the first sentence。 This is the second sentence。",
                         "This is the first sentence。 This is the second sentence。"]
            ),
            DST(
                id="2",
                parent="0",
                order=1,
                dst_type=DSTType.TEXT,
                attributes=DSTAttribute(level=1, position={
                    "x1": 2083,
                    "y1": 178526,
                    "x2": 10376,
                    "y2": 183189
                }, page=1, hash="hadddddddssdgsdgsgsdgsdgsdhsdhdshdsgdsfsdgdsgdfssh2"),
                content=["This is the third sentence。 This is the fourth sentence。",
                         "This is the third sentence。 This is the fourth sentence。",
                         "This is the third sentence。 This is the fourth sentence。"]
            )
        ]

    def tearDown(self):
        pass

    def test_get_ancestors(self):
        dstList = convert_to_dst_list(self.kdc)
        chunks = self.parser.process_chunks(dstList, 2)
        chunks_json = json.dumps([chunk.model_dump() for chunk in chunks], indent=4, ensure_ascii=False)

        print(chunks_json)

    def test_split_semantically(self):
        content_list = ["This is the first sentence。 This is the second sentence。",
                        "This is the first sentence。 This is the second sentence。",
                        "This is the first sentence。 This is the second sentence。",
                        "This is the third sentence。 This is the fourth sentence。",
                        "This is the third sentence。 This is the fourth sentence。",
                        "This is the third sentence。 This is the fourth sentence。"]
        max_length = 50
        chunk_overlap = 20

        semantic_chunk, semantic_dsts, remaining_content, remaining_dsts = split_semantically(
            content_list, self.dst_list, max_length
        )

        # Assertions
        self.assertEqual(semantic_chunk, "This is the first sentence. This is the second sentence. This is the third")
        self.assertEqual(len(semantic_dsts), 2)
        self.assertEqual(remaining_content, [" sentence. This is the fourth sentence."])
        self.assertEqual(len(remaining_dsts), 1)
        self.assertEqual(remaining_dsts[0].id, "2")
