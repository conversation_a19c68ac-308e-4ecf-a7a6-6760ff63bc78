from collections import defaultdict
from typing import List

from commons.trace.tracer import trace_span
from modules.entity.chunk_entity import DSTN<PERSON>, Chunk
from modules.entity.dst_entity import DST
from modules.flows.chunk.chunk_template import ChunkTemplate
from modules.flows.chunk.common import build_dst_tree, build_chunk, calculate_tree_depth
from modules.pipeline.context import ChunkInfo, FileInfo


class PPTXChunk(ChunkTemplate):
    """
    Implementation of ChunkTemplate for DOCX files.
    Handles the logic for processing chunks specific to DOCX format.
    """
    @trace_span
    def process_chunks(self, dst_list: List[DST], page_size: int, chunks_info: ChunkInfo) -> List[Chunk]:
        tree = build_dst_tree(
            dst_list)
        # print("Tree structure:", tree)
        depth = calculate_tree_depth(tree)
        chunks = []
        if depth == 2:
            page_groups = defaultdict(list)
            for dst in dst_list:
                page_groups[str(dst.attributes.page)].append(dst)
            # Rebuild the tree for each page group and process chunks
            for page_num, group in page_groups.items():
                # print(f"Processing page: {page_num}")
                page_tree = DSTNode(blocks=group, children=[])
                chunk, merge_chunk = build_chunk(page_tree, page_size,-1,"", "", chunks_info)
                if chunk:
                    chunks.extend(chunk)
                if merge_chunk:
                    chunks.append(merge_chunk)

        else:
            chunk, merge_chunk = build_chunk(tree, page_size,-1,"", "", chunks_info)
            if chunk:
                chunks.extend(chunk)
            if merge_chunk:
                chunks.append(merge_chunk)
        return chunks
