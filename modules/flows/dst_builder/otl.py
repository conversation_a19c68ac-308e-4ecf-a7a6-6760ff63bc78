# coding: utf-8
# <AUTHOR> linqi
# @date    : 2025/4/22 11:08


from typing import List, Dict, Any

from modules.entity.dst_entity import FileType, DST
from modules.common import build_media_map, build_root_dst, build_hyperlink_map
from modules.entity.kdc_enttiy import Document, BlockType, Node
from modules.flows.dst_builder.kdc_entity_parse import ParaNode, TableNode, TextboxNode, ComponentNode, HighLightNode
from modules.flows.dst_builder.parse_template import ParseTemplate
from modules.pipeline.context import PipelineContext


async def traverse(id2url: Dict[str, List[Any]], id2text: Dict[str, str], node: Node, parent_id="", ls=None):
    dst_id = parent_id
    for i, block in enumerate(node.blocks or []):
        if block.type == BlockType.para:
            node_dst_id = ParaNode(block, id2url, id2text).process(node.outline_level, parent_id, i+len(ls), ls)
        elif block.type == BlockType.table:
            node_dst_id = await TableNode(block, id2url, id2text).process(node.outline_level, parent_id, i+len(ls), ls)
        elif block.type == BlockType.textbox:
            node_dst_id = TextboxNode(block, id2url, id2text).process(node.outline_level, parent_id, i+len(ls), ls)
        elif block.type == BlockType.component and id2url:
            node_dst_id = ComponentNode(block, id2url, id2text).process(node.outline_level, parent_id, i+len(ls), ls)
        elif block.type == BlockType.highlight:
            node_dst_id = HighLightNode(block, id2url, id2text).process(node.outline_level, parent_id, i+len(ls), ls)
        else:
            continue
        # 如果node_dst_id == parent_id表明没有生成block，所以dst_id不变
        if node_dst_id != parent_id:
            dst_id = node_dst_id
    for child in node.children or []:
        # 给下一层的dst_id是当前层最后一个block的dst_id
        await traverse(id2url, id2text, child, dst_id, ls)


class OTLParse(ParseTemplate):
    def __init__(self):
        super().__init__(FileType.OTL)

    async def dst_generate(self, context: PipelineContext, kdc_data: List[dict]):
        ls = []
        # index = 0
        document = Document().model_validate(kdc_data[0]["doc"])
        id2text = build_hyperlink_map(document.hyperlinks)
        # 建立id和url的映射
        id2url = await build_media_map(context,document.medias)
        root = build_root_dst()
        ls.append(root)
        await traverse(id2url, id2text, document.tree, parent_id=root.id, ls=ls)

        return {0: ls}

    def dst_reprocess(self, context: PipelineContext, dst:  List[DST]):
        pass

    def get_res(self, dst: DST):
        pass

