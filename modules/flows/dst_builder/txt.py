# coding: utf-8
# <AUTHOR> linqi
# @date    : 2025/4/24 14:20

from typing import List

from modules.common import build_media_map, build_root_dst,build_hyperlink_map
from modules.entity.dst_entity import FileType, DST
from modules.entity.kdc_enttiy import Document
from modules.flows.dst_builder.otl import traverse
from modules.flows.dst_builder.parse_template import ParseTemplate
from modules.pipeline.context import PipelineContext


class TXTParse(ParseTemplate):
    def __init__(self):
        super().__init__(FileType.DOC)

    async def dst_generate(self, context: PipelineContext, kdc_data: List[dict]):
        ls = []
        # index = 0
        document = Document().model_validate(kdc_data[0]["doc"])

        # 建立id和url的映射
        id2url = await build_media_map(context,document.medias)
        root = build_root_dst()
        id2text = build_hyperlink_map(document.hyperlinks)
        ls.append(root)
        await traverse(id2url, id2text, document.tree, parent_id=root.id, ls=ls)
        return {0: ls}

    def dst_reprocess(self, context: PipelineContext, dst:  List[DST]):
        pass

    def get_res(self, dst: DST):
        pass

