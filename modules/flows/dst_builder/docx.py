# coding: utf-8
# <AUTHOR> xuwenhao1
# @date    : 2025/4/22
from typing import List, Dict, Any
from modules.common import build_media_map, build_root_dst, build_hyperlink_map
from modules.entity.dst_entity import FileType, DST, get_page_dst, assign_order_to_dst
from modules.entity.kdc_enttiy import Document, Node, BlockType
from modules.flows.dst_builder.kdc_entity_parse import ParaNode, TableNode, TextboxNode, ComponentNode
from modules.flows.dst_builder.parse_template import ParseTemplate
from modules.layout.typesetting import typesetting_correct
from modules.pipeline.context import PipelineContext


async def traverse(id2url: Dict[str, List[Any]], id2text: Dict[str, str], node: Node, parent_id="", ls=None):
    dst_id = parent_id
    for i, block in enumerate(node.blocks or []):
        if block.type == BlockType.para:
            node_dst_id = ParaNode(block, id2url, id2text).process(node.outline_level, parent_id, i + len(ls), ls)
        elif block.type == BlockType.table:
            node_dst_id = await TableNode(block, id2url, id2text).process(node.outline_level, parent_id, i + len(ls),
                                                                          ls)
        elif block.type == BlockType.textbox:
            node_dst_id = TextboxNode(block, id2url, id2text).process(node.outline_level, parent_id, i + len(ls), ls)
        elif block.type == BlockType.component:
            node_dst_id = ComponentNode(block, id2url, id2text).process(node.outline_level, parent_id, i + len(ls), ls)
        else:
            continue
        if node_dst_id != parent_id:
            dst_id = node_dst_id
    for child in node.children or []:
        await traverse(id2url, id2text, child, dst_id, ls)


def is_image(node: Node, images: List[str]) -> bool:
    for i, block in enumerate(node.blocks or []):
        if block.type == BlockType.component and block.component is not None and block.component.type == "image":
            continue
        if block.type == BlockType.para and (
                block.para is None or (block.para.runs is None or len(block.para.runs) == 0)):
            continue
        return False
    for child in node.children or []:
        if not is_image(child, images):
            return False
    return True


class DocxParse(ParseTemplate):
    def __init__(self):
        super().__init__(FileType.DOCX)

    async def dst_generate(self, context: PipelineContext, kdc_data: List[dict]):
        ls = []
        # index = 0
        document = Document().model_validate(kdc_data[0]["doc"])
        id2text = build_hyperlink_map(document.hyperlinks)
        # 建立id和url的映射
        id2url = await build_media_map(context, document.medias)
        root = build_root_dst()
        ls.append(root)
        await traverse(id2url, id2text, document.tree, parent_id=root.id, ls=ls)

        return {0: ls}

    def dst_reprocess(self, context: PipelineContext, dst_list: List[DST]):
        page_dst = get_page_dst(dst_list)
        temp_dst_list,layout = typesetting_correct(dst_list[0],page_dst)
        context.layout = layout
        if temp_dst_list is not None:
            dst_list = temp_dst_list
            dst_list = assign_order_to_dst(dst_list)
        return dst_list

    def get_res(self, dst: DST):
        pass
