import unittest

from modules.entity.dst_entity import dst_to_json
from modules.flows.dst_builder.docx import DocxParse
from modules.pipeline.context import PipelineContext


def docx_kdc_data():
    return [
        {
            "version": "1.0.0",
            "src_format": "docx",
            "src_format_detail": "WPS",
            "dst_format": "kdc",
            "file_info": {
                "total_page_num": 3,
                "is_scan": False
            },
            "doc": {
                "prop": {
                    "page_count": 3,
                    "page_props": [
                        {
                            "size": {
                                "height": 16783,
                                "width": 11850
                            }
                        },
                        {
                            "size": {
                                "height": 16783,
                                "width": 11850
                            }
                        },
                        {
                            "size": {
                                "height": 16783,
                                "width": 11850
                            }
                        }
                    ]
                },
                "tree": {
                    "blocks": None,
                    "children": [
                        {
                            "blocks": [
                                {
                                    "bounding_box": {
                                        "x1": 2443,
                                        "x2": 10332,
                                        "y1": 1723,
                                        "y2": 2122
                                    },
                                    "id": "13586674c8JmakaH19P0",
                                    "index": 1,
                                    "page_index": 0,
                                    "para": {
                                        "prop": {
                                            "alignment": "right",
                                            "def_run_prop": {
                                                "color": "#000000ff",
                                                "font_ascii": "微软雅黑",
                                                "font_east_asia": "微软雅黑",
                                                "size": 10.5
                                            },
                                            "outline_level": 10
                                        },
                                        "runs": [
                                            {
                                                "id": "7le3xxh0vTP1zXkXdWG0",
                                                "prop": {
                                                    "color": "#000000ff",
                                                    "font_ascii": "微软雅黑",
                                                    "font_east_asia": "微软雅黑",
                                                    "size": 9
                                                },
                                                "text": "合同编号："
                                            },
                                            {
                                                "id": "q8w76UBxQ5fKtG8xAGE5",
                                                "prop": {
                                                    "color": "#000000ff",
                                                    "font_ascii": "微软雅黑",
                                                    "font_east_asia": "微软雅黑",
                                                    "size": 9
                                                },
                                                "text": "JZFW"
                                            },
                                            {
                                                "id": "qffZxnsxrdvH99GSxEy9",
                                                "prop": {
                                                    "color": "#000000ff",
                                                    "font_ascii": "微软雅黑",
                                                    "font_east_asia": "微软雅黑",
                                                    "size": 9
                                                },
                                                "text": "2024726"
                                            },
                                            {
                                                "id": "vUKWK5QZSF2QEU9r7B16",
                                                "prop": {
                                                    "color": "#000000ff",
                                                    "font_ascii": "微软雅黑",
                                                    "font_east_asia": "微软雅黑",
                                                    "size": 9
                                                },
                                                "text": "66"
                                            }
                                        ]
                                    },
                                    "type": "para"
                                }
                            ],
                            "children": None,
                            "outline_level": 10
                        },
                        {
                            "blocks": [
                                {
                                    "bounding_box": {
                                        "x1": 2503,
                                        "x2": 10332,
                                        "y1": 2123,
                                        "y2": 2522
                                    },
                                    "id": "7254584DWj3Wzhgt1u19",
                                    "index": 2,
                                    "page_index": 0,
                                    "para": {
                                        "prop": {
                                            "alignment": "right",
                                            "def_run_prop": {
                                                "color": "#000000ff",
                                                "font_ascii": "微软雅黑",
                                                "font_east_asia": "微软雅黑",
                                                "size": 10.5
                                            },
                                            "outline_level": 10
                                        },
                                        "runs": None
                                    },
                                    "type": "para"
                                }
                            ],
                            "children": None,
                            "outline_level": 10
                        },
                        {
                            "blocks": [
                                {
                                    "bounding_box": {
                                        "x1": 2803,
                                        "x2": 10332,
                                        "y1": 2523,
                                        "y2": 2922
                                    },
                                    "id": "230E5FA7zKtovnxt7J20",
                                    "index": 3,
                                    "page_index": 0,
                                    "para": {
                                        "prop": {
                                            "alignment": "center",
                                            "def_run_prop": {
                                                "color": "#000000ff",
                                                "font_ascii": "黑体",
                                                "font_east_asia": "黑体",
                                                "size": 18
                                            },
                                            "outline_level": 10
                                        },
                                        "runs": [
                                            {
                                                "id": "V4DaBTATgSqZTjaRjU20",
                                                "prop": {
                                                    "color": "#000000ff",
                                                    "font_ascii": "黑体",
                                                    "font_east_asia": "黑体",
                                                    "size": 18
                                                },
                                                "text": "月嫂服务合同"
                                            }
                                        ]
                                    },
                                    "type": "para"
                                }
                            ],
                            "children": None,
                            "outline_level": 10
                        },
                        {
                            "blocks": [
                                {
                                    "bounding_box": {
                                        "x1": 2503,
                                        "x2": 10332,
                                        "y1": 2923,
                                        "y2": 3322
                                    },
                                    "id": "1C6A32A5NmkhfiqX1Q27",
                                    "index": 4,
                                    "page_index": 0,
                                    "para": {
                                        "prop": {
                                            "alignment": "left",
                                            "def_run_prop": {
                                                "color": "#000000ff",
                                                "font_ascii": "宋体",
                                                "font_east_asia": "宋体",
                                                "size": 10.5
                                            },
                                            "outline_level": 10
                                        },
                                        "runs": None
                                    },
                                    "type": "para"
                                }
                            ],
                            "children": None,
                            "outline_level": 10
                        },
                        {
                            "blocks": [
                                {
                                    "bounding_box": {
                                        "x1": 2503,
                                        "x2": 10332,
                                        "y1": 3323,
                                        "y2": 3722
                                    },
                                    "id": "08FF604FINnB8FOa6s28",
                                    "index": 5,
                                    "page_index": 0,
                                    "para": {
                                        "prop": {
                                            "alignment": "left",
                                            "def_run_prop": {
                                                "color": "#000000ff",
                                                "font_ascii": "宋体",
                                                "font_east_asia": "宋体",
                                                "size": 10.5
                                            },
                                            "outline_level": 10
                                        },
                                        "runs": [
                                            {
                                                "id": "g8A6QG8GzMM0KIYlYf28",
                                                "prop": {
                                                    "color": "#000000ff",
                                                    "font_ascii": "宋体",
                                                    "font_east_asia": "宋体",
                                                    "size": 10.5
                                                },
                                                "text": "甲方："
                                            },
                                            {
                                                "id": "akVQm7VCevNdK66GEL31",
                                                "prop": {
                                                    "color": "#000000ff",
                                                    "font_ascii": "宋体",
                                                    "font_east_asia": "宋体",
                                                    "size": 10.5
                                                },
                                                "text": "张伟"
                                            }
                                        ]
                                    },
                                    "type": "para"
                                }
                            ],
                            "children": None,
                            "outline_level": 10
                        },
                        {
                            "blocks": [
                                {
                                    "bounding_box": {
                                        "x1": 2503,
                                        "x2": 10332,
                                        "y1": 3723,
                                        "y2": 4122
                                    },
                                    "id": "7210BCEBJGvUskI23Q34",
                                    "index": 6,
                                    "page_index": 0,
                                    "para": {
                                        "prop": {
                                            "alignment": "left",
                                            "def_run_prop": {
                                                "color": "#000000ff",
                                                "font_ascii": "宋体",
                                                "font_east_asia": "宋体",
                                                "size": 10.5
                                            },
                                            "outline_level": 10
                                        },
                                        "runs": [
                                            {
                                                "id": "QUHBN4uYPFd3LLKZtm34",
                                                "prop": {
                                                    "color": "#000000ff",
                                                    "font_ascii": "宋体",
                                                    "font_east_asia": "宋体",
                                                    "size": 10.5
                                                },
                                                "text": "住所："
                                            },
                                            {
                                                "id": "F0vSd3FtTg6ZSkuiFt37",
                                                "prop": {
                                                    "color": "#000000ff",
                                                    "font_ascii": "宋体",
                                                    "font_east_asia": "宋体",
                                                    "size": 10.5
                                                },
                                                "text": "滨海市沿江大道101号七里河畔花园小区"
                                            }
                                        ]
                                    },
                                    "type": "para"
                                }
                            ],
                            "children": None,
                            "outline_level": 10
                        },
                        {
                            "blocks": [
                                {
                                    "bounding_box": {
                                        "x1": 2503,
                                        "x2": 10332,
                                        "y1": 4123,
                                        "y2": 4522
                                    },
                                    "id": "27064ECE0hld2fF18c57",
                                    "index": 7,
                                    "page_index": 0,
                                    "para": {
                                        "prop": {
                                            "alignment": "left",
                                            "def_run_prop": {
                                                "color": "#000000ff",
                                                "font_ascii": "宋体",
                                                "font_east_asia": "宋体",
                                                "size": 10.5
                                            },
                                            "outline_level": 10
                                        },
                                        "runs": [
                                            {
                                                "id": "gkTnXTIEM0WCidKCXI57",
                                                "prop": {
                                                    "color": "#000000ff",
                                                    "font_ascii": "宋体",
                                                    "font_east_asia": "宋体",
                                                    "size": 10.5
                                                },
                                                "text": "联系电话："
                                            },
                                            {
                                                "id": "YgpzCoAM6we5jXtXzl62",
                                                "prop": {
                                                    "color": "#000000ff",
                                                    "font_ascii": "宋体",
                                                    "font_east_asia": "宋体",
                                                    "size": 10.5
                                                },
                                                "text": "123451236352"
                                            }
                                        ]
                                    },
                                    "type": "para"
                                }
                            ],
                            "children": None,
                            "outline_level": 10
                        },
                        {
                            "blocks": [
                                {
                                    "bounding_box": {
                                        "x1": 2503,
                                        "x2": 10332,
                                        "y1": 4523,
                                        "y2": 4922
                                    },
                                    "id": "3BB293E6ZwXQTpC29r75",
                                    "index": 8,
                                    "page_index": 0,
                                    "para": {
                                        "prop": {
                                            "alignment": "left",
                                            "def_run_prop": {
                                                "color": "#000000ff",
                                                "font_ascii": "宋体",
                                                "font_east_asia": "宋体",
                                                "size": 10.5
                                            },
                                            "outline_level": 10
                                        },
                                        "runs": [
                                            {
                                                "id": "9IyVquUrMsNj1UnadK75",
                                                "prop": {
                                                    "color": "#000000ff",
                                                    "font_ascii": "宋体",
                                                    "font_east_asia": "宋体",
                                                    "size": 10.5
                                                },
                                                "text": "身份证号码："
                                            },
                                            {
                                                "id": "pIEbG9O6uQHgwNYyWJ81",
                                                "prop": {
                                                    "color": "#000000ff",
                                                    "font_ascii": "宋体",
                                                    "font_east_asia": "宋体",
                                                    "size": 10.5
                                                },
                                                "text": "7894588456464644654545"
                                            }
                                        ]
                                    },
                                    "type": "para"
                                }
                            ],
                            "children": None,
                            "outline_level": 10
                        },
                        {
                            "blocks": [
                                {
                                    "bounding_box": {
                                        "x1": 2503,
                                        "x2": 10332,
                                        "y1": 4923,
                                        "y2": 5322
                                    },
                                    "id": "6B07914EuqUIqI10W104",
                                    "index": 9,
                                    "page_index": 0,
                                    "para": {
                                        "prop": {
                                            "alignment": "left",
                                            "def_run_prop": {
                                                "color": "#000000ff",
                                                "font_ascii": "宋体",
                                                "font_east_asia": "宋体",
                                                "size": 10.5
                                            },
                                            "outline_level": 10
                                        },
                                        "runs": [
                                            {
                                                "id": "HhpTh2gPY7IGUl1UQ104",
                                                "prop": {
                                                    "color": "#000000ff",
                                                    "font_ascii": "宋体",
                                                    "font_east_asia": "宋体",
                                                    "size": 10.5
                                                },
                                                "text": "月嫂（乙方）："
                                            },
                                            {
                                                "id": "y3DL3K8xSOoWsICiQ111",
                                                "prop": {
                                                    "color": "#000000ff",
                                                    "font_ascii": "宋体",
                                                    "font_east_asia": "宋体",
                                                    "size": 10.5
                                                },
                                                "text": "李华"
                                            }
                                        ]
                                    },
                                    "type": "para"
                                }
                            ],
                            "children": None,
                            "outline_level": 10
                        },
                        {
                            "blocks": [
                                {
                                    "bounding_box": {
                                        "x1": 2503,
                                        "x2": 10332,
                                        "y1": 5323,
                                        "y2": 5722
                                    },
                                    "id": "5014092DjoCrLI20d114",
                                    "index": 10,
                                    "page_index": 0,
                                    "para": {
                                        "prop": {
                                            "alignment": "left",
                                            "def_run_prop": {
                                                "color": "#000000ff",
                                                "font_ascii": "宋体",
                                                "font_east_asia": "宋体",
                                                "size": 10.5
                                            },
                                            "outline_level": 10
                                        },
                                        "runs": [
                                            {
                                                "id": "jtsovbmcZAK58HVje114",
                                                "prop": {
                                                    "color": "#000000ff",
                                                    "font_ascii": "宋体",
                                                    "font_east_asia": "宋体",
                                                    "size": 10.5
                                                },
                                                "text": "住所："
                                            },
                                            {
                                                "id": "Caguu2KBvaV7aOXtp117",
                                                "prop": {
                                                    "color": "#000000ff",
                                                    "font_ascii": "宋体",
                                                    "font_east_asia": "宋体",
                                                    "size": 10.5
                                                },
                                                "text": "东海市中关村大街115号利民小区"
                                            }
                                        ]
                                    },
                                    "type": "para"
                                }
                            ],
                            "children": None,
                            "outline_level": 10
                        },
                        {
                            "blocks": [
                                {
                                    "bounding_box": {
                                        "x1": 2503,
                                        "x2": 10332,
                                        "y1": 5723,
                                        "y2": 6122
                                    },
                                    "id": "40DE74C2lRUpar17q134",
                                    "index": 11,
                                    "page_index": 0,
                                    "para": {
                                        "prop": {
                                            "alignment": "left",
                                            "def_run_prop": {
                                                "color": "#000000ff",
                                                "font_ascii": "宋体",
                                                "font_east_asia": "宋体",
                                                "size": 10.5
                                            },
                                            "outline_level": 10
                                        },
                                        "runs": [
                                            {
                                                "id": "8lPMYSTGgHtJzSFll134",
                                                "prop": {
                                                    "color": "#000000ff",
                                                    "font_ascii": "宋体",
                                                    "font_east_asia": "宋体",
                                                    "size": 10.5
                                                },
                                                "text": "联系电话："
                                            },
                                            {
                                                "id": "3Vcdk3Ftnc6LMdoKK139",
                                                "prop": {
                                                    "color": "#000000ff",
                                                    "font_ascii": "宋体",
                                                    "font_east_asia": "宋体",
                                                    "size": 10.5
                                                },
                                                "text": "12378545254"
                                            }
                                        ]
                                    },
                                    "type": "para"
                                }
                            ],
                            "children": None,
                            "outline_level": 10
                        },
                        {
                            "blocks": [
                                {
                                    "bounding_box": {
                                        "x1": 2503,
                                        "x2": 10332,
                                        "y1": 6123,
                                        "y2": 6522
                                    },
                                    "id": "59C7FDD18GyKXl29Q151",
                                    "index": 12,
                                    "page_index": 0,
                                    "para": {
                                        "prop": {
                                            "alignment": "left",
                                            "def_run_prop": {
                                                "color": "#000000ff",
                                                "font_ascii": "宋体",
                                                "font_east_asia": "宋体",
                                                "size": 10.5
                                            },
                                            "outline_level": 10
                                        },
                                        "runs": [
                                            {
                                                "id": "TDq7aqm3fxo7FQGdx151",
                                                "prop": {
                                                    "color": "#000000ff",
                                                    "font_ascii": "宋体",
                                                    "font_east_asia": "宋体",
                                                    "size": 10.5
                                                },
                                                "text": "身份证号码："
                                            },
                                            {
                                                "id": "AoUgI7xeuRnMo8jmR157",
                                                "prop": {
                                                    "color": "#000000ff",
                                                    "font_ascii": "宋体",
                                                    "font_east_asia": "宋体",
                                                    "size": 10.5
                                                },
                                                "text": "4654564654645616553223"
                                            }
                                        ]
                                    },
                                    "type": "para"
                                }
                            ],
                            "children": None,
                            "outline_level": 10
                        },
                        {
                            "blocks": [
                                {
                                    "bounding_box": {
                                        "x1": 2503,
                                        "x2": 10332,
                                        "y1": 6523,
                                        "y2": 7722
                                    },
                                    "id": "1AB11891vTf9LP92n180",
                                    "index": 13,
                                    "page_index": 0,
                                    "para": {
                                        "prop": {
                                            "alignment": "left",
                                            "def_run_prop": {
                                                "color": "#000000ff",
                                                "font_ascii": "宋体",
                                                "font_east_asia": "宋体",
                                                "size": 10.5
                                            },
                                            "outline_level": 10
                                        },
                                        "runs": [
                                            {
                                                "id": "gtsxilbwdUzTgGB1z180",
                                                "prop": {
                                                    "color": "#000000ff",
                                                    "font_ascii": "宋体",
                                                    "font_east_asia": "宋体",
                                                    "size": 10.5
                                                },
                                                "text": "甲方有雇用母婴护理员（简称月嫂）方面的需求，乙方愿意为甲方提供月嫂服务，并承诺为甲方提供专业、优质、安全的月嫂服务。为了明确双方权利和义务，经平等协商，特订立如下合同，双方共同遵守："
                                            }
                                        ]
                                    },
                                    "type": "para"
                                }
                            ],
                            "children": None,
                            "outline_level": 10
                        },
                        {
                            "blocks": [
                                {
                                    "bounding_box": {
                                        "x1": 2503,
                                        "x2": 10332,
                                        "y1": 7723,
                                        "y2": 8122
                                    },
                                    "id": "5C86AD59zYUi1No1X272",
                                    "index": 14,
                                    "page_index": 0,
                                    "para": {
                                        "prop": {
                                            "alignment": "left",
                                            "def_run_prop": {
                                                "color": "#000000ff",
                                                "font_ascii": "宋体",
                                                "font_east_asia": "宋体",
                                                "size": 10.5
                                            },
                                            "outline_level": 10
                                        },
                                        "runs": None
                                    },
                                    "type": "para"
                                }
                            ],
                            "children": None,
                            "outline_level": 10
                        },
                        {
                            "blocks": [
                                {
                                    "bounding_box": {
                                        "x1": 2503,
                                        "x2": 10332,
                                        "y1": 8123,
                                        "y2": 8522
                                    },
                                    "id": "2C55EAFCi7cW3ub9e273",
                                    "index": 15,
                                    "page_index": 0,
                                    "para": {
                                        "prop": {
                                            "alignment": "left",
                                            "def_run_prop": {
                                                "color": "#000000ff",
                                                "font_ascii": "宋体",
                                                "font_east_asia": "宋体",
                                                "size": 10.5
                                            },
                                            "list_string": "第一条 ",
                                            "outline_level": 1
                                        },
                                        "runs": [
                                            {
                                                "id": "DSImlaYf4pmrz9TEp273",
                                                "prop": {
                                                    "color": "#000000ff",
                                                    "font_ascii": "宋体",
                                                    "font_east_asia": "宋体",
                                                    "size": 10.5
                                                },
                                                "text": "甲乙双方约定事项"
                                            }
                                        ]
                                    },
                                    "type": "para"
                                }
                            ],
                            "children": [
                                {
                                    "blocks": [
                                        {
                                            "bounding_box": {
                                                "x1": 2503,
                                                "x2": 10332,
                                                "y1": 8523,
                                                "y2": 8922
                                            },
                                            "id": "2F36B92ByTKd8bs5O282",
                                            "index": 16,
                                            "page_index": 0,
                                            "para": {
                                                "prop": {
                                                    "alignment": "left",
                                                    "def_run_prop": {
                                                        "color": "#000000ff",
                                                        "font_ascii": "宋体",
                                                        "font_east_asia": "宋体",
                                                        "size": 10.5
                                                    },
                                                    "list_string": "一、",
                                                    "outline_level": 2
                                                },
                                                "runs": [
                                                    {
                                                        "id": "VGJa8lZ78KGWjhbvd282",
                                                        "prop": {
                                                            "color": "#000000ff",
                                                            "font_ascii": "宋体",
                                                            "font_east_asia": "宋体",
                                                            "size": 10.5
                                                        },
                                                        "text": "服务期限"
                                                    }
                                                ]
                                            },
                                            "type": "para"
                                        }
                                    ],
                                    "children": [
                                        {
                                            "blocks": [
                                                {
                                                    "bounding_box": {
                                                        "x1": 2503,
                                                        "x2": 10332,
                                                        "y1": 8923,
                                                        "y2": 9322
                                                    },
                                                    "id": "53C7ADA6DkboOd33i287",
                                                    "index": 17,
                                                    "page_index": 0,
                                                    "para": {
                                                        "prop": {
                                                            "alignment": "left",
                                                            "def_run_prop": {
                                                                "color": "#000000ff",
                                                                "font_ascii": "宋体",
                                                                "font_east_asia": "宋体",
                                                                "size": 10.5
                                                            },
                                                            "outline_level": 10
                                                        },
                                                        "runs": [
                                                            {
                                                                "id": "ohg7C5MFrm7Fe5k1e287",
                                                                "prop": {
                                                                    "color": "#000000ff",
                                                                    "font_ascii": "宋体",
                                                                    "font_east_asia": "宋体",
                                                                    "size": 10.5
                                                                },
                                                                "text": "（1）"
                                                            },
                                                            {
                                                                "id": "wRntpZUH2Lzc5mkwN290",
                                                                "prop": {
                                                                    "color": "#000000ff",
                                                                    "font_ascii": "宋体",
                                                                    "font_east_asia": "宋体",
                                                                    "size": 10.5
                                                                },
                                                                "text": "2020"
                                                            },
                                                            {
                                                                "id": "6NQCAOHIxf1kGxNSa294",
                                                                "prop": {
                                                                    "color": "#000000ff",
                                                                    "font_ascii": "宋体",
                                                                    "font_east_asia": "宋体",
                                                                    "size": 10.5
                                                                },
                                                                "text": "年"
                                                            },
                                                            {
                                                                "id": "H0e01lUx1pufP6APc295",
                                                                "prop": {
                                                                    "color": "#000000ff",
                                                                    "font_ascii": "宋体",
                                                                    "font_east_asia": "宋体",
                                                                    "size": 10.5
                                                                },
                                                                "text": "1"
                                                            },
                                                            {
                                                                "id": "AkEsaqChYhIZZk7jv296",
                                                                "prop": {
                                                                    "color": "#000000ff",
                                                                    "font_ascii": "宋体",
                                                                    "font_east_asia": "宋体",
                                                                    "size": 10.5
                                                                },
                                                                "text": "月"
                                                            },
                                                            {
                                                                "id": "YjKcYKSKu6wySJ1rw297",
                                                                "prop": {
                                                                    "color": "#000000ff",
                                                                    "font_ascii": "宋体",
                                                                    "font_east_asia": "宋体",
                                                                    "size": 10.5
                                                                },
                                                                "text": "1"
                                                            },
                                                            {
                                                                "id": "N9aZmvc56b0YiiUNk298",
                                                                "prop": {
                                                                    "color": "#000000ff",
                                                                    "font_ascii": "宋体",
                                                                    "font_east_asia": "宋体",
                                                                    "size": 10.5
                                                                },
                                                                "text": "日起至"
                                                            },
                                                            {
                                                                "id": "DgXVUE1TdiH0FlCAm301",
                                                                "prop": {
                                                                    "color": "#000000ff",
                                                                    "font_ascii": "宋体",
                                                                    "font_east_asia": "宋体",
                                                                    "size": 10.5
                                                                },
                                                                "text": "2021"
                                                            },
                                                            {
                                                                "id": "6PABRsdEUEmMQf9Zk305",
                                                                "prop": {
                                                                    "color": "#000000ff",
                                                                    "font_ascii": "宋体",
                                                                    "font_east_asia": "宋体",
                                                                    "size": 10.5
                                                                },
                                                                "text": "年"
                                                            },
                                                            {
                                                                "id": "YTXlb5XRCjLwbvfzM306",
                                                                "prop": {
                                                                    "color": "#000000ff",
                                                                    "font_ascii": "宋体",
                                                                    "font_east_asia": "宋体",
                                                                    "size": 10.5
                                                                },
                                                                "text": "1"
                                                            },
                                                            {
                                                                "id": "jAovpVZEtRs8yufSE307",
                                                                "prop": {
                                                                    "color": "#000000ff",
                                                                    "font_ascii": "宋体",
                                                                    "font_east_asia": "宋体",
                                                                    "size": 10.5
                                                                },
                                                                "text": "月"
                                                            },
                                                            {
                                                                "id": "rEY2WCrWvIUudGdRq308",
                                                                "prop": {
                                                                    "color": "#000000ff",
                                                                    "font_ascii": "宋体",
                                                                    "font_east_asia": "宋体",
                                                                    "size": 10.5
                                                                },
                                                                "text": "1"
                                                            },
                                                            {
                                                                "id": "0cCrQWpMJGJMngaxB309",
                                                                "prop": {
                                                                    "color": "#000000ff",
                                                                    "font_ascii": "宋体",
                                                                    "font_east_asia": "宋体",
                                                                    "size": 10.5
                                                                },
                                                                "text": "日止，共计"
                                                            },
                                                            {
                                                                "id": "GkzmdRwk0SJkzX7Hf314",
                                                                "prop": {
                                                                    "color": "#000000ff",
                                                                    "font_ascii": "宋体",
                                                                    "font_east_asia": "宋体",
                                                                    "size": 10.5
                                                                },
                                                                "text": "365"
                                                            },
                                                            {
                                                                "id": "2eavSuo4PUiPBYurL317",
                                                                "prop": {
                                                                    "color": "#000000ff",
                                                                    "font_ascii": "宋体",
                                                                    "font_east_asia": "宋体",
                                                                    "size": 10.5
                                                                },
                                                                "text": "天。"
                                                            }
                                                        ]
                                                    },
                                                    "type": "para"
                                                }
                                            ],
                                            "children": None,
                                            "outline_level": 10
                                        },
                                        {
                                            "blocks": [
                                                {
                                                    "bounding_box": {
                                                        "x1": 2503,
                                                        "x2": 10332,
                                                        "y1": 9323,
                                                        "y2": 10122
                                                    },
                                                    "id": "59D559D0Mvwzfl46r320",
                                                    "index": 18,
                                                    "page_index": 0,
                                                    "para": {
                                                        "prop": {
                                                            "alignment": "left",
                                                            "def_run_prop": {
                                                                "color": "#000000ff",
                                                                "font_ascii": "宋体",
                                                                "font_east_asia": "宋体",
                                                                "size": 10.5
                                                            },
                                                            "outline_level": 10
                                                        },
                                                        "runs": [
                                                            {
                                                                "id": "FCBWRMDNqzBfGqDyr320",
                                                                "prop": {
                                                                    "color": "#000000ff",
                                                                    "font_ascii": "宋体",
                                                                    "font_east_asia": "宋体",
                                                                    "size": 10.5
                                                                },
                                                                "text": "（2）由于乙方工作性质特殊，需24小时对母婴进行护理，为乙方健康，甲方应安排乙方休息"
                                                            },
                                                            {
                                                                "id": "nKyr8slGHAsrm2P5A362",
                                                                "prop": {
                                                                    "color": "#000000ff",
                                                                    "font_ascii": "宋体",
                                                                    "font_east_asia": "宋体",
                                                                    "size": 10.5
                                                                },
                                                                "text": "1"
                                                            },
                                                            {
                                                                "id": "iXYEULAIf2k2m9Ccq363",
                                                                "prop": {
                                                                    "color": "#000000ff",
                                                                    "font_ascii": "宋体",
                                                                    "font_east_asia": "宋体",
                                                                    "size": 10.5
                                                                },
                                                                "text": "天。"
                                                            }
                                                        ]
                                                    },
                                                    "type": "para"
                                                }
                                            ],
                                            "children": None,
                                            "outline_level": 10
                                        }
                                    ],
                                    "outline_level": 2
                                },
                                {
                                    "blocks": [
                                        {
                                            "bounding_box": {
                                                "x1": 2503,
                                                "x2": 10332,
                                                "y1": 10123,
                                                "y2": 10522
                                            },
                                            "id": "0FF05CC76hiPFwk5W366",
                                            "index": 19,
                                            "page_index": 0,
                                            "para": {
                                                "prop": {
                                                    "alignment": "left",
                                                    "def_run_prop": {
                                                        "color": "#000000ff",
                                                        "font_ascii": "宋体",
                                                        "font_east_asia": "宋体",
                                                        "size": 10.5
                                                    },
                                                    "list_string": "二、",
                                                    "outline_level": 2
                                                },
                                                "runs": [
                                                    {
                                                        "id": "m2TXlBTnMiQvpePSQ366",
                                                        "prop": {
                                                            "color": "#000000ff",
                                                            "font_ascii": "宋体",
                                                            "font_east_asia": "宋体",
                                                            "size": 10.5
                                                        },
                                                        "text": "服务费用"
                                                    }
                                                ]
                                            },
                                            "type": "para"
                                        }
                                    ],
                                    "children": [
                                        {
                                            "blocks": [
                                                {
                                                    "bounding_box": {
                                                        "x1": 2503,
                                                        "x2": 10332,
                                                        "y1": 10523,
                                                        "y2": 11322
                                                    },
                                                    "id": "1D39D5E9NNAGJX47g371",
                                                    "index": 20,
                                                    "page_index": 0,
                                                    "para": {
                                                        "prop": {
                                                            "alignment": "left",
                                                            "def_run_prop": {
                                                                "color": "#000000ff",
                                                                "font_ascii": "宋体",
                                                                "font_east_asia": "宋体",
                                                                "size": 10.5
                                                            },
                                                            "outline_level": 10
                                                        },
                                                        "runs": [
                                                            {
                                                                "id": "n3kRtbfKDBpST3E9k371",
                                                                "prop": {
                                                                    "color": "#000000ff",
                                                                    "font_ascii": "宋体",
                                                                    "font_east_asia": "宋体",
                                                                    "size": 10.5
                                                                },
                                                                "text": "甲方向乙方支付服务费，"
                                                            },
                                                            {
                                                                "id": "S0bFL1y4xhb31uk2P382",
                                                                "prop": {
                                                                    "color": "#000000ff",
                                                                    "font_ascii": "宋体",
                                                                    "font_east_asia": "宋体",
                                                                    "size": 10.5
                                                                },
                                                                "text": "共计50，000元，"
                                                            },
                                                            {
                                                                "id": "hdwQADDnodpmzHPtg392",
                                                                "prop": {
                                                                    "color": "#000000ff",
                                                                    "font_ascii": "宋体",
                                                                    "font_east_asia": "宋体",
                                                                    "size": 10.5
                                                                },
                                                                "text": "支付的时间为服务期终止日："
                                                            },
                                                            {
                                                                "id": "gDCPm0cJshcwvPZSS405",
                                                                "prop": {
                                                                    "color": "#000000ff",
                                                                    "font_ascii": "宋体",
                                                                    "font_east_asia": "宋体",
                                                                    "size": 10.5
                                                                },
                                                                "text": "2021"
                                                            },
                                                            {
                                                                "id": "iG5hClKzRZKX8jd9X409",
                                                                "prop": {
                                                                    "color": "#000000ff",
                                                                    "font_ascii": "宋体",
                                                                    "font_east_asia": "宋体",
                                                                    "size": 10.5
                                                                },
                                                                "text": " 年"
                                                            },
                                                            {
                                                                "id": "Qy8dtLX1TLa81FAgR411",
                                                                "prop": {
                                                                    "color": "#000000ff",
                                                                    "font_ascii": "宋体",
                                                                    "font_east_asia": "宋体",
                                                                    "size": 10.5
                                                                },
                                                                "text": "1"
                                                            },
                                                            {
                                                                "id": "8SmTF6jPMAQS8TfRi412",
                                                                "prop": {
                                                                    "color": "#000000ff",
                                                                    "font_ascii": "宋体",
                                                                    "font_east_asia": "宋体",
                                                                    "size": 10.5
                                                                },
                                                                "text": "月"
                                                            },
                                                            {
                                                                "id": "jfaq0KtjeIphFW3uc413",
                                                                "prop": {
                                                                    "color": "#000000ff",
                                                                    "font_ascii": "宋体",
                                                                    "font_east_asia": "宋体",
                                                                    "size": 10.5
                                                                },
                                                                "text": "1"
                                                            },
                                                            {
                                                                "id": "3mdDDaUJGx6ItUYaF414",
                                                                "prop": {
                                                                    "color": "#000000ff",
                                                                    "font_ascii": "宋体",
                                                                    "font_east_asia": "宋体",
                                                                    "size": 10.5
                                                                },
                                                                "text": "日。 "
                                                            }
                                                        ]
                                                    },
                                                    "type": "para"
                                                }
                                            ],
                                            "children": None,
                                            "outline_level": 10
                                        }
                                    ],
                                    "outline_level": 2
                                },
                                {
                                    "blocks": [
                                        {
                                            "bounding_box": {
                                                "x1": 2503,
                                                "x2": 10332,
                                                "y1": 11323,
                                                "y2": 11722
                                            },
                                            "id": "40BCD1D31moXzjr5m418",
                                            "index": 21,
                                            "page_index": 0,
                                            "para": {
                                                "prop": {
                                                    "alignment": "left",
                                                    "def_run_prop": {
                                                        "color": "#000000ff",
                                                        "font_ascii": "宋体",
                                                        "font_east_asia": "宋体",
                                                        "size": 10.5
                                                    },
                                                    "list_string": "三、",
                                                    "outline_level": 2
                                                },
                                                "runs": [
                                                    {
                                                        "id": "0LoMIMhqsuiBtlcbf418",
                                                        "prop": {
                                                            "color": "#000000ff",
                                                            "font_ascii": "宋体",
                                                            "font_east_asia": "宋体",
                                                            "size": 10.5
                                                        },
                                                        "text": "服务内容"
                                                    }
                                                ]
                                            },
                                            "type": "para"
                                        }
                                    ],
                                    "children": [
                                        {
                                            "blocks": [
                                                {
                                                    "bounding_box": {
                                                        "x1": 2503,
                                                        "x2": 10332,
                                                        "y1": 11723,
                                                        "y2": 12122
                                                    },
                                                    "id": "5DC1E163OhNPEkT3L423",
                                                    "index": 22,
                                                    "page_index": 0,
                                                    "para": {
                                                        "prop": {
                                                            "alignment": "left",
                                                            "def_run_prop": {
                                                                "color": "#000000ff",
                                                                "font_ascii": "宋体",
                                                                "font_east_asia": "宋体",
                                                                "size": 10.5
                                                            },
                                                            "list_string": "1.",
                                                            "outline_level": 3
                                                        },
                                                        "runs": [
                                                            {
                                                                "id": "4HJnVR2HBJ5myiMsc423",
                                                                "prop": {
                                                                    "color": "#000000ff",
                                                                    "font_ascii": "宋体",
                                                                    "font_east_asia": "宋体",
                                                                    "size": 10.5
                                                                },
                                                                "text": "产妇"
                                                            }
                                                        ]
                                                    },
                                                    "type": "para"
                                                }
                                            ],
                                            "children": [
                                                {
                                                    "blocks": [
                                                        {
                                                            "bounding_box": {
                                                                "x1": 2503,
                                                                "x2": 10332,
                                                                "y1": 12123,
                                                                "y2": 12522
                                                            },
                                                            "id": "28D880880lOH2q30c426",
                                                            "index": 23,
                                                            "page_index": 0,
                                                            "para": {
                                                                "prop": {
                                                                    "alignment": "left",
                                                                    "def_run_prop": {
                                                                        "color": "#000000ff",
                                                                        "font_ascii": "宋体",
                                                                        "font_east_asia": "宋体",
                                                                        "size": 10.5
                                                                    },
                                                                    "list_string": "A.",
                                                                    "outline_level": 10
                                                                },
                                                                "runs": [
                                                                    {
                                                                        "id": "ZH8dC6yDSZK40fCVW426",
                                                                        "prop": {
                                                                            "color": "#000000ff",
                                                                            "font_ascii": "宋体",
                                                                            "font_east_asia": "宋体",
                                                                            "size": 10.5
                                                                        },
                                                                        "text": "科学、合理安排产妇膳食，平衡营养，促进产后康复及乳汁分泌；"
                                                                    }
                                                                ]
                                                            },
                                                            "type": "para"
                                                        }
                                                    ],
                                                    "children": None,
                                                    "outline_level": 10
                                                },
                                                {
                                                    "blocks": [
                                                        {
                                                            "bounding_box": {
                                                                "x1": 2503,
                                                                "x2": 10332,
                                                                "y1": 12523,
                                                                "y2": 12922
                                                            },
                                                            "id": "15D69D94EuBcza17l456",
                                                            "index": 24,
                                                            "page_index": 0,
                                                            "para": {
                                                                "prop": {
                                                                    "alignment": "left",
                                                                    "def_run_prop": {
                                                                        "color": "#000000ff",
                                                                        "font_ascii": "宋体",
                                                                        "font_east_asia": "宋体",
                                                                        "size": 10.5
                                                                    },
                                                                    "list_string": "B.",
                                                                    "outline_level": 10
                                                                },
                                                                "runs": [
                                                                    {
                                                                        "id": "r0q68TOVMMHFFNdRV456",
                                                                        "prop": {
                                                                            "color": "#000000ff",
                                                                            "font_ascii": "宋体",
                                                                            "font_east_asia": "宋体",
                                                                            "size": 10.5
                                                                        },
                                                                        "text": "指导产妇实行母乳喂养、按需哺喂；"
                                                                    }
                                                                ]
                                                            },
                                                            "type": "para"
                                                        }
                                                    ],
                                                    "children": None,
                                                    "outline_level": 10
                                                },
                                                {
                                                    "blocks": [
                                                        {
                                                            "bounding_box": {
                                                                "x1": 2503,
                                                                "x2": 10332,
                                                                "y1": 12923,
                                                                "y2": 13322
                                                            },
                                                            "id": "61549AC60PYy0D34A473",
                                                            "index": 25,
                                                            "page_index": 0,
                                                            "para": {
                                                                "prop": {
                                                                    "alignment": "left",
                                                                    "def_run_prop": {
                                                                        "color": "#000000ff",
                                                                        "font_ascii": "宋体",
                                                                        "font_east_asia": "宋体",
                                                                        "size": 10.5
                                                                    },
                                                                    "list_string": "C.",
                                                                    "outline_level": 10
                                                                },
                                                                "runs": [
                                                                    {
                                                                        "id": "u0RiomsfcVaPQa0rI473",
                                                                        "prop": {
                                                                            "color": "#000000ff",
                                                                            "font_ascii": "宋体",
                                                                            "font_east_asia": "宋体",
                                                                            "size": 10.5
                                                                        },
                                                                        "text": "指导产妇进行乳房按摩与护理，防止乳房下垂、松驰；指导产妇产后运动；"
                                                                    }
                                                                ]
                                                            },
                                                            "type": "para"
                                                        }
                                                    ],
                                                    "children": None,
                                                    "outline_level": 10
                                                },
                                                {
                                                    "blocks": [
                                                        {
                                                            "bounding_box": {
                                                                "x1": 2503,
                                                                "x2": 10332,
                                                                "y1": 13323,
                                                                "y2": 13722
                                                            },
                                                            "id": "2EA67359RYtVoc33Y507",
                                                            "index": 26,
                                                            "page_index": 0,
                                                            "para": {
                                                                "prop": {
                                                                    "alignment": "left",
                                                                    "def_run_prop": {
                                                                        "color": "#000000ff",
                                                                        "font_ascii": "宋体",
                                                                        "font_east_asia": "宋体",
                                                                        "size": 10.5
                                                                    },
                                                                    "list_string": "D.",
                                                                    "outline_level": 10
                                                                },
                                                                "runs": [
                                                                    {
                                                                        "id": "lhbJAypKzxGrvGrww507",
                                                                        "prop": {
                                                                            "color": "#000000ff",
                                                                            "font_ascii": "宋体",
                                                                            "font_east_asia": "宋体",
                                                                            "size": 10.5
                                                                        },
                                                                        "text": "协助产妇进行个人卫生清洁，做好会阴冲洗及伤口护理，防止细菌感染；"
                                                                    }
                                                                ]
                                                            },
                                                            "type": "para"
                                                        }
                                                    ],
                                                    "children": None,
                                                    "outline_level": 10
                                                },
                                                {
                                                    "blocks": [
                                                        {
                                                            "bounding_box": {
                                                                "x1": 2503,
                                                                "x2": 10332,
                                                                "y1": 13723,
                                                                "y2": 14122
                                                            },
                                                            "id": "19F86F7EvJ9KGC30n540",
                                                            "index": 27,
                                                            "page_index": 0,
                                                            "para": {
                                                                "prop": {
                                                                    "alignment": "left",
                                                                    "def_run_prop": {
                                                                        "color": "#000000ff",
                                                                        "font_ascii": "宋体",
                                                                        "font_east_asia": "宋体",
                                                                        "size": 10.5
                                                                    },
                                                                    "list_string": "E.",
                                                                    "outline_level": 10
                                                                },
                                                                "runs": [
                                                                    {
                                                                        "id": "N8llHeApfJgRGOnxy540",
                                                                        "prop": {
                                                                            "color": "#000000ff",
                                                                            "font_ascii": "宋体",
                                                                            "font_east_asia": "宋体",
                                                                            "size": 10.5
                                                                        },
                                                                        "text": "舒解产妇产后焦虑、烦躁等情绪，减轻产妇操劳，尽快恢复健康；"
                                                                    }
                                                                ]
                                                            },
                                                            "type": "para"
                                                        }
                                                    ],
                                                    "children": None,
                                                    "outline_level": 10
                                                },
                                                {
                                                    "blocks": [
                                                        {
                                                            "bounding_box": {
                                                                "x1": 2503,
                                                                "x2": 10332,
                                                                "y1": 14123,
                                                                "y2": 14522
                                                            },
                                                            "id": "0BD904E19u0o2R17S570",
                                                            "index": 28,
                                                            "page_index": 0,
                                                            "para": {
                                                                "prop": {
                                                                    "alignment": "left",
                                                                    "def_run_prop": {
                                                                        "color": "#000000ff",
                                                                        "font_ascii": "宋体",
                                                                        "font_east_asia": "宋体",
                                                                        "size": 10.5
                                                                    },
                                                                    "list_string": "F.",
                                                                    "outline_level": 10
                                                                },
                                                                "runs": [
                                                                    {
                                                                        "id": "IthuG7xw4tLyQfRCd570",
                                                                        "prop": {
                                                                            "color": "#000000ff",
                                                                            "font_ascii": "宋体",
                                                                            "font_east_asia": "宋体",
                                                                            "size": 10.5
                                                                        },
                                                                        "text": "随时对产妇的身体状况观察、记录。"
                                                                    }
                                                                ]
                                                            },
                                                            "type": "para"
                                                        }
                                                    ],
                                                    "children": None,
                                                    "outline_level": 10
                                                }
                                            ],
                                            "outline_level": 3
                                        },
                                        {
                                            "blocks": [
                                                {
                                                    "bounding_box": {
                                                        "x1": 2503,
                                                        "x2": 10332,
                                                        "y1": 14523,
                                                        "y2": 14922
                                                    },
                                                    "id": "218DCCFBzuoCYmU3J587",
                                                    "index": 29,
                                                    "page_index": 0,
                                                    "para": {
                                                        "prop": {
                                                            "alignment": "left",
                                                            "def_run_prop": {
                                                                "color": "#000000ff",
                                                                "font_ascii": "宋体",
                                                                "font_east_asia": "宋体",
                                                                "size": 10.5
                                                            },
                                                            "list_string": "2.",
                                                            "outline_level": 3
                                                        },
                                                        "runs": [
                                                            {
                                                                "id": "8jjrMDI3oypMyV2Yd587",
                                                                "prop": {
                                                                    "color": "#000000ff",
                                                                    "font_ascii": "宋体",
                                                                    "font_east_asia": "宋体",
                                                                    "size": 10.5
                                                                },
                                                                "text": "婴儿"
                                                            }
                                                        ]
                                                    },
                                                    "type": "para"
                                                }
                                            ],
                                            "children": [
                                                {
                                                    "blocks": [
                                                        {
                                                            "bounding_box": {
                                                                "x1": 2503,
                                                                "x2": 10332,
                                                                "y1": 14923,
                                                                "y2": 15322
                                                            },
                                                            "id": "79023D1BKGPCRT25e590",
                                                            "index": 30,
                                                            "page_index": 0,
                                                            "para": {
                                                                "prop": {
                                                                    "alignment": "left",
                                                                    "def_run_prop": {
                                                                        "color": "#000000ff",
                                                                        "font_ascii": "宋体",
                                                                        "font_east_asia": "宋体",
                                                                        "size": 10.5
                                                                    },
                                                                    "list_string": "A.",
                                                                    "outline_level": 10
                                                                },
                                                                "runs": [
                                                                    {
                                                                        "id": "BlabLj0yLEGWDsv4q590",
                                                                        "prop": {
                                                                            "color": "#000000ff",
                                                                            "font_ascii": "宋体",
                                                                            "font_east_asia": "宋体",
                                                                            "size": 10.5
                                                                        },
                                                                        "text": "科学、合理健康喂养婴儿，保证婴儿健康的营养需要；"
                                                                    }
                                                                ]
                                                            },
                                                            "type": "para"
                                                        }
                                                    ],
                                                    "children": None,
                                                    "outline_level": 10
                                                },
                                                {
                                                    "blocks": [
                                                        {
                                                            "bounding_box": {
                                                                "x1": 2503,
                                                                "x2": 10332,
                                                                "y1": 18789,
                                                                "y2": 19188
                                                            },
                                                            "id": "60B1ED34tfMlIN14O615",
                                                            "index": 31,
                                                            "page_index": 1,
                                                            "para": {
                                                                "prop": {
                                                                    "alignment": "left",
                                                                    "def_run_prop": {
                                                                        "color": "#000000ff",
                                                                        "font_ascii": "宋体",
                                                                        "font_east_asia": "宋体",
                                                                        "size": 10.5
                                                                    },
                                                                    "list_string": "B.",
                                                                    "outline_level": 10
                                                                },
                                                                "runs": [
                                                                    {
                                                                        "id": "TOqFeIpeExO2vAiKt615",
                                                                        "prop": {
                                                                            "color": "#000000ff",
                                                                            "font_ascii": "宋体",
                                                                            "font_east_asia": "宋体",
                                                                            "size": 10.5
                                                                        },
                                                                        "text": "安抚哭闹婴儿，呵护入眠等；"
                                                                    }
                                                                ]
                                                            },
                                                            "type": "para"
                                                        }
                                                    ],
                                                    "children": None,
                                                    "outline_level": 10
                                                },
                                                {
                                                    "blocks": [
                                                        {
                                                            "bounding_box": {
                                                                "x1": 2503,
                                                                "x2": 10332,
                                                                "y1": 19189,
                                                                "y2": 19988
                                                            },
                                                            "id": "08AC7C4ErTSw5n47H629",
                                                            "index": 32,
                                                            "page_index": 1,
                                                            "para": {
                                                                "prop": {
                                                                    "alignment": "left",
                                                                    "def_run_prop": {
                                                                        "color": "#000000ff",
                                                                        "font_ascii": "宋体",
                                                                        "font_east_asia": "宋体",
                                                                        "size": 10.5
                                                                    },
                                                                    "list_string": "C.",
                                                                    "outline_level": 10
                                                                },
                                                                "runs": [
                                                                    {
                                                                        "id": "dXCnf4b53Lnk3j6Rj629",
                                                                        "prop": {
                                                                            "color": "#000000ff",
                                                                            "font_ascii": "宋体",
                                                                            "font_east_asia": "宋体",
                                                                            "size": 10.5
                                                                        },
                                                                        "text": "为婴儿洗澡、按摩、抚触，增加与婴儿的情感交流，促进婴儿健康发育，及时为婴儿更换衣物、尿布等；"
                                                                    }
                                                                ]
                                                            },
                                                            "type": "para"
                                                        }
                                                    ],
                                                    "children": None,
                                                    "outline_level": 10
                                                },
                                                {
                                                    "blocks": [
                                                        {
                                                            "bounding_box": {
                                                                "x1": 2503,
                                                                "x2": 10332,
                                                                "y1": 19989,
                                                                "y2": 20388
                                                            },
                                                            "id": "7D621CB7dAbowL23T676",
                                                            "index": 33,
                                                            "page_index": 1,
                                                            "para": {
                                                                "prop": {
                                                                    "alignment": "left",
                                                                    "def_run_prop": {
                                                                        "color": "#000000ff",
                                                                        "font_ascii": "宋体",
                                                                        "font_east_asia": "宋体",
                                                                        "size": 10.5
                                                                    },
                                                                    "list_string": "D.",
                                                                    "outline_level": 10
                                                                },
                                                                "runs": [
                                                                    {
                                                                        "id": "XJaqnMQHcu9k0GNGj676",
                                                                        "prop": {
                                                                            "color": "#000000ff",
                                                                            "font_ascii": "宋体",
                                                                            "font_east_asia": "宋体",
                                                                            "size": 10.5
                                                                        },
                                                                        "text": "做好脐部护理，臀部护理，保持干爽，防止感染；"
                                                                    }
                                                                ]
                                                            },
                                                            "type": "para"
                                                        }
                                                    ],
                                                    "children": None,
                                                    "outline_level": 10
                                                },
                                                {
                                                    "blocks": [
                                                        {
                                                            "bounding_box": {
                                                                "x1": 2503,
                                                                "x2": 10332,
                                                                "y1": 20389,
                                                                "y2": 20788
                                                            },
                                                            "id": "48A8F6483b5Tse32D699",
                                                            "index": 34,
                                                            "page_index": 1,
                                                            "para": {
                                                                "prop": {
                                                                    "alignment": "left",
                                                                    "def_run_prop": {
                                                                        "color": "#000000ff",
                                                                        "font_ascii": "宋体",
                                                                        "font_east_asia": "宋体",
                                                                        "size": 10.5
                                                                    },
                                                                    "list_string": "E.",
                                                                    "outline_level": 10
                                                                },
                                                                "runs": [
                                                                    {
                                                                        "id": "atBsZQ6LJUeUH6cYY699",
                                                                        "prop": {
                                                                            "color": "#000000ff",
                                                                            "font_ascii": "宋体",
                                                                            "font_east_asia": "宋体",
                                                                            "size": 10.5
                                                                        },
                                                                        "text": "随时对婴儿的身体状况（如食欲、食量、体温、大小便等）观察记录；"
                                                                    }
                                                                ]
                                                            },
                                                            "type": "para"
                                                        }
                                                    ],
                                                    "children": None,
                                                    "outline_level": 10
                                                },
                                                {
                                                    "blocks": [
                                                        {
                                                            "bounding_box": {
                                                                "x1": 2503,
                                                                "x2": 10332,
                                                                "y1": 20789,
                                                                "y2": 21188
                                                            },
                                                            "id": "4DEA09B8J3lOYJ13X731",
                                                            "index": 35,
                                                            "page_index": 1,
                                                            "para": {
                                                                "prop": {
                                                                    "alignment": "left",
                                                                    "def_run_prop": {
                                                                        "color": "#000000ff",
                                                                        "font_ascii": "宋体",
                                                                        "font_east_asia": "宋体",
                                                                        "size": 10.5
                                                                    },
                                                                    "list_string": "F.",
                                                                    "outline_level": 10
                                                                },
                                                                "runs": [
                                                                    {
                                                                        "id": "9uCaZGwf1HEbyggrC731",
                                                                        "prop": {
                                                                            "color": "#000000ff",
                                                                            "font_ascii": "宋体",
                                                                            "font_east_asia": "宋体",
                                                                            "size": 10.5
                                                                        },
                                                                        "text": "培养婴儿良好的生活习惯。"
                                                                    }
                                                                ]
                                                            },
                                                            "type": "para"
                                                        }
                                                    ],
                                                    "children": None,
                                                    "outline_level": 10
                                                }
                                            ],
                                            "outline_level": 3
                                        },
                                        {
                                            "blocks": [
                                                {
                                                    "bounding_box": {
                                                        "x1": 2503,
                                                        "x2": 10332,
                                                        "y1": 21189,
                                                        "y2": 21588
                                                    },
                                                    "id": "724B4A0671IfZZk3j744",
                                                    "index": 36,
                                                    "page_index": 1,
                                                    "para": {
                                                        "prop": {
                                                            "alignment": "left",
                                                            "def_run_prop": {
                                                                "color": "#000000ff",
                                                                "font_ascii": "宋体",
                                                                "font_east_asia": "宋体",
                                                                "size": 10.5
                                                            },
                                                            "list_string": "3.",
                                                            "outline_level": 3
                                                        },
                                                        "runs": [
                                                            {
                                                                "id": "5S3wLreAjffwVHO5A744",
                                                                "prop": {
                                                                    "color": "#000000ff",
                                                                    "font_ascii": "宋体",
                                                                    "font_east_asia": "宋体",
                                                                    "size": 10.5
                                                                },
                                                                "text": "其他"
                                                            }
                                                        ]
                                                    },
                                                    "type": "para"
                                                }
                                            ],
                                            "children": [
                                                {
                                                    "blocks": [
                                                        {
                                                            "bounding_box": {
                                                                "x1": 2503,
                                                                "x2": 10332,
                                                                "y1": 21589,
                                                                "y2": 21988
                                                            },
                                                            "id": "1071D4EBtHcz6w20h747",
                                                            "index": 37,
                                                            "page_index": 1,
                                                            "para": {
                                                                "prop": {
                                                                    "alignment": "left",
                                                                    "def_run_prop": {
                                                                        "color": "#000000ff",
                                                                        "font_ascii": "宋体",
                                                                        "font_east_asia": "宋体",
                                                                        "size": 10.5
                                                                    },
                                                                    "outline_level": 10
                                                                },
                                                                "runs": [
                                                                    {
                                                                        "id": "uBl9PzFBYRSARE6wM747",
                                                                        "prop": {
                                                                            "color": "#000000ff",
                                                                            "font_ascii": "宋体",
                                                                            "font_east_asia": "宋体",
                                                                            "size": 10.5
                                                                        },
                                                                        "text": "洗衣、买菜、煮饭、打扫卫生等日常家务。"
                                                                    }
                                                                ]
                                                            },
                                                            "type": "para"
                                                        }
                                                    ],
                                                    "children": None,
                                                    "outline_level": 10
                                                }
                                            ],
                                            "outline_level": 3
                                        }
                                    ],
                                    "outline_level": 2
                                }
                            ],
                            "outline_level": 1
                        },
                        {
                            "blocks": [
                                {
                                    "bounding_box": {
                                        "x1": 2503,
                                        "x2": 10332,
                                        "y1": 21989,
                                        "y2": 22388
                                    },
                                    "id": "2960D7014zZHGX11l767",
                                    "index": 38,
                                    "page_index": 1,
                                    "para": {
                                        "prop": {
                                            "alignment": "left",
                                            "def_run_prop": {
                                                "color": "#000000ff",
                                                "font_ascii": "宋体",
                                                "font_east_asia": "宋体",
                                                "size": 10.5
                                            },
                                            "list_string": "第二条 ",
                                            "outline_level": 1
                                        },
                                        "runs": [
                                            {
                                                "id": "v9gyOmj6m844sPJ7x767",
                                                "prop": {
                                                    "color": "#000000ff",
                                                    "font_ascii": "宋体",
                                                    "font_east_asia": "宋体",
                                                    "size": 10.5
                                                },
                                                "text": "甲乙双方的权利和义务"
                                            }
                                        ]
                                    },
                                    "type": "para"
                                }
                            ],
                            "children": [
                                {
                                    "blocks": [
                                        {
                                            "bounding_box": {
                                                "x1": 2503,
                                                "x2": 10332,
                                                "y1": 22389,
                                                "y2": 22788
                                            },
                                            "id": "4F54FE87XXn2xHz6w778",
                                            "index": 39,
                                            "page_index": 1,
                                            "para": {
                                                "prop": {
                                                    "alignment": "left",
                                                    "def_run_prop": {
                                                        "color": "#000000ff",
                                                        "font_ascii": "宋体",
                                                        "font_east_asia": "宋体",
                                                        "size": 10.5
                                                    },
                                                    "list_string": "一、",
                                                    "outline_level": 2
                                                },
                                                "runs": [
                                                    {
                                                        "id": "8TTyb9mmpcXpCAkME778",
                                                        "prop": {
                                                            "color": "#000000ff",
                                                            "font_ascii": "宋体",
                                                            "font_east_asia": "宋体",
                                                            "size": 10.5
                                                        },
                                                        "text": "甲方的权利"
                                                    }
                                                ]
                                            },
                                            "type": "para"
                                        }
                                    ],
                                    "children": [
                                        {
                                            "blocks": [
                                                {
                                                    "bounding_box": {
                                                        "x1": 2503,
                                                        "x2": 10332,
                                                        "y1": 22789,
                                                        "y2": 23188
                                                    },
                                                    "id": "2722CE3AprmVYF23F784",
                                                    "index": 40,
                                                    "page_index": 1,
                                                    "para": {
                                                        "prop": {
                                                            "alignment": "left",
                                                            "def_run_prop": {
                                                                "color": "#000000ff",
                                                                "font_ascii": "宋体",
                                                                "font_east_asia": "宋体",
                                                                "size": 10.5
                                                            },
                                                            "list_string": "(1)",
                                                            "outline_level": 10
                                                        },
                                                        "runs": [
                                                            {
                                                                "id": "w2keFkJv0RQzJiZRT784",
                                                                "prop": {
                                                                    "color": "#000000ff",
                                                                    "font_ascii": "宋体",
                                                                    "font_east_asia": "宋体",
                                                                    "size": 10.5
                                                                },
                                                                "text": "甲方可在服务内容的工作范围内，安排乙方工作；"
                                                            }
                                                        ]
                                                    },
                                                    "type": "para"
                                                }
                                            ],
                                            "children": None,
                                            "outline_level": 10
                                        },
                                        {
                                            "blocks": [
                                                {
                                                    "bounding_box": {
                                                        "x1": 2503,
                                                        "x2": 10332,
                                                        "y1": 23189,
                                                        "y2": 23588
                                                    },
                                                    "id": "1A7F6E16NcLbiX23D807",
                                                    "index": 41,
                                                    "page_index": 1,
                                                    "para": {
                                                        "prop": {
                                                            "alignment": "left",
                                                            "def_run_prop": {
                                                                "color": "#000000ff",
                                                                "font_ascii": "宋体",
                                                                "font_east_asia": "宋体",
                                                                "size": 10.5
                                                            },
                                                            "list_string": "(2)",
                                                            "outline_level": 10
                                                        },
                                                        "runs": [
                                                            {
                                                                "id": "egd7vVIWclhPcZSVW807",
                                                                "prop": {
                                                                    "color": "#000000ff",
                                                                    "font_ascii": "宋体",
                                                                    "font_east_asia": "宋体",
                                                                    "size": 10.5
                                                                },
                                                                "text": "甲方有权要求乙方体检，并提供有效的健康证明。"
                                                            }
                                                        ]
                                                    },
                                                    "type": "para"
                                                }
                                            ],
                                            "children": None,
                                            "outline_level": 10
                                        }
                                    ],
                                    "outline_level": 2
                                },
                                {
                                    "blocks": [
                                        {
                                            "bounding_box": {
                                                "x1": 2503,
                                                "x2": 10332,
                                                "y1": 23589,
                                                "y2": 23988
                                            },
                                            "id": "26C3C255taj9Omu6O830",
                                            "index": 42,
                                            "page_index": 1,
                                            "para": {
                                                "prop": {
                                                    "alignment": "left",
                                                    "def_run_prop": {
                                                        "color": "#000000ff",
                                                        "font_ascii": "宋体",
                                                        "font_east_asia": "宋体",
                                                        "size": 10.5
                                                    },
                                                    "list_string": "二、",
                                                    "outline_level": 2
                                                },
                                                "runs": [
                                                    {
                                                        "id": "adlflyPF1c7mp5pUL830",
                                                        "prop": {
                                                            "color": "#000000ff",
                                                            "font_ascii": "宋体",
                                                            "font_east_asia": "宋体",
                                                            "size": 10.5
                                                        },
                                                        "text": "甲方的义务"
                                                    }
                                                ]
                                            },
                                            "type": "para"
                                        }
                                    ],
                                    "children": [
                                        {
                                            "blocks": [
                                                {
                                                    "bounding_box": {
                                                        "x1": 2503,
                                                        "x2": 10332,
                                                        "y1": 23989,
                                                        "y2": 24388
                                                    },
                                                    "id": "3BDB1A58pkEo0v12o836",
                                                    "index": 43,
                                                    "page_index": 1,
                                                    "para": {
                                                        "prop": {
                                                            "alignment": "left",
                                                            "def_run_prop": {
                                                                "color": "#000000ff",
                                                                "font_ascii": "宋体",
                                                                "font_east_asia": "宋体",
                                                                "size": 10.5
                                                            },
                                                            "list_string": "(1)",
                                                            "outline_level": 10
                                                        },
                                                        "runs": [
                                                            {
                                                                "id": "AL8Lw3epLlPNC3P8e836",
                                                                "prop": {
                                                                    "color": "#000000ff",
                                                                    "font_ascii": "宋体",
                                                                    "font_east_asia": "宋体",
                                                                    "size": 10.5
                                                                },
                                                                "text": "按时向乙方支付服务费；"
                                                            }
                                                        ]
                                                    },
                                                    "type": "para"
                                                }
                                            ],
                                            "children": None,
                                            "outline_level": 10
                                        },
                                        {
                                            "blocks": [
                                                {
                                                    "bounding_box": {
                                                        "x1": 2503,
                                                        "x2": 10332,
                                                        "y1": 24389,
                                                        "y2": 24788
                                                    },
                                                    "id": "4C8961CEpXnFJG21g848",
                                                    "index": 44,
                                                    "page_index": 1,
                                                    "para": {
                                                        "prop": {
                                                            "alignment": "left",
                                                            "def_run_prop": {
                                                                "color": "#000000ff",
                                                                "font_ascii": "宋体",
                                                                "font_east_asia": "宋体",
                                                                "size": 10.5
                                                            },
                                                            "list_string": "(2)",
                                                            "outline_level": 10
                                                        },
                                                        "runs": [
                                                            {
                                                                "id": "qHVsnKz9CNhW4Vu4A848",
                                                                "prop": {
                                                                    "color": "#000000ff",
                                                                    "font_ascii": "宋体",
                                                                    "font_east_asia": "宋体",
                                                                    "size": 10.5
                                                                },
                                                                "text": "配合乙方做好月嫂工作，并提供合理的协助；"
                                                            }
                                                        ]
                                                    },
                                                    "type": "para"
                                                }
                                            ],
                                            "children": None,
                                            "outline_level": 10
                                        },
                                        {
                                            "blocks": [
                                                {
                                                    "bounding_box": {
                                                        "x1": 2503,
                                                        "x2": 10332,
                                                        "y1": 24789,
                                                        "y2": 25188
                                                    },
                                                    "id": "11049DC4P7aKnw19y869",
                                                    "index": 45,
                                                    "page_index": 1,
                                                    "para": {
                                                        "prop": {
                                                            "alignment": "left",
                                                            "def_run_prop": {
                                                                "color": "#000000ff",
                                                                "font_ascii": "宋体",
                                                                "font_east_asia": "宋体",
                                                                "size": 10.5
                                                            },
                                                            "list_string": "(3)",
                                                            "outline_level": 10
                                                        },
                                                        "runs": [
                                                            {
                                                                "id": "wI02LFrmCgN0e68xf869",
                                                                "prop": {
                                                                    "color": "#000000ff",
                                                                    "font_ascii": "宋体",
                                                                    "font_east_asia": "宋体",
                                                                    "size": 10.5
                                                                },
                                                                "text": "为乙方提供与甲方基本相同的食宿条件。"
                                                            }
                                                        ]
                                                    },
                                                    "type": "para"
                                                }
                                            ],
                                            "children": None,
                                            "outline_level": 10
                                        }
                                    ],
                                    "outline_level": 2
                                },
                                {
                                    "blocks": [
                                        {
                                            "bounding_box": {
                                                "x1": 2503,
                                                "x2": 10332,
                                                "y1": 25189,
                                                "y2": 25588
                                            },
                                            "id": "23309F09srZSGMK6Q888",
                                            "index": 46,
                                            "page_index": 1,
                                            "para": {
                                                "prop": {
                                                    "alignment": "left",
                                                    "def_run_prop": {
                                                        "color": "#000000ff",
                                                        "font_ascii": "宋体",
                                                        "font_east_asia": "宋体",
                                                        "size": 10.5
                                                    },
                                                    "list_string": "三、",
                                                    "outline_level": 2
                                                },
                                                "runs": [
                                                    {
                                                        "id": "suWD1PRsnNlDjj08J888",
                                                        "prop": {
                                                            "color": "#000000ff",
                                                            "font_ascii": "宋体",
                                                            "font_east_asia": "宋体",
                                                            "size": 10.5
                                                        },
                                                        "text": "乙方的权利"
                                                    }
                                                ]
                                            },
                                            "type": "para"
                                        }
                                    ],
                                    "children": [
                                        {
                                            "blocks": [
                                                {
                                                    "bounding_box": {
                                                        "x1": 2503,
                                                        "x2": 10332,
                                                        "y1": 25589,
                                                        "y2": 25988
                                                    },
                                                    "id": "3EA5A51C6V17uT29f894",
                                                    "index": 47,
                                                    "page_index": 1,
                                                    "para": {
                                                        "prop": {
                                                            "alignment": "left",
                                                            "def_run_prop": {
                                                                "color": "#000000ff",
                                                                "font_ascii": "宋体",
                                                                "font_east_asia": "宋体",
                                                                "size": 10.5
                                                            },
                                                            "list_string": "(1)",
                                                            "outline_level": 10
                                                        },
                                                        "runs": [
                                                            {
                                                                "id": "CYMVa8gtFc1G2mPeH894",
                                                                "prop": {
                                                                    "color": "#000000ff",
                                                                    "font_ascii": "宋体",
                                                                    "font_east_asia": "宋体",
                                                                    "size": 10.5
                                                                },
                                                                "text": "甲方增加服务内容以外的工作，乙方有权要求增加适当的报酬；"
                                                            }
                                                        ]
                                                    },
                                                    "type": "para"
                                                }
                                            ],
                                            "children": None,
                                            "outline_level": 10
                                        },
                                        {
                                            "blocks": [
                                                {
                                                    "bounding_box": {
                                                        "x1": 2503,
                                                        "x2": 10332,
                                                        "y1": 25989,
                                                        "y2": 26388
                                                    },
                                                    "id": "537AD304TDlZwE18y923",
                                                    "index": 48,
                                                    "page_index": 1,
                                                    "para": {
                                                        "prop": {
                                                            "alignment": "left",
                                                            "def_run_prop": {
                                                                "color": "#000000ff",
                                                                "font_ascii": "宋体",
                                                                "font_east_asia": "宋体",
                                                                "size": 10.5
                                                            },
                                                            "list_string": "(2)",
                                                            "outline_level": 10
                                                        },
                                                        "runs": [
                                                            {
                                                                "id": "1lOvDwTsoitXsVK4C923",
                                                                "prop": {
                                                                    "color": "#000000ff",
                                                                    "font_ascii": "宋体",
                                                                    "font_east_asia": "宋体",
                                                                    "size": 10.5
                                                                },
                                                                "text": "乙方有权拒绝甲方不合理的工作安排。"
                                                            }
                                                        ]
                                                    },
                                                    "type": "para"
                                                }
                                            ],
                                            "children": None,
                                            "outline_level": 10
                                        }
                                    ],
                                    "outline_level": 2
                                },
                                {
                                    "blocks": [
                                        {
                                            "bounding_box": {
                                                "x1": 2503,
                                                "x2": 10332,
                                                "y1": 26389,
                                                "y2": 26788
                                            },
                                            "id": "3B84E0D3dX7kEHp6v941",
                                            "index": 49,
                                            "page_index": 1,
                                            "para": {
                                                "prop": {
                                                    "alignment": "left",
                                                    "def_run_prop": {
                                                        "color": "#000000ff",
                                                        "font_ascii": "宋体",
                                                        "font_east_asia": "宋体",
                                                        "size": 10.5
                                                    },
                                                    "list_string": "四、",
                                                    "outline_level": 2
                                                },
                                                "runs": [
                                                    {
                                                        "id": "YboJ604rhC1jBBTpD941",
                                                        "prop": {
                                                            "color": "#000000ff",
                                                            "font_ascii": "宋体",
                                                            "font_east_asia": "宋体",
                                                            "size": 10.5
                                                        },
                                                        "text": "乙方的义务"
                                                    }
                                                ]
                                            },
                                            "type": "para"
                                        }
                                    ],
                                    "children": [
                                        {
                                            "blocks": [
                                                {
                                                    "bounding_box": {
                                                        "x1": 2503,
                                                        "x2": 10332,
                                                        "y1": 26789,
                                                        "y2": 27188
                                                    },
                                                    "id": "68CD57334YWsvk16t947",
                                                    "index": 50,
                                                    "page_index": 1,
                                                    "para": {
                                                        "prop": {
                                                            "alignment": "left",
                                                            "def_run_prop": {
                                                                "color": "#000000ff",
                                                                "font_ascii": "宋体",
                                                                "font_east_asia": "宋体",
                                                                "size": 10.5
                                                            },
                                                            "list_string": "(1)",
                                                            "outline_level": 10
                                                        },
                                                        "runs": [
                                                            {
                                                                "id": "9ED6W7UHkvn4w8lQk947",
                                                                "prop": {
                                                                    "color": "#000000ff",
                                                                    "font_ascii": "宋体",
                                                                    "font_east_asia": "宋体",
                                                                    "size": 10.5
                                                                },
                                                                "text": "严格按服务内容为甲方提供服务；"
                                                            }
                                                        ]
                                                    },
                                                    "type": "para"
                                                }
                                            ],
                                            "children": None,
                                            "outline_level": 10
                                        },
                                        {
                                            "blocks": [
                                                {
                                                    "bounding_box": {
                                                        "x1": 2503,
                                                        "x2": 10332,
                                                        "y1": 27189,
                                                        "y2": 27988
                                                    },
                                                    "id": "27DC6E33RyulyU41S963",
                                                    "index": 51,
                                                    "page_index": 1,
                                                    "para": {
                                                        "prop": {
                                                            "alignment": "left",
                                                            "def_run_prop": {
                                                                "color": "#000000ff",
                                                                "font_ascii": "宋体",
                                                                "font_east_asia": "宋体",
                                                                "size": 10.5
                                                            },
                                                            "list_string": "(2)",
                                                            "outline_level": 10
                                                        },
                                                        "runs": [
                                                            {
                                                                "id": "yZOf4gcGvbkaY3sEJ963",
                                                                "prop": {
                                                                    "color": "#000000ff",
                                                                    "font_ascii": "宋体",
                                                                    "font_east_asia": "宋体",
                                                                    "size": 10.5
                                                                },
                                                                "text": "在工作时，乙方需爱护甲方的财物，如因乙方的原因，给甲方造成损失的，乙方给予赔偿。"
                                                            }
                                                        ]
                                                    },
                                                    "type": "para"
                                                }
                                            ],
                                            "children": None,
                                            "outline_level": 10
                                        }
                                    ],
                                    "outline_level": 2
                                }
                            ],
                            "outline_level": 1
                        },
                        {
                            "blocks": [
                                {
                                    "bounding_box": {
                                        "x1": 2503,
                                        "x2": 10332,
                                        "y1": 27989,
                                        "y2": 28788
                                    },
                                    "id": "556A5488J9j6U42M1004",
                                    "index": 52,
                                    "page_index": 1,
                                    "para": {
                                        "prop": {
                                            "alignment": "left",
                                            "def_run_prop": {
                                                "color": "#000000ff",
                                                "font_ascii": "宋体",
                                                "font_east_asia": "宋体",
                                                "size": 10.5
                                            },
                                            "list_string": "第三条 ",
                                            "outline_level": 1
                                        },
                                        "runs": [
                                            {
                                                "id": "0l2OqQQt1XaKkOsR1004",
                                                "prop": {
                                                    "color": "#000000ff",
                                                    "font_ascii": "宋体",
                                                    "font_east_asia": "宋体",
                                                    "size": 10.5
                                                },
                                                "text": "在合同期内，乙方有下列情况之一时，甲方有权立即终止合同，并按工作天折算费用给乙方"
                                            },
                                            {
                                                "id": "jXxBri47M3fFWmOO1044",
                                                "prop": {
                                                    "color": "#000000ff",
                                                    "font_ascii": "宋体",
                                                    "font_east_asia": "宋体",
                                                    "size": 10.5
                                                },
                                                "text": ":"
                                            }
                                        ]
                                    },
                                    "type": "para"
                                }
                            ],
                            "children": [
                                {
                                    "blocks": [
                                        {
                                            "bounding_box": {
                                                "x1": 2503,
                                                "x2": 10332,
                                                "y1": 28789,
                                                "y2": 29188
                                            },
                                            "id": "192FE84EdselX11r1046",
                                            "index": 53,
                                            "page_index": 1,
                                            "para": {
                                                "prop": {
                                                    "alignment": "left",
                                                    "def_run_prop": {
                                                        "color": "#000000ff",
                                                        "font_ascii": "宋体",
                                                        "font_east_asia": "宋体",
                                                        "size": 10.5
                                                    },
                                                    "list_string": "(1)",
                                                    "outline_level": 10
                                                },
                                                "runs": [
                                                    {
                                                        "id": "bJiCRYwrPBvPwwDg1046",
                                                        "prop": {
                                                            "color": "#000000ff",
                                                            "font_ascii": "宋体",
                                                            "font_east_asia": "宋体",
                                                            "size": 10.5
                                                        },
                                                        "text": "违反国家法律、法规；"
                                                    }
                                                ]
                                            },
                                            "type": "para"
                                        }
                                    ],
                                    "children": None,
                                    "outline_level": 10
                                },
                                {
                                    "blocks": [
                                        {
                                            "bounding_box": {
                                                "x1": 2503,
                                                "x2": 10332,
                                                "y1": 29189,
                                                "y2": 29588
                                            },
                                            "id": "0D0819C7x8Uvl14x1057",
                                            "index": 54,
                                            "page_index": 1,
                                            "para": {
                                                "prop": {
                                                    "alignment": "left",
                                                    "def_run_prop": {
                                                        "color": "#000000ff",
                                                        "font_ascii": "宋体",
                                                        "font_east_asia": "宋体",
                                                        "size": 10.5
                                                    },
                                                    "list_string": "(2)",
                                                    "outline_level": 10
                                                },
                                                "runs": [
                                                    {
                                                        "id": "Lq1GeFKfGFEv1I5h1057",
                                                        "prop": {
                                                            "color": "#000000ff",
                                                            "font_ascii": "宋体",
                                                            "font_east_asia": "宋体",
                                                            "size": 10.5
                                                        },
                                                        "text": "不服从甲方合理工作安排的；"
                                                    }
                                                ]
                                            },
                                            "type": "para"
                                        }
                                    ],
                                    "children": None,
                                    "outline_level": 10
                                },
                                {
                                    "blocks": [
                                        {
                                            "bounding_box": {
                                                "x1": 2503,
                                                "x2": 10332,
                                                "y1": 29589,
                                                "y2": 29988
                                            },
                                            "id": "4F9B0E7226ABH15E1071",
                                            "index": 55,
                                            "page_index": 1,
                                            "para": {
                                                "prop": {
                                                    "alignment": "left",
                                                    "def_run_prop": {
                                                        "color": "#000000ff",
                                                        "font_ascii": "宋体",
                                                        "font_east_asia": "宋体",
                                                        "size": 10.5
                                                    },
                                                    "list_string": "(3)",
                                                    "outline_level": 10
                                                },
                                                "runs": [
                                                    {
                                                        "id": "I9wf1iKvrEEbcCtn1071",
                                                        "prop": {
                                                            "color": "#000000ff",
                                                            "font_ascii": "宋体",
                                                            "font_east_asia": "宋体",
                                                            "size": 10.5
                                                        },
                                                        "text": "未经甲方同意，乙方擅自离岗；"
                                                    }
                                                ]
                                            },
                                            "type": "para"
                                        }
                                    ],
                                    "children": None,
                                    "outline_level": 10
                                },
                                {
                                    "blocks": [
                                        {
                                            "bounding_box": {
                                                "x1": 2503,
                                                "x2": 10332,
                                                "y1": 29989,
                                                "y2": 30388
                                            },
                                            "id": "08CE9E6BLTU1M11n1086",
                                            "index": 56,
                                            "page_index": 1,
                                            "para": {
                                                "prop": {
                                                    "alignment": "left",
                                                    "def_run_prop": {
                                                        "color": "#000000ff",
                                                        "font_ascii": "宋体",
                                                        "font_east_asia": "宋体",
                                                        "size": 10.5
                                                    },
                                                    "list_string": "(4)",
                                                    "outline_level": 10
                                                },
                                                "runs": [
                                                    {
                                                        "id": "roqS6YLfJ7hgRPeT1086",
                                                        "prop": {
                                                            "color": "#000000ff",
                                                            "font_ascii": "宋体",
                                                            "font_east_asia": "宋体",
                                                            "size": 10.5
                                                        },
                                                        "text": "被证明有偷盗行为的；"
                                                    }
                                                ]
                                            },
                                            "type": "para"
                                        }
                                    ],
                                    "children": None,
                                    "outline_level": 10
                                },
                                {
                                    "blocks": [
                                        {
                                            "bounding_box": {
                                                "x1": 2503,
                                                "x2": 10332,
                                                "y1": 30389,
                                                "y2": 30788
                                            },
                                            "id": "7307013AeOpSe11D1097",
                                            "index": 57,
                                            "page_index": 1,
                                            "para": {
                                                "prop": {
                                                    "alignment": "left",
                                                    "def_run_prop": {
                                                        "color": "#000000ff",
                                                        "font_ascii": "宋体",
                                                        "font_east_asia": "宋体",
                                                        "size": 10.5
                                                    },
                                                    "list_string": "(5)",
                                                    "outline_level": 10
                                                },
                                                "runs": [
                                                    {
                                                        "id": "oJsiXTbXrFHtjLVq1097",
                                                        "prop": {
                                                            "color": "#000000ff",
                                                            "font_ascii": "宋体",
                                                            "font_east_asia": "宋体",
                                                            "size": 10.5
                                                        },
                                                        "text": "不能胜任相应工作的；"
                                                    }
                                                ]
                                            },
                                            "type": "para"
                                        }
                                    ],
                                    "children": None,
                                    "outline_level": 10
                                },
                                {
                                    "blocks": [
                                        {
                                            "bounding_box": {
                                                "x1": 2503,
                                                "x2": 10332,
                                                "y1": 30789,
                                                "y2": 31188
                                            },
                                            "id": "3754A89AkXcNF18M1108",
                                            "index": 58,
                                            "page_index": 1,
                                            "para": {
                                                "prop": {
                                                    "alignment": "left",
                                                    "def_run_prop": {
                                                        "color": "#000000ff",
                                                        "font_ascii": "宋体",
                                                        "font_east_asia": "宋体",
                                                        "size": 10.5
                                                    },
                                                    "list_string": "(6)",
                                                    "outline_level": 10
                                                },
                                                "runs": [
                                                    {
                                                        "id": "uyzAbRSgNQ2rSAoc1108",
                                                        "prop": {
                                                            "color": "#000000ff",
                                                            "font_ascii": "宋体",
                                                            "font_east_asia": "宋体",
                                                            "size": 10.5
                                                        },
                                                        "text": "身体有病，不能继续从事月嫂工作的。"
                                                    }
                                                ]
                                            },
                                            "type": "para"
                                        }
                                    ],
                                    "children": None,
                                    "outline_level": 10
                                }
                            ],
                            "outline_level": 1
                        },
                        {
                            "blocks": [
                                {
                                    "bounding_box": {
                                        "x1": 2503,
                                        "x2": 10332,
                                        "y1": 31189,
                                        "y2": 31988
                                    },
                                    "id": "26446BBF1Rabv43b1126",
                                    "index": 59,
                                    "page_index": 1,
                                    "para": {
                                        "prop": {
                                            "alignment": "left",
                                            "def_run_prop": {
                                                "color": "#000000ff",
                                                "font_ascii": "宋体",
                                                "font_east_asia": "宋体",
                                                "size": 10.5
                                            },
                                            "list_string": "第四条 ",
                                            "outline_level": 1
                                        },
                                        "runs": [
                                            {
                                                "id": "SHsfWJZPItp2XbJU1126",
                                                "prop": {
                                                    "color": "#000000ff",
                                                    "font_ascii": "宋体",
                                                    "font_east_asia": "宋体",
                                                    "size": 10.5
                                                },
                                                "text": "在合同期内，甲方有下列情况之一时，乙方有权立即终止合同，并要求甲方按工作天折算费用："
                                            }
                                        ]
                                    },
                                    "type": "para"
                                }
                            ],
                            "children": [
                                {
                                    "blocks": [
                                        {
                                            "bounding_box": {
                                                "x1": 2503,
                                                "x2": 10332,
                                                "y1": 31989,
                                                "y2": 32388
                                            },
                                            "id": "21D10202neaSW18D1169",
                                            "index": 60,
                                            "page_index": 1,
                                            "para": {
                                                "prop": {
                                                    "alignment": "left",
                                                    "def_run_prop": {
                                                        "color": "#000000ff",
                                                        "font_ascii": "宋体",
                                                        "font_east_asia": "宋体",
                                                        "size": 10.5
                                                    },
                                                    "list_string": "(1)",
                                                    "outline_level": 10
                                                },
                                                "runs": [
                                                    {
                                                        "id": "dZ5wGyKCVCZ9syDJ1169",
                                                        "prop": {
                                                            "color": "#000000ff",
                                                            "font_ascii": "宋体",
                                                            "font_east_asia": "宋体",
                                                            "size": 10.5
                                                        },
                                                        "text": "甲方的工作安排违反国家法律、法规；"
                                                    }
                                                ]
                                            },
                                            "type": "para"
                                        }
                                    ],
                                    "children": None,
                                    "outline_level": 10
                                },
                                {
                                    "blocks": [
                                        {
                                            "bounding_box": {
                                                "x1": 2503,
                                                "x2": 10332,
                                                "y1": 35855,
                                                "y2": 36254
                                            },
                                            "id": "770EED9BTYI6z32e1187",
                                            "index": 61,
                                            "page_index": 2,
                                            "para": {
                                                "prop": {
                                                    "alignment": "left",
                                                    "def_run_prop": {
                                                        "color": "#000000ff",
                                                        "font_ascii": "宋体",
                                                        "font_east_asia": "宋体",
                                                        "size": 10.5
                                                    },
                                                    "list_string": "(2)",
                                                    "outline_level": 10
                                                },
                                                "runs": [
                                                    {
                                                        "id": "Lu3QlAgBR2XWUrWt1187",
                                                        "prop": {
                                                            "color": "#000000ff",
                                                            "font_ascii": "宋体",
                                                            "font_east_asia": "宋体",
                                                            "size": 10.5
                                                        },
                                                        "text": "甲方以任何理由对乙方实施搜身、扣押钱物、殴打谩骂、威逼等行为。"
                                                    }
                                                ]
                                            },
                                            "type": "para"
                                        }
                                    ],
                                    "children": None,
                                    "outline_level": 10
                                }
                            ],
                            "outline_level": 1
                        },
                        {
                            "blocks": [
                                {
                                    "bounding_box": {
                                        "x1": 2503,
                                        "x2": 10332,
                                        "y1": 36255,
                                        "y2": 36654
                                    },
                                    "id": "4255C00C9FXsMQ5w1219",
                                    "index": 62,
                                    "page_index": 2,
                                    "para": {
                                        "prop": {
                                            "alignment": "left",
                                            "def_run_prop": {
                                                "color": "#000000ff",
                                                "font_ascii": "宋体",
                                                "font_east_asia": "宋体",
                                                "size": 10.5
                                            },
                                            "list_string": "第五条 ",
                                            "outline_level": 1
                                        },
                                        "runs": [
                                            {
                                                "id": "9MphTrUnIlXegO7J1219",
                                                "prop": {
                                                    "color": "#000000ff",
                                                    "font_ascii": "宋体",
                                                    "font_east_asia": "宋体",
                                                    "size": 10.5
                                                },
                                                "text": "其他约定"
                                            }
                                        ]
                                    },
                                    "type": "para"
                                }
                            ],
                            "children": [
                                {
                                    "blocks": [
                                        {
                                            "bounding_box": {
                                                "x1": 2503,
                                                "x2": 10332,
                                                "y1": 36655,
                                                "y2": 37454
                                            },
                                            "id": "1A47B092hTBnv42x1224",
                                            "index": 63,
                                            "page_index": 2,
                                            "para": {
                                                "prop": {
                                                    "alignment": "left",
                                                    "def_run_prop": {
                                                        "color": "#000000ff",
                                                        "font_ascii": "宋体",
                                                        "font_east_asia": "宋体",
                                                        "size": 10.5
                                                    },
                                                    "list_string": "一、",
                                                    "outline_level": 2
                                                },
                                                "runs": [
                                                    {
                                                        "id": "o7Htvm0mSXyC4buQ1224",
                                                        "prop": {
                                                            "color": "#000000ff",
                                                            "font_ascii": "宋体",
                                                            "font_east_asia": "宋体",
                                                            "size": 10.5
                                                        },
                                                        "text": "1．由于乙方的违法行为或其他责任造成甲方损失的，由乙方承担相关责任，赔偿甲方损失。"
                                                    }
                                                ]
                                            },
                                            "type": "para"
                                        }
                                    ],
                                    "children": None,
                                    "outline_level": 2
                                },
                                {
                                    "blocks": [
                                        {
                                            "bounding_box": {
                                                "x1": 2503,
                                                "x2": 10332,
                                                "y1": 37455,
                                                "y2": 38254
                                            },
                                            "id": "67B4F14ECtygx43b1266",
                                            "index": 64,
                                            "page_index": 2,
                                            "para": {
                                                "prop": {
                                                    "alignment": "left",
                                                    "def_run_prop": {
                                                        "color": "#000000ff",
                                                        "font_ascii": "宋体",
                                                        "font_east_asia": "宋体",
                                                        "size": 10.5
                                                    },
                                                    "list_string": "二、",
                                                    "outline_level": 2
                                                },
                                                "runs": [
                                                    {
                                                        "id": "OQhVcLYzKGm6siDF1266",
                                                        "prop": {
                                                            "color": "#000000ff",
                                                            "font_ascii": "宋体",
                                                            "font_east_asia": "宋体",
                                                            "size": 10.5
                                                        },
                                                        "text": "乙方在服务期间（包括路途中），由于自身或第三方原因造成自身伤害的责任由乙方自行负责。"
                                                    }
                                                ]
                                            },
                                            "type": "para"
                                        }
                                    ],
                                    "children": None,
                                    "outline_level": 2
                                },
                                {
                                    "blocks": [
                                        {
                                            "bounding_box": {
                                                "x1": 2503,
                                                "x2": 10332,
                                                "y1": 38255,
                                                "y2": 38654
                                            },
                                            "id": "25E379DCxqSML27R1309",
                                            "index": 65,
                                            "page_index": 2,
                                            "para": {
                                                "prop": {
                                                    "alignment": "left",
                                                    "def_run_prop": {
                                                        "color": "#000000ff",
                                                        "font_ascii": "宋体",
                                                        "font_east_asia": "宋体",
                                                        "size": 10.5
                                                    },
                                                    "list_string": "三、",
                                                    "outline_level": 2
                                                },
                                                "runs": [
                                                    {
                                                        "id": "VTelTsNIO0vbrY0C1309",
                                                        "prop": {
                                                            "color": "#000000ff",
                                                            "font_ascii": "宋体",
                                                            "font_east_asia": "宋体",
                                                            "size": 10.5
                                                        },
                                                        "text": "若乙方在服务期间表现良好，甲方愿无偿为乙方提供客源。"
                                                    }
                                                ]
                                            },
                                            "type": "para"
                                        }
                                    ],
                                    "children": None,
                                    "outline_level": 2
                                },
                                {
                                    "blocks": [
                                        {
                                            "bounding_box": {
                                                "x1": 2503,
                                                "x2": 10332,
                                                "y1": 38655,
                                                "y2": 39054
                                            },
                                            "id": "2D841686HlrmR36t1336",
                                            "index": 66,
                                            "page_index": 2,
                                            "para": {
                                                "prop": {
                                                    "alignment": "left",
                                                    "def_run_prop": {
                                                        "color": "#000000ff",
                                                        "font_ascii": "宋体",
                                                        "font_east_asia": "宋体",
                                                        "size": 10.5
                                                    },
                                                    "list_string": "四、",
                                                    "outline_level": 2
                                                },
                                                "runs": [
                                                    {
                                                        "id": "WQyurCBM3oxNMnRo1336",
                                                        "prop": {
                                                            "color": "#000000ff",
                                                            "font_ascii": "宋体",
                                                            "font_east_asia": "宋体",
                                                            "size": 10.5
                                                        },
                                                        "text": "服务期结束后，如甲方在母婴护理方面有疑问向乙方咨询时，乙方应予以解答。"
                                                    }
                                                ]
                                            },
                                            "type": "para"
                                        }
                                    ],
                                    "children": None,
                                    "outline_level": 2
                                }
                            ],
                            "outline_level": 1
                        },
                        {
                            "blocks": [
                                {
                                    "bounding_box": {
                                        "x1": 2503,
                                        "x2": 10332,
                                        "y1": 39055,
                                        "y2": 39454
                                    },
                                    "id": "25D6627DCPXBu16B1372",
                                    "index": 67,
                                    "page_index": 2,
                                    "para": {
                                        "prop": {
                                            "alignment": "left",
                                            "def_run_prop": {
                                                "color": "#000000ff",
                                                "font_ascii": "宋体",
                                                "font_east_asia": "宋体",
                                                "size": 10.5
                                            },
                                            "list_string": "第六条 ",
                                            "outline_level": 1
                                        },
                                        "runs": [
                                            {
                                                "id": "vf7lm9jG2FtVY9Sy1372",
                                                "prop": {
                                                    "color": "#000000ff",
                                                    "font_ascii": "宋体",
                                                    "font_east_asia": "宋体",
                                                    "size": 10.5
                                                },
                                                "text": "因履行本合同发生争议的解决办法"
                                            }
                                        ]
                                    },
                                    "type": "para"
                                }
                            ],
                            "children": None,
                            "outline_level": 1
                        },
                        {
                            "blocks": [
                                {
                                    "bounding_box": {
                                        "x1": 2503,
                                        "x2": 10332,
                                        "y1": 39455,
                                        "y2": 40254
                                    },
                                    "id": "5C681C901VxTS44o1388",
                                    "index": 68,
                                    "page_index": 2,
                                    "para": {
                                        "prop": {
                                            "alignment": "left",
                                            "def_run_prop": {
                                                "color": "#000000ff",
                                                "font_ascii": "宋体",
                                                "font_east_asia": "宋体",
                                                "size": 10.5
                                            },
                                            "list_string": "第七条 ",
                                            "outline_level": 1
                                        },
                                        "runs": [
                                            {
                                                "id": "NJq3DeT6Hklz4CmN1388",
                                                "prop": {
                                                    "color": "#000000ff",
                                                    "font_ascii": "宋体",
                                                    "font_east_asia": "宋体",
                                                    "size": 10.5
                                                },
                                                "text": "双方因履行本合同发生争议，应当先协商解决；协商不成的，可向所属行政区人民法院提出诉讼。"
                                            }
                                        ]
                                    },
                                    "type": "para"
                                }
                            ],
                            "children": None,
                            "outline_level": 1
                        },
                        {
                            "blocks": [
                                {
                                    "bounding_box": {
                                        "x1": 2503,
                                        "x2": 10332,
                                        "y1": 40255,
                                        "y2": 41454
                                    },
                                    "id": "1443909CInfZg93Y1432",
                                    "index": 69,
                                    "page_index": 2,
                                    "para": {
                                        "prop": {
                                            "alignment": "left",
                                            "def_run_prop": {
                                                "color": "#000000ff",
                                                "font_ascii": "宋体",
                                                "font_east_asia": "宋体",
                                                "size": 10.5
                                            },
                                            "list_string": "第八条 ",
                                            "outline_level": 1
                                        },
                                        "runs": [
                                            {
                                                "id": "rTXE8sv0MpYRo3Su1432",
                                                "prop": {
                                                    "color": "#000000ff",
                                                    "font_ascii": "宋体",
                                                    "font_east_asia": "宋体",
                                                    "size": 10.5
                                                },
                                                "text": "本合同自甲乙双方签字后生效。本合同一式二份，甲乙双方各执一份。"
                                            },
                                            {
                                                "id": "bWjbuTxDukb9E0CW1463",
                                                "prop": {
                                                    "color": "#000000ff",
                                                    "font_ascii": "宋体",
                                                    "font_east_asia": "宋体",
                                                    "size": 10.5
                                                },
                                                "text": "本合同自双方或双方法定代表人或其授权代表人签字并加盖公章之日起生效。有效期为1年，自2020年1月1日至2021年1月1日"
                                            }
                                        ]
                                    },
                                    "type": "para"
                                }
                            ],
                            "children": None,
                            "outline_level": 1
                        },
                        {
                            "blocks": [
                                {
                                    "bounding_box": {
                                        "x1": 2503,
                                        "x2": 10332,
                                        "y1": 41455,
                                        "y2": 41854
                                    },
                                    "id": "44AA5A46GCxbbK1j1525",
                                    "index": 70,
                                    "page_index": 2,
                                    "para": {
                                        "prop": {
                                            "alignment": "left",
                                            "def_run_prop": {
                                                "color": "#000000ff",
                                                "font_ascii": "宋体",
                                                "font_east_asia": "宋体",
                                                "size": 10.5
                                            },
                                            "outline_level": 1
                                        },
                                        "runs": None
                                    },
                                    "type": "para"
                                }
                            ],
                            "children": [
                                {
                                    "blocks": [
                                        {
                                            "id": "jAEHaSnDWrLW268r1526",
                                            "index": 71,
                                            "page_index": 2,
                                            "table": {
                                                "rows": [
                                                    {
                                                        "cells": [
                                                            {

                                                                "blocks": [
                                                                    {
                                                                        "bounding_box": {
                                                                            "x1": 2503,
                                                                            "x2": 6127,
                                                                            "y1": 41855,
                                                                            "y2": 42254
                                                                        },
                                                                        "id": "53A5862Bm63dcE7f1526",
                                                                        "index": 72,
                                                                        "page_index": 2,
                                                                        "para": {
                                                                            "prop": {
                                                                                "alignment": "left",
                                                                                "def_run_prop": {
                                                                                    "color": "#000000ff",
                                                                                    "font_ascii": "宋体",
                                                                                    "font_east_asia": "宋体",
                                                                                    "size": 10.5
                                                                                },
                                                                                "outline_level": 10
                                                                            },
                                                                            "runs": [
                                                                                {
                                                                                    "id": "unS3UOtltIF6eOTz1526",
                                                                                    "prop": {
                                                                                        "color": "#000000ff",
                                                                                        "font_ascii": "宋体",
                                                                                        "font_east_asia": "宋体",
                                                                                        "size": 10.5
                                                                                    },
                                                                                    "text": "甲方："
                                                                                },
                                                                                {
                                                                                    "id": "SBc8mGncR6z44wbm1529",
                                                                                    "prop": {
                                                                                        "color": "#000000ff",
                                                                                        "font_ascii": "宋体",
                                                                                        "font_east_asia": "宋体",
                                                                                        "size": 10.5
                                                                                    },
                                                                                    "text": "张伟"
                                                                                },
                                                                                {
                                                                                    "id": "9kkLqtwfRgQtxAdT1531",
                                                                                    "prop": {
                                                                                        "color": "#000000ff",
                                                                                        "font_ascii": "宋体",
                                                                                        "font_east_asia": "宋体",
                                                                                        "size": 10.5
                                                                                    },
                                                                                    "text": "　"
                                                                                }
                                                                            ]
                                                                        },
                                                                        "type": "para"
                                                                    }
                                                                ]
                                                            },
                                                            {
                                                                "blocks": [
                                                                    {
                                                                        "bounding_box": {
                                                                            "x1": 6764,
                                                                            "x2": 10388,
                                                                            "y1": 41855,
                                                                            "y2": 42254
                                                                        },
                                                                        "id": "251672BCzcMzhI7D1533",
                                                                        "index": 73,
                                                                        "page_index": 2,
                                                                        "para": {
                                                                            "prop": {
                                                                                "alignment": "left",
                                                                                "def_run_prop": {
                                                                                    "color": "#000000ff",
                                                                                    "font_ascii": "宋体",
                                                                                    "font_east_asia": "宋体",
                                                                                    "size": 10.5
                                                                                },
                                                                                "outline_level": 10
                                                                            },
                                                                            "runs": [
                                                                                {
                                                                                    "id": "JYfDxVuksWkC81lh1533",
                                                                                    "prop": {
                                                                                        "color": "#000000ff",
                                                                                        "font_ascii": "宋体",
                                                                                        "font_east_asia": "宋体",
                                                                                        "size": 10.5
                                                                                    },
                                                                                    "text": "　乙方："
                                                                                },
                                                                                {
                                                                                    "id": "AQxyIEGN3f0NsH3j1537",
                                                                                    "prop": {
                                                                                        "color": "#000000ff",
                                                                                        "font_ascii": "宋体",
                                                                                        "font_east_asia": "宋体",
                                                                                        "size": 10.5
                                                                                    },
                                                                                    "text": "李华"
                                                                                }
                                                                            ]
                                                                        },
                                                                        "type": "para"
                                                                    }
                                                                ]
                                                            }
                                                        ]
                                                    }
                                                ]
                                            },
                                            "type": "table"
                                        }
                                    ],
                                    "children": None,
                                    "outline_level": 10
                                },
                                {
                                    "blocks": [
                                        {
                                            "bounding_box": {
                                                "x1": 2083,
                                                "x2": 10332,
                                                "y1": 45455,
                                                "y2": 45854
                                            },
                                            "id": "5B7B766CC8KpZ10N1794",
                                            "index": 74,
                                            "page_index": 2,
                                            "para": {
                                                "prop": {
                                                    "alignment": "left",
                                                    "def_run_prop": {
                                                        "color": "#000000ff",
                                                        "font_ascii": "宋体",
                                                        "font_east_asia": "宋体",
                                                        "size": 10.5
                                                    },
                                                    "outline_level": 10
                                                },
                                                "runs": [
                                                    {
                                                        "id": "h3jqot18pOp1xG8I1794",
                                                        "prop": {
                                                            "color": "#000000ff",
                                                            "font_ascii": "宋体",
                                                            "font_east_asia": "宋体",
                                                            "size": 10.5
                                                        },
                                                        "text": "赖小子是测试工程师"
                                                    }
                                                ]
                                            },
                                            "type": "para"
                                        }
                                    ],
                                    "children": None,
                                    "outline_level": 10
                                }
                            ],
                            "outline_level": 1
                        }
                    ],
                    "outline_level": 0
                }
            },
            "is_partly_exported": False,
            "attachment_url": ""
        }
    ]


class MyTestCase(unittest.TestCase):
    def setUp(self):
        self.parser = DocxParse()
        self.kdc = docx_kdc_data()
        self.contxt = PipelineContext()
    def tearDown(self):
        pass

    def test_something(self):
        dst = self.parser.dst_generate(self.contxt, self.kdc)
        dst_to_json(dst)
