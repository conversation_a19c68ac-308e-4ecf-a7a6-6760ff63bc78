import unittest
from modules.flows.dst_builder.xls import XlsParse
from commons.db.storedao import StoreDao, StoreType
from modules.entity.dst_entity import print_dst_tree, dst_to_json
import os

from modules.pipeline.context import PipelineContext


class EtParseTest(unittest.TestCase):
    def setUp(self):
        StoreDao().init(
            host="ks3-cn-beijing.ksyun.com",
            ak=os.environ.get("ks3_common_ak", ""),
            sk=os.environ.get("ks3_common_sk", ""),
            bucket="kna-common", store_type=StoreType.Ks3
        )
        self.parser = XlsParse()
        self.kdc_url = "http://kna-common.ks3-cn-beijing.ksyuncs.com/dfh/et_0428.json?KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb&Expires=1745977362&Signature=%2FhY1lQrSDoU0J6jQb7gO0mhwcDo%3D"

        self.contxt = PipelineContext()
    def tearDown(self):
        pass

    def test_parse_xls(self):
        kdc_data = self.parser.data_process(kdc_ks3_url=self.kdc_url)
        dst_list = self.parser.dst_generate(self.contxt,kdc_data)
        print_dst_tree(dst_list)
        dst_to_json(dst_list)
