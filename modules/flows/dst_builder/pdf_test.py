# coding: utf-8
# <AUTHOR> wangziyang1
# @date    : 2025/4/21 22:21
import json

from modules.entity.dst_entity import FileType, DST
from typing import List
import unittest
from modules.flows.dst_builder.pdf import PdfParse
from commons.db.storedao import StoreDao, StoreType
import os

from modules.pipeline.context import PipelineContext, FileInfo


class PDFParseTest(unittest.TestCase):
    def setUp(self):
        StoreDao().init(
            host="ks3-cn-beijing.ksyun.com",
            ak=os.environ.get("ks3_common_ak", ""),
            sk=os.environ.get("ks3_common_sk", ""),
            bucket="kna-common", store_type=StoreType.Ks3
        )
        self.parser = PdfParse()
        self.kdc_url = "https://kna-common.ks3-cn-beijing.ksyuncs.com/wzy_test/pdf_kdc.txt?KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb&Expires=1805288859&Signature=hUwi17quqStq6FeEKy5i55Hsc9c%3D"
        self.contxt = PipelineContext()
    def tearDown(self):
        pass

    def test_parse_pdf(self):

        kdc_data = self.parser.data_process(kdc_ks3_url=self.kdc_url)
        dst_list = self.parser.dst_generate(self.contxt,kdc_data)
        dst_list = self.parser.dst_reprocess(self.contxt,dst_list)
        print("\n")
        self.parser.get_res(dst_list)
        # res = []
        # for dst in dst_list:
        #     res.append(dst.model_dump(mode="json"))
        #
        # print(json.dumps(res))
