# coding: utf-8
# <AUTHOR> linqi
# @date    : 2025/5/7 10:10
import hashlib
import time
from collections import defaultdict

import fitz
from PIL import Image
import io
import base64
import uuid
import logging
import re
from typing import Union, List
from io import BytesIO

from commons.thread.multiprocess import MultiProcess
from modules.common import calculate_original_bbox, upload_image
from modules.entity.crop_entity import SealDetectionResult, Box
from modules.entity.parse_entity import Image as ParseImage, ImageType, ParseRes
from commons.trace.tracer import async_trace_span
from modules.flows.callback import callback_parse_background
from modules.flows.dst_builder.dst_handler import ParserFactory
from modules.utils import ConnPool
from commons.db.storedao import StoreDao
from modules.pipeline.base import PipelineHandler
from modules.pipeline.context import PipelineContext, FileType, OcData
from modules.entity.kdc_enttiy import Document
from modules.entity.dst_entity import BBox, DST, DSTType, DSTAttribute
from modules.entity.chunk_entity import PageImage
from conf import ConfHandlerName
from services.datamodel import ParseTarget


class DSTMergeNode(PipelineHandler):
    def __init__(self, name: str):
        super().__init__(name)
        self.name = name

    # 后面优化的话可以把截表格、复选框、扫描件都共用一个截图方法，提前遍历一次chunk，判断哪些要截复选框，哪些要截表格，分别放到不同的列表里面，然后在截图方法里面遍历列表进行截图
    @async_trace_span
    async def process(self, context: PipelineContext) -> PipelineContext:
        res = []
        try:
            # res = crop_shot(context, res)
            _t0 = time.perf_counter()
            # 1. 合并多路解析的dst
            dst_list = context.sort_multiple_parse_dsts()
            # 2. dst的大纲等逻辑处理
            parser = ParserFactory.get_parser(context.file_info.file_type)
            if context.file_info.file_type == FileType.PDF or context.file_info.file_type == FileType.DOC or context.file_info.file_type == FileType.DOCX:
                dst_list = parser.dst_reprocess(context, dst_list)
            if dst_list is None:
                raise ValueError("Failed to generate DST list")
            # 3. 统计字数
            word_count = 0
            for dst in dst_list:
                if dst.dst_type == DSTType.IMAGE:
                    continue
                elif dst.dst_type == DSTType.TABLE:
                    word_count += len(re.sub(r'<.*?>|\s', '', dst.content[0]))
                elif dst.dst_type == DSTType.MINDMAP or dst.dst_type == DSTType.SPREADSHEET or dst.dst_type == DSTType.FLOWCHART or dst.dst_type == DSTType.OTHER:
                    continue
                else:
                    for content in dst.content:
                        word_count += len(content)
            context.file_info.word_count = word_count - 3  # 减去根节点的内容
            if context.word_count is not None and 0 < context.word_count < context.file_info.word_count:
                raise ValueError(
                    f"File word count ({context.file_info.word_count}) exceeds limit ({context.word_count})")
            context.dst = dst_list
            # 下面这里可以不加，因为dst的结果已经存到context.dst里面了
            context.handler_results[ConfHandlerName.dst_handler] = dst_list
            dur = time.perf_counter() - _t0
            context.business_log.info(f"DocumentParserNode processed {len(dst_list)} DSTs for file type {context.file_info.file_type} , total word count: {word_count}")
            context.business_log.info(f"DSTMergeNode process completed in {dur:.2f} seconds.")
            return context

        except Exception as e:
            logging.error(f"Error in CropNode.process: {e}")
            raise e



