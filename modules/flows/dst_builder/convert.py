"""
Description: This module is used to convert the output of the parser to the desired format.
Author: <PERSON>hangdaqing
Date: 2025-1-8
"""
# from commons.bigtree import Node, add_dict_to_tree_by_path
# from modules.common import ParseOutput, MetaData
#
# def to_dst(dict_of_json):
#     """
#     convert to document structure tree format
#     :param dict_of_json:
#     :return dict_of_json:
#     """
#     # 将meta.url 拼接到content中
#     for k, v in dict_of_json.items():
#         if v.get("type") == "image":
#             v["content"] = v.get("meta", {}).get("url")
#         else:
#             continue
#     root = Node("1")
#     root = add_dict_to_tree_by_path(root, dict_of_json)
#     # tree = root.show(attr_list=["title", "content", "type", "page_num", "bbox", "meta"])
#     # 只保留文本和图片链接
#     tree = root.show(attr_list=["title", "content"])
#     return tree
#
# def to_json(dict_of_json):
#     """
#     convert to json format
#     :param dict_of_json:
#     :return json:
#     """
#     result = []
#     for k, v in dict_of_json.items():
#         # 将类型为heading的元素的类型转换为title
#         item_type = v.get("type")
#         if item_type == "main heading":
#             if not v["content"].strip():
#                 _type = None
#             else:
#                 _type = "title"
#         elif item_type in ["heading 1", "heading 2", "heading 3"]:
#             _type = "title"
#         elif item_type == "text":
#             _type = "text"
#         elif item_type == "table":
#             _type = "table"
#         elif item_type == "image":
#             _type = "figure"
#         elif item_type == "title":
#             _type = "title"
#         else:
#             continue
#
#         _meta = {} if len(v.get("meta", {}))==0 else v.get("meta")
#         meta = MetaData.parse_obj(_meta)
#
#         if _type:
#             # 将bbox统一输出为list的格式
#             if v.get("bbox"):
#                 bbox = v.get("bbox")
#                 if isinstance(bbox, dict):
#                     bbox = [bbox.get("x1"), bbox.get("y1"), bbox.get("x2"), bbox.get("y2")]
#                 else:
#                     bbox = bbox
#             else:
#                 bbox = None
#             data = ParseOutput(index=k,title=v.get("title"), content=v.get("content"), type=_type,page_num=v.get("page_num"),bbox=bbox,meta=meta)
#         else:
#             data = ParseOutput(index=k,title=None, content=None, type='title',page_num=None,bbox=None,meta=meta)
#
#         result.append(data)
#     return result
#
# def to_markdown(dict_of_json):
#     """
#     convert to Markdown format
#     :param dict_of_json:
#     :return: markdown
#     """
#     markdown = ""
#     for key, value in dict_of_json.items():
#         value_type = value.get("type")
#         if value_type == "title":
#             if value['title']:
#                 markdown += f"# {value['title']}\n\n"
#         elif value_type == "text":
#             if value['content']:
#                 markdown += f"{value['content']}\n\n"
#         elif value_type == "table":
#             if value['title']:
#                 markdown += f"## {value['title']}\n\n"
#             markdown += f"{value['content']}\n\n"
#         elif value_type == "figure" or value_type == "image":
#             markdown += f"![图片]({value['meta']['url']})\n\n"
#         # 针对docx
#         elif value_type == "main heading":
#             markdown += f"# {value['title']}\n\n"
#             markdown += f"{value['content']}\n\n"
#         elif value_type == "heading 1":
#             markdown += f"## {value['title']}\n\n"
#             markdown += f"{value['content']}\n\n"
#         elif value_type == "heading 2":
#             markdown += f"### {value['title']}\n\n"
#             markdown += f"{value['content']}\n\n"
#         elif value_type == "heading 3":
#             markdown += f"#### {value['title']}\n\n"
#             markdown += f"{value['content']}\n\n"
#         else:
#             continue
#     return markdown