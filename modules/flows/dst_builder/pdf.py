# coding: utf-8
# <AUTHOR> wangziyang1
# @date    : 2025/4/21 22:21


import time

import logging
from typing import List, Dict, Any

from modules.flows.dst_builder.parse_template import ParseTemplate
from modules.entity.dst_entity import FileType, DST, BBox, DSTType, PositionInfo, dst_to_json, print_dst_tree, \
    get_page_dst, assign_order_to_dst
from modules.entity.kdc_enttiy import Document, Node, BlockType, Block, ComponentType, KDC_DOC_FIELD, OutlineLevel
from modules.flows.dst_builder.kdc_entity_parse import ComponentNode, TextboxNode, TableNode, ParaNode
from modules.common import build_media_map, build_root_dst, build_hyperlink_map
from modules.flows.filters.catalog_filter import get_catalog_pages
from modules.flows.filters.filter import dst_mark
from modules.layout.common import contains_chinese
from modules.layout.outline import StringFormatValidator, assign_levels, add_parent, is_main_text
from modules.layout.typesetting import typesetting_correct
from modules.pipeline.context import PipelineContext
from modules.rpc.kdc_rpc import PAGE_SIZE


class PdfParse(ParseTemplate):
    def __init__(self):
        super().__init__(FileType.PDF)
        self.kdc_data = None
        self.dst = None

    def _kdc_validate(self) -> List[Document]:
        kdc_doc_list = []
        for idx, item in enumerate(self.kdc_data):
            if item is None:
                logging.error(f"PdfParse._kdc_validate error: kdc item is None")
                continue
            if KDC_DOC_FIELD not in item.keys() and item[KDC_DOC_FIELD] is None:
                logging.error(f"PdfParse._kdc_validate error: doc is none or doc not in kdc item.keys():{item.keys()}")
                continue
            kdc_doc_list.append(Document().model_validate(item[KDC_DOC_FIELD]))
        return kdc_doc_list

    async def _extract_node_block(self,
                                  block: Block,
                                  parent_outline_level: int,
                                  index: int,
                                  parent_id: str,
                                  media_id2url: Dict[str, List[Any]],
                                  dst_list: List[DST],
                                  id2text: Dict[str, str],
                                  ) -> str:
        dst_id = parent_id
        match block.type:
            case BlockType.component:
                if block.component is not None and block.component.type == ComponentType.image:
                    dst_id = ComponentNode(block, media_id2url, id2text).process(parent_outline_level, parent_id, index,
                                                                                 dst_list)
            case BlockType.textbox:
                dst_id = TextboxNode(block, media_id2url, id2text).process(parent_outline_level, parent_id, index,
                                                                           dst_list)
            case BlockType.table:
                dst_id = await TableNode(block, media_id2url, id2text).process(parent_outline_level, parent_id, index,
                                                                               dst_list)
            case BlockType.para:
                dst_id = ParaNode(block, media_id2url, id2text).process(parent_outline_level, parent_id, index,
                                                                        dst_list)
        return dst_id

    async def _extract_kdc_node(self,
                                kdc_node: Node,
                                parent_id: str,
                                media_id2url: Dict[str, List[Any]],
                                dst_list: List[DST],
                                id2text: Dict[str, str],
                                ):
        parent_outline_level = kdc_node.outline_level
        if kdc_node.blocks is not None:
            index = 0
            for block in kdc_node.blocks:
                # kdc block 提取DST
                dst_id = await self._extract_node_block(block, parent_outline_level, index + len(dst_list), parent_id,
                                                        media_id2url, dst_list, id2text)
                if dst_id != parent_id:
                    index += 1
        if len(dst_list) > 0:
            # 最后一个block作为children的父节点
            children_parent_id = dst_list[-1].id
        else:
            children_parent_id = parent_id
        if kdc_node.children is not None:
            for child in kdc_node.children:
                # 递归遍历子节点
                await self._extract_kdc_node(child, children_parent_id, media_id2url, dst_list, id2text)

    async def dst_generate(self, context: PipelineContext, kdc_data: List[dict]) -> None | Dict[int,List[DST]]:
        self.kdc_data = kdc_data
        kdc_doc_list = self._kdc_validate()
        if len(kdc_doc_list) == 0:
            logging.error(f"PdfParse.dst_generate error: kdc_doc_list is empty")
            return None
        root_dst = build_root_dst()
        result = {}
        for index, kdc_doc in enumerate(kdc_doc_list):
            dst_list = [root_dst]
            id2text = build_hyperlink_map(kdc_doc.hyperlinks)
            # 媒体信息
            _t0 = time.perf_counter()

            medias_id2url = await build_media_map(context, kdc_doc.medias)
            context.business_log.info(
                f"PdfParse.dst_generate build_media_map took {time.perf_counter() - _t0:.2f} seconds")
            # 提取node
            await self._extract_kdc_node(kdc_doc.tree, root_dst.id, medias_id2url, dst_list, id2text)
            ## todo 判断一下是否会覆盖
            result[kdc_doc.page_start] = dst_list
            context.business_log.info(
                f"PdfParse._extract_kdc_node build_media_map took {time.perf_counter() - _t0:.2f} seconds")
        return result

    def dst_reprocess(self, context: PipelineContext, dst_list: List[DST]):
        page_dst = get_page_dst(dst_list)
        temp_dst_list,layout = typesetting_correct(dst_list[0],page_dst)
        context.layout = layout
        if temp_dst_list is not None:
            dst_list = temp_dst_list
        dst_list = dst_mark(dst_list, page_dst, context.file_info)
        catalog_page = get_catalog_pages(dst_list)
        rotate_map = {}
        if context.file_info.rotate_page is not None:
            for rotate, pages in context.file_info.rotate_page.items():
                for page in pages:
                    rotate_map[page] = rotate
        outline = {}
        font_size_count = {}
        outline_position = []
        chinese_count = 0
        for i in range(len(dst_list) - 1, -1, -1):
            current_dst = dst_list[i]
            if current_dst.dst_type == DSTType.ROOT:
                continue
            # 处理position
            if rotate_map.get(current_dst.attributes.page) is not None:
                current_dst.attributes.position = PositionInfo(bbox=BBox(
                    x1=current_dst.attributes.position.bbox.x1,
                    y1=current_dst.attributes.position.bbox.y1,
                    x2=current_dst.attributes.position.bbox.x2,
                    y2=current_dst.attributes.position.bbox.y2,
                    rotate=rotate_map.get(current_dst.attributes.page),
                ))
            dst_content = "".join(current_dst.content)
            if contains_chinese(dst_content):
                chinese_count += 1
            if current_dst.attributes.page <= catalog_page:
                continue
            if current_dst.dst_type != DSTType.TEXT:
                continue
            if current_dst.font_size is not None and current_dst.font_size > 0 and current_dst.attributes.level == OutlineLevel.l10:
                font_size_count[current_dst.font_size] = font_size_count.get(current_dst.font_size, 0) + 1

            if current_dst.attributes.level < OutlineLevel.l10 and current_dst.attributes.level != 0:
                outline[current_dst.id] = dst_content
                outline_x1 = current_dst.attributes.position.bbox.x1
                outline_position.extend([outline_x1 + offset for offset in range(-10, 11)])

        ##总结 outline的规律
        format_groups, is_common_patten = StringFormatValidator.analyze_format(outline)
        is_en = False
        if chinese_count == 0 or chinese_count / len(dst_list) < 0.1:
            is_en = True
        main_text_size = max(font_size_count, key=font_size_count.get, default=None)
        format_outline =[]
        for dst in dst_list:
            if dst.attributes.page <= catalog_page:
                continue
            if dst.dst_type == DSTType.ROOT:
                dst.attributes.level = -1
                continue
            if dst.dst_type != DSTType.TEXT:
                continue
            if is_common_patten and is_main_text(dst, main_text_size):
                continue
            dst_x1 = dst.attributes.position.bbox.x1
            if dst_x1 in outline_position:
                dst_content = "".join(dst.content).replace(" ","")
                matched_groups = StringFormatValidator.validate_against_groups(format_groups, dst_content,
                                                                               match_prefix=True)
                if matched_groups:
                    prefix, _ = matched_groups[0]
                    if is_en and prefix in StringFormatValidator.EN_PREFIXES and is_main_text(dst, main_text_size):
                        continue
                    StringFormatValidator.add_to_group(format_groups, dst.id, dst_content, prefix)
                    format_outline.append(dst.id)
        # 如果所有的需要纠错的大纲都是原始已经赋值过大纲就不需要重新赋值大纲
        for dst in dst_list:
            if dst.dst_type == DSTType.ROOT:
                continue
            dst.attributes.level = OutlineLevel.l10
        dst_list = assign_levels(is_en, format_groups, dst_list)
        dst_list = add_parent(dst_list)
        dst_list = assign_order_to_dst(dst_list)
        return dst_list

    def get_res(self, dst_list: List[DST]):
        for dst in dst_list:
            print("    " * dst.attributes.level + dst.parent)
            print("    " * dst.attributes.level + "".join(dst.content))
            print("    " * dst.attributes.level + dst.id)

    def get_outline(self, dst_list: List[DST]):
        outline = []
        for dst in dst_list:
            if dst.attributes.level < OutlineLevel.l10:
                outline.append("".join(dst.content))
        return outline
