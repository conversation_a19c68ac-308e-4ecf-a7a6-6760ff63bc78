# coding: utf-8
# <AUTHOR> xuwenhao1
# @date    : 2025/4/22
from typing import List

from modules.common import build_media_map, build_root_dst, build_hyperlink_map
from modules.entity.dst_entity import FileType, DST, get_page_dst, assign_order_to_dst
from modules.entity.kdc_enttiy import Document
from modules.flows.dst_builder.docx import traverse, is_image
from modules.flows.dst_builder.parse_template import ParseTemplate
from modules.layout.typesetting import typesetting_correct
from modules.pipeline.context import PipelineContext


def is_all_images(context: PipelineContext, kdc_data: List[dict],images: List[str]):
    """设置是否全部为图片"""
    document = Document().model_validate(kdc_data[0]["doc"])
    data_array = []
    if document.medias:
    # Iterate over document.medias and add .data to the array
        for media in document.medias:
            if hasattr(media, 'data'):  # Check if the media object has a 'data' attribute
                data_array.append(media.data)
        return is_image(document.tree,images),data_array
    return False, images


class DocParse(ParseTemplate):
    def __init__(self):
        super().__init__(FileType.DOC)

    async def dst_generate(self, context: PipelineContext, kdc_data: List[dict]):
        ls = []
        # index = 0
        document = Document().model_validate(kdc_data[0]["doc"])

        # 建立id和url的映射
        id2url = await build_media_map(context,document.medias)
        id2text = build_hyperlink_map(document.hyperlinks)
        root = build_root_dst()
        ls.append(root)
        await traverse(id2url, id2text, document.tree, parent_id=root.id, ls=ls)
        return {0: ls}

    def dst_reprocess(self, context: PipelineContext, dst_list:  List[DST]):
        page_dst = get_page_dst(dst_list)
        temp_dst_list,layout = typesetting_correct(dst_list[0],page_dst)
        context.layout = layout
        if temp_dst_list is not None:
            dst_list = temp_dst_list
            dst_list = assign_order_to_dst(dst_list)

        return dst_list
    def get_res(self, dst: DST):
        pass
