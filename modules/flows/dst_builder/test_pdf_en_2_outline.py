from typing import List
from unittest import TestCase

from modules.entity.dst_entity import print_dst_tree, print_dst_indent_tree, DST, get_page_dst
from modules.flows.chunk.test_docx import convert_to_dst_list
from modules.flows.dst_builder.pdf import PdfParse
from modules.layout.typesetting import determine_layout_with_middle_line, reorder_dst_by_page
from modules.pipeline.context import PipelineContext


def kdc_pdf_data_3():
    return [
        {
            "id": "d7987e9eb3c448889e809c747e4874c4",
            "parent": "-1",
            "order": 0,
            "dst_type": "root",
            "attributes": {
                "level": 0,
                "position": {
                    "bbox": {
                        "x1": 1,
                        "y1": 2,
                        "x2": 3,
                        "y2": 4,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 0,
                "hash": "roothashhashhashhashhashhashhashhashhashhash"
            },
            "content": [
                "根节点"
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": -1,
            "bold": False
        },
        {
            "id": "37e9dcd06f584928a43a00241932374e",
            "parent": "d7987e9eb3c448889e809c747e4874c4",
            "order": 1,
            "dst_type": "text",
            "attributes": {
                "level": 1,
                "position": {
                    "bbox": {
                        "x1": 1589,
                        "y1": 1079,
                        "x2": 10620,
                        "y2": 2079,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 0,
                "hash": "e3bd6f68e737f252c084219baca9f0395525e3f1"
            },
            "content": [
                "LLMs Meet Finance:Fine-Tuning FoundationModels for the Open FinLLM Leaderboard"
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 21.5,
            "bold": True
        },
        {
            "id": "bf4e7cc2d59b4d09bd42c9b320175abb",
            "parent": "37e9dcd06f584928a43a00241932374e",
            "order": 2,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 2179,
                        "y1": 2389,
                        "x2": 10609,
                        "y2": 2609,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 0,
                "hash": "4d22f060be62e1f58fe137cf39fca3a0bf443610"
            },
            "content": [
                "2nd Youran Sun3rd Mahendra Kumar1st Varun Rao"
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 8.5,
            "bold": False
        },
        {
            "id": "d5c01969030245c186df25740e680e53",
            "parent": "37e9dcd06f584928a43a00241932374e",
            "order": 3,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 1319,
                        "y1": 2710,
                        "x2": 11239,
                        "y2": 3640,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 0,
                "hash": "9dae5bfb73cd14ed0c6b7f4ddf547155f0dec08d"
            },
            "content": [
                "University of Maryland,College ParkDepartment of MathematicsUniversity of Maryland,College ParkMD,20742University of Maryland,College ParkMD,<EMAIL>,<EMAIL><EMAIL>"
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 10.0,
            "bold": False
        },
        {
            "id": "586d1138ab304b659f942492f961ad93",
            "parent": "37e9dcd06f584928a43a00241932374e",
            "order": 4,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 1740,
                        "y1": 3880,
                        "x2": 10229,
                        "y2": 4129,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 0,
                "hash": "b0fdec72954727b8f6fc0b8680bed36bb40f8309"
            },
            "content": [
                "4h Tejas Mutneja5ᵗh Agastya Mukherjee6hHaizhao Yang*"
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 10.0,
            "bold": False
        },
        {
            "id": "21fb1db76cb546a5bada44ec0928fca2",
            "parent": "37e9dcd06f584928a43a00241932374e",
            "order": 5,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 1029,
                        "y1": 4199,
                        "x2": 10950,
                        "y2": 5359,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 0,
                "hash": "4e81187923cd84b5757e2cc89129346d96bc6d1c"
            },
            "content": [
                "University of Maryland,College ParkUniversity of Maryland,College ParkDepartment of MathematicsMD,20742MD,20742Department <NAME_EMAIL><EMAIL> of Maryland,College ParkMD,<EMAIL>"
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 8.5,
            "bold": False
        },
        {
            "id": "55be4a8e2c9c415d990774bf50a1f6c7",
            "parent": "37e9dcd06f584928a43a00241932374e",
            "order": 6,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 359,
                        "y1": 4240,
                        "x2": 629,
                        "y2": 4729,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 0,
                "hash": "f6e1126cedebf23e1463aee73f9df08783640400"
            },
            "content": [
                "25"
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 13.5,
            "bold": False
        },
        {
            "id": "508a0ea7d2df4c93aa4ca6196b744bd7",
            "parent": "37e9dcd06f584928a43a00241932374e",
            "order": 7,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 359,
                        "y1": 5109,
                        "x2": 719,
                        "y2": 11160,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 0,
                "hash": "42b6c9d9e403742ffaa1becd62d1cc7949713a81"
            },
            "content": [
                "arXiv:2504.13125v1[cs.CL]17 Apr"
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 21.5,
            "bold": False
        },
        {
            "id": "9f46c0937bd647ecb7e51de327f837a1",
            "parent": "37e9dcd06f584928a43a00241932374e",
            "order": 8,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 940,
                        "y1": 5809,
                        "x2": 6000,
                        "y2": 7970,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 0,
                "hash": "d488006a2ad9e2c8b4f065b58e5553c5718533e6"
            },
            "content": [
                "Abstract—This paper investigates the application of large lan-guage models (LLMs)to financial tasks.We fine-tuned foundationmodels using the Open FinLLM Leaderboard as a benchmark.Building on Qwen2.5 and Deepseek-R1,we employed techniquesincluding supervised fine-tuning(SFT),direct preference opti-mization(DPO),and reinforcement learning(RL)to enhancetheir financial capabilities.The fine-tuned models demonstratedsubstantial performance gains across a wide range of financialtasks.Moreover,we measured the data scaling law in the financialdomain.Our work demonstrates the potential of large languagemodels (LLMs)in financial applications."
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 8.5,
            "bold": False
        },
        {
            "id": "88e1ac7ec4ce422eb5224e67d15e7ee5",
            "parent": "37e9dcd06f584928a43a00241932374e",
            "order": 9,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 6409,
                        "y1": 5809,
                        "x2": 9569,
                        "y2": 5949,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 0,
                "hash": "a94e7b4bf16adfcb2bc57f85dac1b4b5f7f05be5"
            },
            "content": [
                "Our main contributions are as follows:"
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 8.5,
            "bold": False
        },
        {
            "id": "5beaa41ce1334087b0d578c0b36504e3",
            "parent": "37e9dcd06f584928a43a00241932374e",
            "order": 10,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 6430,
                        "y1": 6090,
                        "x2": 11250,
                        "y2": 7210,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 0,
                "hash": "58045dd065bae2155c0df0ceb6290650876f0242"
            },
            "content": [
                "1)We fine-tuned the Qwen2.5 and Deepseek-R1 modelsusing techniques such as supervised fine-tuning (SFT)and direct preference optimization(DPO)and observedsignificant improvements in the models’performanceacross diverse tasks."
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 8.5,
            "bold": False
        },
        {
            "id": "8c341b90e1974022af87dc58e25ef32e",
            "parent": "37e9dcd06f584928a43a00241932374e",
            "order": 11,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 6409,
                        "y1": 7310,
                        "x2": 11259,
                        "y2": 8179,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 0,
                "hash": "5db7eb4b262cbc00ff88fe17cdc8f14aea5d25ac"
            },
            "content": [
                "2)We leverage reinforcement learning(RL)and chain-of-thought(CoT)prompting to synthesize data and furtherstrengthen the capabilities of LLMs in the financialdomain."
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 8.5,
            "bold": False
        },
        {
            "id": "a9fc79b3550e43808458c342bb48b368",
            "parent": "37e9dcd06f584928a43a00241932374e",
            "order": 12,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 940,
                        "y1": 7999,
                        "x2": 5979,
                        "y2": 8570,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 0,
                "hash": "65db21c0eb67aa09bc7006ec591b4e135b444c4c"
            },
            "content": [
                "Index Terms—Large Language Model,Foundation Mod-els,FinRL,DeepSeek,Supervised Fine-Tuning,ReinforcementLearning,Scaling Laws"
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 8.5,
            "bold": True
        },
        {
            "id": "c76559ad3cd04e14bace5c6516df740a",
            "parent": "37e9dcd06f584928a43a00241932374e",
            "order": 13,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 6409,
                        "y1": 8270,
                        "x2": 11250,
                        "y2": 8929,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 0,
                "hash": "25462e880e1ae5e835e6816435894deac2d7da26"
            },
            "content": [
                "3)We measured the data scaling law in the financial do-main and found it highly consistent with results reportedin previous literature."
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 8.5,
            "bold": False
        },
        {
            "id": "fe4fa2a7c12245f1811ee2f13d14ba4b",
            "parent": "37e9dcd06f584928a43a00241932374e",
            "order": 14,
            "dst_type": "text",
            "attributes": {
                "level": 2,
                "position": {
                    "bbox": {
                        "x1": 2689,
                        "y1": 8810,
                        "x2": 4249,
                        "y2": 8950,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 0,
                "hash": "aed283b0a67aba74a88b64da877d2ed17089fdf1"
            },
            "content": [
                "I.INTRODUCTION"
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 8.5,
            "bold": False
        },
        {
            "id": "d686808b99364643b8df6312982ae5b8",
            "parent": "fe4fa2a7c12245f1811ee2f13d14ba4b",
            "order": 15,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 929,
                        "y1": 9079,
                        "x2": 5990,
                        "y2": 11199,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 0,
                "hash": "bba7410c59219a0aa79fcbe5eff59569269ef4ea"
            },
            "content": [
                "With the development of Transformer-based Natural Lan-guage Processing(NLP),Large Language Models (LLMs)arebecoming a standard technology integrated into the humantoolkit.In the financial sector,LLMs are expected to assistin tasks such as making intelligent decisions and creatingpersonalized financial searches.The integration of LLMs intofinancial applications is expected to advance the developmentof open finance.This paper aims to explore the applicationsof reinforcement learning(RL)and LLMs in financial tasks."
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 8.5,
            "bold": False
        },
        {
            "id": "a927d066fe0a4e30840f38b27a91149d",
            "parent": "37e9dcd06f584928a43a00241932374e",
            "order": 16,
            "dst_type": "text",
            "attributes": {
                "level": 2,
                "position": {
                    "bbox": {
                        "x1": 7869,
                        "y1": 9219,
                        "x2": 9590,
                        "y2": 9360,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 0,
                "hash": "22e11f00c65d9e3108d8fa3ed176c835e4098aaa"
            },
            "content": [
                "II.RELATED WORK"
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 8.5,
            "bold": False
        },
        {
            "id": "e28f927c3cb04a07848737b6597fba49",
            "parent": "a927d066fe0a4e30840f38b27a91149d",
            "order": 17,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 6200,
                        "y1": 9580,
                        "x2": 11269,
                        "y2": 11410,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 0,
                "hash": "330571b0c0cde83a70eec4c7908e332f749303ab"
            },
            "content": [
                "a)Financial Reinforcement Learning Datasets andFrameworks:FNSPID2 provides a large-scale dataset span-ning from 1999 to 2023,containing 29.7 million stock pricerecords and 15.7 million time-aligned financial news articlesfor 4,775 S&P 500 companies.FinRL-Meta 13,4 is adata-centric library for financial reinforcement learning thattransforms dynamic real-world market data into gym-styleenvironments."
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 8.5,
            "bold": False
        },
        {
            "id": "3dbeb3bfd47941488ee44840212bde1f",
            "parent": "a927d066fe0a4e30840f38b27a91149d",
            "order": 18,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 940,
                        "y1": 11260,
                        "x2": 6000,
                        "y2": 13789,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 0,
                "hash": "c17014fc59995540c7580e9abaffed6e34ea19b3"
            },
            "content": [
                "The Open FinLLM Leaderboard is an open platform forevaluating the performance of LLMs on various financialtasks.It is based on Finben(Pixiu)四and includes 36datasets spanning 24 financial tasks,covering seven criticalaspects:information extraction(IE),textual analysis,questionanswering(QA),text generation,risk management,forecast-ing,and decision-making.In this paper,we employ varioustechniques,including prompt engineering,chain-of-thought(CoT)reasoning,and reinforcement learning(RL),to enhancethe performance of open-source LLMs on the Open FinLLMLeaderboard."
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 8.5,
            "bold": False
        },
        {
            "id": "8e9f6330568b4ded8b527bbdd7d068f6",
            "parent": "a927d066fe0a4e30840f38b27a91149d",
            "order": 19,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 6200,
                        "y1": 11519,
                        "x2": 11259,
                        "y2": 13639,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 0,
                "hash": "c27dd2f33e453a75cb13c9c432580ac28d9a1203"
            },
            "content": [
                "b)LLM-Enhanced Agents:FinRL-DeepSeek 15 intro-duces a trading agent that combines reinforcement learningwith large language models to enhance trading performance.[⑥summarized the approaches and results of participatingteams in the Financial Regulations Challenge at COLING2025.FinMind-Y-Me7 ranked first in the competition men-tioned above.In their solution,they applied techniques such asSequential Fine-Tuning and Task-Specific Prompts to enhancethe performance of the fine-tuned model."
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 10.0,
            "bold": False
        },
        {
            "id": "993820693c8a4a47b4d19176fd878c23",
            "parent": "a927d066fe0a4e30840f38b27a91149d",
            "order": 20,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 6209,
                        "y1": 13680,
                        "x2": 11250,
                        "y2": 14349,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 0,
                "hash": "d544119c8a9541e1385b60bf9bcb20f2cb92ed7a"
            },
            "content": [
                "c)Simulate micro-level behaviors using LLMs:Somestudies use LLM-based multi-agent systems to simulate micro-level behaviors,aiming to observe financial market dynamics"
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 8.5,
            "bold": False
        },
        {
            "id": "9630010788f24a0a90d9ea099e244a89",
            "parent": "a927d066fe0a4e30840f38b27a91149d",
            "order": 21,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 1110,
                        "y1": 13990,
                        "x2": 4849,
                        "y2": 14390,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 0,
                "hash": "f317bd98c570d2ec386094eb4a9f77be80c8a3c1"
            },
            "content": [
                "These authors contributed equally to this work.*Corresponding author:<NAME_EMAIL>."
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 8.5,
            "bold": False
        },
        {
            "id": "2a5dc3c06abe4c46bfd3875b0471d642",
            "parent": "a927d066fe0a4e30840f38b27a91149d",
            "order": 22,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 940,
                        "y1": 1019,
                        "x2": 5979,
                        "y2": 1449,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 1,
                "hash": "018a0125104e2f162270887d7b04c90c84993d27"
            },
            "content": [
                "and macroeconomics after removing the assumption of fullyrational agents 8,19."
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 9.0,
            "bold": False
        },
        {
            "id": "0e5e9ad4ccfc435dae476ae9e96df889",
            "parent": "a927d066fe0a4e30840f38b27a91149d",
            "order": 23,
            "dst_type": "image",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 6349,
                        "y1": 1030,
                        "x2": 11039,
                        "y2": 2469,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 1,
                "hash": "16df32b36493d1c07eb9ad732878678e4f42f24c"
            },
            "content": [
                "http://kna-common.ks3-cn-beijing.ksyuncs.com/insight-ai/doc-parser/1a951ff74af4433fa11977c864d806a1_1.png?Signature=9R7lM0Onm6cWhWfTBLkW7Ts0RzA%3D&Expires=3243410128&KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb"
            ],
            "image_pixel": [
                469,
                143
            ],
            "mark": None,
            "font_size": -1,
            "bold": False
        },
        {
            "id": "13256b61a007420aa36cecfb50a8b630",
            "parent": "37e9dcd06f584928a43a00241932374e",
            "order": 24,
            "dst_type": "text",
            "attributes": {
                "level": 2,
                "position": {
                    "bbox": {
                        "x1": 2589,
                        "y1": 1790,
                        "x2": 4360,
                        "y2": 1939,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 1,
                "hash": "694835b635946ac64d5855ef21468c96813f4dfc"
            },
            "content": [
                "III.METHODOLOGY"
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 9.0,
            "bold": False
        },
        {
            "id": "206a47d41fcd4ef6974ccf52986359b7",
            "parent": "13256b61a007420aa36cecfb50a8b630",
            "order": 25,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 949,
                        "y1": 2229,
                        "x2": 5990,
                        "y2": 3369,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 1,
                "hash": "00b97448fbb24b730eb91e32fce438cf37575db2"
            },
            "content": [
                "a)Supervised Fine-Tuning(SFT):SFT is mainly usedfor alignment,task adaptation,and knowledge enhancement.Therefore,we always start with SFT.Among all 41 datasets,28 provide training/validation data,and we use these data forSFT.Formally,the loss is given by"
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 9.0,
            "bold": False
        },
        {
            "id": "0a72559b93ad4d5e9f06df7a0ee9d121",
            "parent": "13256b61a007420aa36cecfb50a8b630",
            "order": 26,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 6209,
                        "y1": 2740,
                        "x2": 11239,
                        "y2": 3069,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 1,
                "hash": "4c2f4fbb723b60a2242efe946db207ce5c9711d0"
            },
            "content": [
                "Fig.1.Training flowchart showing the progression from base model to finalmodel through SFT,DPO,and RL stages."
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 9.0,
            "bold": False
        },
        {
            "id": "92de2afa2ea847518f40b5fccffec473",
            "parent": "13256b61a007420aa36cecfb50a8b630",
            "order": 27,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 2579,
                        "y1": 3640,
                        "x2": 4370,
                        "y2": 3839,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 1,
                "hash": "a227651466b5a4bc9387218e674e939adb480c16"
            },
            "content": [
                "LsFT=-Inπe(y+|x)"
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 10.0,
            "bold": False
        },
        {
            "id": "121d9f94aa8640db8eb3921cd522bc0a",
            "parent": "13256b61a007420aa36cecfb50a8b630",
            "order": 28,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 949,
                        "y1": 4100,
                        "x2": 6000,
                        "y2": 4529,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 1,
                "hash": "19e076f5a22c5aebcb3de025cfbc6ddf9450da45"
            },
            "content": [
                "where θ represents the parameters of the LLM,x is the query,and y+is the answer."
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 9.0,
            "bold": False
        },
        {
            "id": "0309bd083f9843ed9f4379047677278f",
            "parent": "13256b61a007420aa36cecfb50a8b630",
            "order": 29,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 1149,
                        "y1": 4599,
                        "x2": 5990,
                        "y2": 4790,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 1,
                "hash": "d0d6fdb8c3b6c39d694927b99e503995834205b7"
            },
            "content": [
                "Apart from directly applying SFT on the training set,perfor-"
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 9.0,
            "bold": False
        },
        {
            "id": "52b8d3a9cbd841eaabaf81b4f5de4c3f",
            "parent": "13256b61a007420aa36cecfb50a8b630",
            "order": 30,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 949,
                        "y1": 4819,
                        "x2": 5990,
                        "y2": 5010,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 1,
                "hash": "4af7db304996e1c2f668addfaff3eb32a314a66b"
            },
            "content": [
                "mance can be further improved by employing Sequential Fine-"
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 9.0,
            "bold": False
        },
        {
            "id": "e4f8733405f6412696d3db6707d643c3",
            "parent": "13256b61a007420aa36cecfb50a8b630",
            "order": 31,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 949,
                        "y1": 5070,
                        "x2": 5979,
                        "y2": 5269,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 1,
                "hash": "d9d10e9b723fc2247f29d603f25152b3aa58af09"
            },
            "content": [
                "Tuning and Task-Specific Prompts,as mentioned in FinMind-"
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 10.0,
            "bold": False
        },
        {
            "id": "ae7c85cdfce64de7aee8c5f0587f9042",
            "parent": "13256b61a007420aa36cecfb50a8b630",
            "order": 32,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 959,
                        "y1": 5310,
                        "x2": 1770,
                        "y2": 5480,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 1,
                "hash": "d95c894bea150db629ce06ed7b5651399ee18567"
            },
            "content": [
                "Y-Me忆."
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 9.0,
            "bold": False
        },
        {
            "id": "5bc65fc18e13471f8fd85824bda4f5b7",
            "parent": "13256b61a007420aa36cecfb50a8b630",
            "order": 33,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 940,
                        "y1": 5569,
                        "x2": 6000,
                        "y2": 7680,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 1,
                "hash": "ac1d7459a99bf1ab7d4b027b2dd1c1ec68478169"
            },
            "content": [
                "b)Direct Preference Optimization(DPO):After SFT,the performance of the large model improved significantly.However,we observed that the LLM tends to generate exces-sively long and repeating responses [10],I11.For example,if the correct answer is “Apple”,the model might output“Apple Apple Apple ..”due to its inability to determine aproper stopping point..To ensure that the model stops atthe appropriate point,we apply DPO fine-tuning after SFT.Specifically,the DPO loss is defined as"
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 9.0,
            "bold": False
        },
        {
            "id": "130bb9d0884c466584a4084c64e9f677",
            "parent": "13256b61a007420aa36cecfb50a8b630",
            "order": 34,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 940,
                        "y1": 8600,
                        "x2": 5990,
                        "y2": 9989,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 1,
                "hash": "057289ffae0e3a4c9f05a35d0208cfcc730b5752"
            },
            "content": [
                "where πe represents the current model,πref is the base modelafter SFT,βis a temperature hyperparameter that controls theoptimization strength (usually set to 1)and σ(·)is the Sigmoidfunction.For the positive data y+,we use the answers fromthe database,while the negative data y-consists of overlylong responses(or outputs that we consider undesirable)."
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 9.0,
            "bold": False
        },
        {
            "id": "3226c8a2518d4f7e8e4786e0b2e82db9",
            "parent": "13256b61a007420aa36cecfb50a8b630",
            "order": 35,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 949,
                        "y1": 10050,
                        "x2": 5990,
                        "y2": 11209,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 1,
                "hash": "50324a6a72a76563959ef11fc62bd3857ffc663a"
            },
            "content": [
                "c)Data Synthesis and Reinforcement Learning(RL):Insome cases,the dataset does not contain training data,andwe have to synthesize data for reinforcement learning.Thespecific procedure for RLis as follows:(1)Corpus Collection:gather relevant text data based on the theme of the dataset"
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 10.0,
            "bold": False
        },
        {
            "id": "d61efa8b1d44412ea5816e49723231d3",
            "parent": "13256b61a007420aa36cecfb50a8b630",
            "order": 36,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 940,
                        "y1": 11270,
                        "x2": 6000,
                        "y2": 11690,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 1,
                "hash": "d18ad9e6d2b385683cd67685657cd4315ec698c4"
            },
            "content": [
                "(2)LLM Annotation:the current LLMπt is prompted togenerate annotations using Chain-of-Thought(CoT)reasoning."
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 10.0,
            "bold": False
        },
        {
            "id": "1a964095d698468da643896fd4056a18",
            "parent": "13256b61a007420aa36cecfb50a8b630",
            "order": 37,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 949,
                        "y1": 11749,
                        "x2": 6000,
                        "y2": 13099,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 1,
                "hash": "9ea65b97d89cfd1ed68f567f6d324f1e586e7e31"
            },
            "content": [
                "(3)Answer Extraction:apply regular expressions to extract theanswer y+from the generated response.(4)Query Formatting:the extracted data is structured into a query x following theprompt format of the dataset.(5)Training:use the synthesizedx,y+pairs for SFT and DPO to train and denote the resultLLM as πt+1·"
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 9.0,
            "bold": False
        },
        {
            "id": "88ccfb7ec5044022936e69aa6759679b",
            "parent": "13256b61a007420aa36cecfb50a8b630",
            "order": 38,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 949,
                        "y1": 13179,
                        "x2": 6000,
                        "y2": 14060,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 1,
                "hash": "46387caac793db7aa63aa655403dda2c1bebf9a9"
            },
            "content": [
                "If computational resources allow,we can further use πt+1 togenerate additional data,then continue SFT or DPO to obtainπt+2.This iterative process can be repeated multiple times torefine the model."
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 9.0,
            "bold": False
        },
        {
            "id": "4efaff3bcfcd41e58cbe97e9d4556990",
            "parent": "13256b61a007420aa36cecfb50a8b630",
            "order": 39,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 1149,
                        "y1": 14160,
                        "x2": 5060,
                        "y2": 14349,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 1,
                "hash": "11ded8a2e4340e75a86073e0d6712d5d8ecf422d"
            },
            "content": [
                "The training flowchart is illustrated in Figure 1"
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 9.0,
            "bold": False
        },
        {
            "id": "c0b33274971f4bb0998a729f38f7a6d9",
            "parent": "37e9dcd06f584928a43a00241932374e",
            "order": 40,
            "dst_type": "text",
            "attributes": {
                "level": 2,
                "position": {
                    "bbox": {
                        "x1": 7939,
                        "y1": 3550,
                        "x2": 9529,
                        "y2": 3700,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 1,
                "hash": "7675840b35036fe26e93fdd5ceecf88bf98e5f44"
            },
            "content": [
                "IV.EXPERIMENTS"
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 9.0,
            "bold": False
        },
        {
            "id": "82925cfb47ca4d9489cde2e8f23fbe86",
            "parent": "c0b33274971f4bb0998a729f38f7a6d9",
            "order": 41,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 6209,
                        "y1": 3880,
                        "x2": 11250,
                        "y2": 5020,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 1,
                "hash": "ccbdb8e9e3c4d9bc8b487e9c11b1aac35e498ffa"
            },
            "content": [
                "a)The effectiveness of SFT:We fine-tuned the modelsusing LLaMAFactory I12 with a learning rate of 5e-5 and aconstant learning rate scheduler.The effective batch size wasset to 64.We used LoRA with a rank of 128 and an alpha of256.DeepSpeed [13]stage 2 was applied during training."
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 9.0,
            "bold": False
        },
        {
            "id": "1c2811bf32474971a3ff4d0de8ab939d",
            "parent": "c0b33274971f4bb0998a729f38f7a6d9",
            "order": 42,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 6209,
                        "y1": 5070,
                        "x2": 11259,
                        "y2": 7649,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 1,
                "hash": "a77bb0ae4a589ae3b9119d32102ffb0d45acbb82"
            },
            "content": [
                "TableI presents the results of training DeepSeek-R1-1.5Band Qwen2.5-1.5B-Instruct using data from NER(NamedEntity Recognition)and FiQASA(Financial QA-Style Sen-timent Analysis).Beyond reporting scores on the fine-tunedtasks,we also include results on CC(Causal Classification)and FPB(Financial Polarity Benchmark)to investigate themodels'generalization and transfer learning capabilities.Wecan observe that SFT significantly improves peformance inthe corresponding task.DeepSeek-R1-1.5B achieves higherperformance metrics than Qwen2.5-1.5B-Instruct,indicatingbetter task-specific generalization."
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 9.0,
            "bold": False
        },
        {
            "id": "095ae8b4ff4c49dd8b8c670e20ae92a3",
            "parent": "c0b33274971f4bb0998a729f38f7a6d9",
            "order": 43,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 6200,
                        "y1": 7690,
                        "x2": 11269,
                        "y2": 11439,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 1,
                "hash": "afe423ffd15f2850278548afe99e6c45da3d41be"
            },
            "content": [
                "However,the interactions between different tasks do notfollow a general pattern.Training on NER reduces the per-formance on CC,while training on FiQASA enhances theperformance on FPB.Both the FiQASA and FPB datasets in-volve sentiment classification tasks over sets of headlines.Thesimilarity in task strucure and headline formatting betweenthese datasets likely accounts for the substantial improvementin performance on FPB benchmarks following initial fine-tuning on FiQASA.In contrast,the NER dataset,whichfocuses primarily on the identification of named entities,mayhave hindered performance on the CC benchmark.This couldbe explained by the fine-tuned model overemphasizing namedentities at the expense of capturing the underlying semantic orcausal relationships between headlines.Moreover,SequentialFine-Tuning does not outperform direct fine-tuning across thetasks."
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 9.0,
            "bold": False
        },
        {
            "id": "c8f22f28b8254ddc837e105a980fd27e",
            "parent": "c0b33274971f4bb0998a729f38f7a6d9",
            "order": 44,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 6200,
                        "y1": 11540,
                        "x2": 11259,
                        "y2": 13649,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 1,
                "hash": "9ca1c85a6a21619f548bfa9bb1f0ea7fe70315f6"
            },
            "content": [
                "b)The effectiveness of DPO:As discussed in themethodology section,after SFT,the model tends to producerepetitive outputs;in other words,it learns to \"repeat the cor-rect answer tokens”rather than understanding the relationshipbetween the correct answer and the context.In this task,weare unable to modify the prompts or change the samplingparameters [10],so we apply DPO after SFT.We treat overlylong outputs generated by the SFT model on the training set asrejected labels and use the correct answers as accepted labels."
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 10.0,
            "bold": False
        },
        {
            "id": "bb45af8264b9471393447ffdda5facb5",
            "parent": "c0b33274971f4bb0998a729f38f7a6d9",
            "order": 45,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 6209,
                        "y1": 13680,
                        "x2": 11259,
                        "y2": 14349,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 1,
                "hash": "14e6013ce6a25dd40e76664ac028a6b0d42b3981"
            },
            "content": [
                "Table四presents the results of our DPO experiments.Wefirst perform SFT on Qwen2.5-1.5B-Instruct using an NERdataset and then apply DPO based on the fine-tuned model."
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 9.0,
            "bold": False
        },
        {
            "id": "214b897d07984430bf54b3152c73cff9",
            "parent": "c0b33274971f4bb0998a729f38f7a6d9",
            "order": 46,
            "dst_type": "image",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 1210,
                        "y1": 7879,
                        "x2": 5719,
                        "y2": 8430,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 1,
                "hash": "7a4331c87219d0ab3c30b1be598c288fce5eb227"
            },
            "content": [
                "http://kna-common.ks3-cn-beijing.ksyuncs.com/insight-ai/doc-parser/3be0544be0294c519ec934fefc73a731_0.png?Signature=DQ16JBq2Nf3fEPsnkcp180v1F4U%3D&Expires=3243410128&KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb"
            ],
            "image_pixel": [
                451,
                55
            ],
            "mark": None,
            "font_size": -1,
            "bold": False
        },
        {
            "id": "33d650e6afc7482ca1d6b828bcc980e8",
            "parent": "c0b33274971f4bb0998a729f38f7a6d9",
            "order": 47,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 2830,
                        "y1": 1050,
                        "x2": 9369,
                        "y2": 1399,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 2,
                "hash": "40ba2c80ccb757425925c47d6e9e15be6b5352eb"
            },
            "content": [
                "TABLE IEVALUATION OF DEEPSEEK AND QWEN ON MULTIPLE TASKS BEFORE AND AFTER FINE-TUNING."
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 10.0,
            "bold": False
        },
        {
            "id": "399e89ccb73e400aa39b3e2345b00511",
            "parent": "c0b33274971f4bb0998a729f38f7a6d9",
            "order": 48,
            "dst_type": "table",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 1000,
                        "y1": 1580,
                        "x2": 11299,
                        "y2": 4490,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 2,
                "hash": "698d0b07823209cc5964ce4123515ac2920861df"
            },
            "content": [
                "<table><tr><td rowspan=\"1\" colspan=\"2\">Model</td><td rowspan=\"1\" colspan=\"1\">Fine-tuned on</td><td rowspan=\"1\" colspan=\"1\">Datasize(#Tokens)</td><td rowspan=\"1\" colspan=\"1\">NER(F1)</td><td rowspan=\"1\" colspan=\"1\">CC(F1)</td><td rowspan=\"1\" colspan=\"1\">FiQASA(F1)</td><td rowspan=\"1\" colspan=\"1\">FPB(F1)</td><td rowspan=\"1\" colspan=\"1\"></td></tr><tr><td rowspan=\"1\" colspan=\"2\"></td><td rowspan=\"1\" colspan=\"1\">/</td><td rowspan=\"1\" colspan=\"1\">/</td><td rowspan=\"1\" colspan=\"1\">0.1448</td><td rowspan=\"1\" colspan=\"1\">0.6683</td><td rowspan=\"1\" colspan=\"1\">0.4383</td><td rowspan=\"1\" colspan=\"1\">0.1845</td><td rowspan=\"1\" colspan=\"1\"></td></tr><tr><td rowspan=\"1\" colspan=\"2\"></td><td rowspan=\"1\" colspan=\"1\">NER</td><td rowspan=\"1\" colspan=\"1\">408(51k)</td><td rowspan=\"1\" colspan=\"1\">0.7231</td><td rowspan=\"1\" colspan=\"1\">0.3290</td><td rowspan=\"1\" colspan=\"1\">0.2755</td><td rowspan=\"1\" colspan=\"1\">0.1433</td><td rowspan=\"1\" colspan=\"1\"></td></tr><tr><td rowspan=\"1\" colspan=\"2\"></td><td rowspan=\"1\" colspan=\"1\">FiQASA</td><td rowspan=\"1\" colspan=\"1\">750(34k)</td><td rowspan=\"1\" colspan=\"1\">0.0286</td><td rowspan=\"1\" colspan=\"1\">0.1977</td><td rowspan=\"1\" colspan=\"1\">0.7865</td><td rowspan=\"1\" colspan=\"1\">0.2123</td><td rowspan=\"1\" colspan=\"1\"></td></tr><tr><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\">DeepSeek-R1-1.5B</td><td rowspan=\"1\" colspan=\"1\">NER&FiQASA</td><td rowspan=\"1\" colspan=\"1\">1158(85k)</td><td rowspan=\"1\" colspan=\"1\">0.6913</td><td rowspan=\"1\" colspan=\"1\">0.0950</td><td rowspan=\"1\" colspan=\"1\">0.7584</td><td rowspan=\"1\" colspan=\"1\">0.2019</td><td rowspan=\"1\" colspan=\"1\"></td></tr><tr><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\">NER,then FiQASA</td><td rowspan=\"1\" colspan=\"1\">1158(85k)</td><td rowspan=\"1\" colspan=\"1\">0.7002</td><td rowspan=\"1\" colspan=\"1\">0.0967</td><td rowspan=\"1\" colspan=\"1\">0.7866</td><td rowspan=\"1\" colspan=\"1\">0.1355</td><td rowspan=\"1\" colspan=\"1\"></td></tr><tr><td rowspan=\"1\" colspan=\"2\"></td><td rowspan=\"1\" colspan=\"1\">FiQASA,then NER</td><td rowspan=\"1\" colspan=\"1\">1158(85k)</td><td rowspan=\"1\" colspan=\"1\">0.6540</td><td rowspan=\"1\" colspan=\"1\">0.0822</td><td rowspan=\"1\" colspan=\"1\">0.6984</td><td rowspan=\"1\" colspan=\"1\">0.1461</td><td rowspan=\"1\" colspan=\"1\"></td></tr><tr><td rowspan=\"1\" colspan=\"2\"></td><td rowspan=\"1\" colspan=\"1\">/</td><td rowspan=\"1\" colspan=\"1\">/</td><td rowspan=\"1\" colspan=\"1\">0.0060</td><td rowspan=\"1\" colspan=\"1\">0.6667</td><td rowspan=\"1\" colspan=\"1\">0.6789</td><td rowspan=\"1\" colspan=\"1\">0.2269</td><td rowspan=\"1\" colspan=\"1\"></td></tr><tr><td rowspan=\"1\" colspan=\"2\"></td><td rowspan=\"1\" colspan=\"1\">NER</td><td rowspan=\"1\" colspan=\"1\">408(51k)</td><td rowspan=\"1\" colspan=\"1\">0.6212</td><td rowspan=\"1\" colspan=\"1\">0.3962</td><td rowspan=\"1\" colspan=\"1\">0.6894</td><td rowspan=\"1\" colspan=\"1\">0.2141</td><td rowspan=\"1\" colspan=\"1\"></td></tr><tr><td rowspan=\"1\" colspan=\"2\"></td><td rowspan=\"1\" colspan=\"1\">FiQASA</td><td rowspan=\"1\" colspan=\"1\">750(34k)</td><td rowspan=\"1\" colspan=\"1\">0.0000</td><td rowspan=\"1\" colspan=\"1\">0.5144</td><td rowspan=\"1\" colspan=\"1\">0.8029</td><td rowspan=\"1\" colspan=\"1\">0.5016</td><td rowspan=\"1\" colspan=\"1\"></td></tr><tr><td rowspan=\"1\" colspan=\"2\">Qwen2.5-1.5B-Instruct</td><td rowspan=\"1\" colspan=\"1\">NER&FiQASA</td><td rowspan=\"1\" colspan=\"1\">1158(85k)</td><td rowspan=\"1\" colspan=\"1\">0.5926</td><td rowspan=\"1\" colspan=\"1\">0.5357</td><td rowspan=\"1\" colspan=\"1\">0.7703</td><td rowspan=\"1\" colspan=\"1\">0.6704</td><td rowspan=\"1\" colspan=\"1\"></td></tr><tr><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\">NER,then FiQASA</td><td rowspan=\"1\" colspan=\"1\">1158(85k)</td><td rowspan=\"1\" colspan=\"1\">0.0938</td><td rowspan=\"1\" colspan=\"1\">0.3055</td><td rowspan=\"1\" colspan=\"1\">0.8054</td><td rowspan=\"1\" colspan=\"1\">0.2359</td><td rowspan=\"1\" colspan=\"1\"></td></tr><tr><td rowspan=\"1\" colspan=\"2\"></td><td rowspan=\"1\" colspan=\"1\">FiQASA,then NER</td><td rowspan=\"1\" colspan=\"1\">1158(85k)</td><td rowspan=\"1\" colspan=\"1\">0.4873</td><td rowspan=\"1\" colspan=\"1\">0.2064</td><td rowspan=\"1\" colspan=\"1\">0.7999</td><td rowspan=\"1\" colspan=\"1\">0.3622</td><td rowspan=\"1\" colspan=\"1\"></td></tr><tr><td rowspan=\"1\" colspan=\"9\">Beyond reporting scores on the fine tuned task,we include results on other tasks to investigte the models’generalization and transfer learming capabilities.</td></tr></table>"
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": -1,
            "bold": False
        },
        {
            "id": "fde2139a846f49cda4709591644471e8",
            "parent": "c0b33274971f4bb0998a729f38f7a6d9",
            "order": 49,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 1189,
                        "y1": 4470,
                        "x2": 11029,
                        "y2": 4850,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 2,
                "hash": "5370e9c2557b3cef26bcbf7129b752dc066bde2d"
            },
            "content": [
                "Task abbreviations:NER(Named Entity Recognition),CC(Causal Classification),FiQASA(Financial QA-Style Sentiment Analysis),FPB (FinancialPolarity Benchmark)."
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 10.0,
            "bold": False
        },
        {
            "id": "57547ccd4b6c44faae322d906169839b",
            "parent": "c0b33274971f4bb0998a729f38f7a6d9",
            "order": 50,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 2470,
                        "y1": 5310,
                        "x2": 4470,
                        "y2": 5649,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 2,
                "hash": "bcfa5f95be562ed647cb0b6c2934cac855958d3e"
            },
            "content": [
                "TABLEⅡEFFECT OF DPO AFTER SFT."
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 10.0,
            "bold": False
        },
        {
            "id": "afcd65097c0948b894b3975a6700c0bc",
            "parent": "c0b33274971f4bb0998a729f38f7a6d9",
            "order": 51,
            "dst_type": "table",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 959,
                        "y1": 5830,
                        "x2": 6039,
                        "y2": 7070,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 2,
                "hash": "7c95efd2849fd28608f70d2cf31f7d3e5cdae533"
            },
            "content": [
                "<table><tr><td rowspan=\"1\" colspan=\"2\">Train Phase</td><td rowspan=\"1\" colspan=\"1\">Overlength Ratio</td><td rowspan=\"1\" colspan=\"1\">NER</td><td rowspan=\"1\" colspan=\"1\">CC</td></tr><tr><td rowspan=\"1\" colspan=\"2\">SFT</td><td rowspan=\"1\" colspan=\"1\">54.7%</td><td rowspan=\"1\" colspan=\"1\">0.6212</td><td rowspan=\"1\" colspan=\"1\">0.3962</td></tr><tr><td rowspan=\"1\" colspan=\"2\">+1 DPO</td><td rowspan=\"1\" colspan=\"1\">8.6%</td><td rowspan=\"1\" colspan=\"1\">0.6180</td><td rowspan=\"1\" colspan=\"1\">0.4437</td></tr><tr><td rowspan=\"1\" colspan=\"2\">+1 DPO</td><td rowspan=\"1\" colspan=\"1\">1.7%</td><td rowspan=\"1\" colspan=\"1\">0.5950</td><td rowspan=\"1\" colspan=\"1\">0.5592</td></tr><tr><td rowspan=\"1\" colspan=\"5\">The Overlength Ratio refers to the proportion of samples in the training set</td></tr></table>"
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": -1,
            "bold": False
        },
        {
            "id": "43ddfa883a0a443f87994f8826dcda54",
            "parent": "c0b33274971f4bb0998a729f38f7a6d9",
            "order": 52,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 1019,
                        "y1": 7019,
                        "x2": 5890,
                        "y2": 7169,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 2,
                "hash": "1c6e2f681d85edcb8aad3abdaee2a08b77953f80"
            },
            "content": [
                "where the number of output tokens exceeds the number of answer tokens."
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 7.5,
            "bold": False
        },
        {
            "id": "2afc6ef9622649fb8a293ca3683e025d",
            "parent": "c0b33274971f4bb0998a729f38f7a6d9",
            "order": 53,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 940,
                        "y1": 7600,
                        "x2": 6000,
                        "y2": 10469,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 2,
                "hash": "94bd73a5f9888f0a2ce9d81f295460bcf64b9951"
            },
            "content": [
                "The SFT configuration is the same as in TableⅡ For DPO,thetraining setup is identical to that of SFT,except that the LoRArank and LoRA alpha are reduced to 16 and 32,respectively.As shown,the Overlength Ratio is significantly reduced afterapplying DPO,which aligns with our original motivation forusing DPO.In addition to the Overlength Ratio,we alsorecorded the F¹scores for the NER and CC tasks to investigatethe impact of DPO on other tasks.We observe that the F1score for the trained NER task remains unchanged mainly(theslight decrease may be due to the scoring mechanism of thebenchmark),while the score for the unseen CC task actuallyimproves."
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 10.0,
            "bold": False
        },
        {
            "id": "ef6eb53b8d2345328ace2818df065f82",
            "parent": "c0b33274971f4bb0998a729f38f7a6d9",
            "order": 54,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 1510,
                        "y1": 10759,
                        "x2": 5430,
                        "y2": 11090,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 2,
                "hash": "13f4bac21f1367daf74354ebdb10a7eb03cb92b2"
            },
            "content": [
                "TABLE ⅢTHE EFFECTIVENESS OF USING SYNTHETIC DATA FOR RL."
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 7.5,
            "bold": False
        },
        {
            "id": "635ab467c1ed41f5a7d52a5b9d5d7b1d",
            "parent": "c0b33274971f4bb0998a729f38f7a6d9",
            "order": 55,
            "dst_type": "table",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 1229,
                        "y1": 11279,
                        "x2": 5709,
                        "y2": 12199,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 2,
                "hash": "b655c213a15bed3a3568a163f61ad95d1d861493"
            },
            "content": [
                "<table><tr><td rowspan=\"1\" colspan=\"1\">Task</td><td rowspan=\"1\" colspan=\"1\">Metrics</td><td rowspan=\"1\" colspan=\"1\">Datasize(#Tokens)</td><td rowspan=\"1\" colspan=\"1\">Performance Boost</td></tr><tr><td rowspan=\"1\" colspan=\"1\">MultiFin</td><td rowspan=\"1\" colspan=\"1\">F1</td><td rowspan=\"1\" colspan=\"1\">219(23k)</td><td rowspan=\"1\" colspan=\"1\">+87.1%</td></tr><tr><td rowspan=\"1\" colspan=\"1\">FOMC</td><td rowspan=\"1\" colspan=\"1\">F1</td><td rowspan=\"1\" colspan=\"1\">108(10k)</td><td rowspan=\"1\" colspan=\"1\">+22.5%</td></tr><tr><td rowspan=\"1\" colspan=\"1\">TSA</td><td rowspan=\"1\" colspan=\"1\">RMSE</td><td rowspan=\"1\" colspan=\"1\">105(10k)</td><td rowspan=\"1\" colspan=\"1\">+3.4%</td></tr></table>"
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": -1,
            "bold": False
        },
        {
            "id": "6a8da9d99e3f449992dc2360b45c405c",
            "parent": "c0b33274971f4bb0998a729f38f7a6d9",
            "order": 56,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 1019,
                        "y1": 12269,
                        "x2": 5909,
                        "y2": 13000,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 2,
                "hash": "041058d94c48cf7e1da61598740de41382b74585"
            },
            "content": [
                "Task abbreviations(description):MultiFin (Real-world Article Headlinesacross different writing systems and language families),FOMC (FederalOpen Market Committee Hawkish-Dovish Cassification),TSA(SentimentAnalysis on Social Media)."
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 10.0,
            "bold": False
        },
        {
            "id": "01b0835e5cd949dcbdd14cb59657d6b9",
            "parent": "c0b33274971f4bb0998a729f38f7a6d9",
            "order": 57,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 940,
                        "y1": 13220,
                        "x2": 6000,
                        "y2": 14349,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 2,
                "hash": "544164f91eaaeab0b2a93966d1a0cebc9a3fa24c"
            },
            "content": [
                "c)RL with Synthesed Data:Almost half of the tasksdo not provide training datasets,so we need to synthesizedata to improve the model's performance on these tasks.Thedata synthesis process consists of three steps:(1)CorpusCollection:Prompt the LLM to generate questions based on"
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 10.0,
            "bold": False
        },
        {
            "id": "f96ddce670654bab8b52fea6d33983fb",
            "parent": "c0b33274971f4bb0998a729f38f7a6d9",
            "order": 58,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 6209,
                        "y1": 5279,
                        "x2": 11259,
                        "y2": 6450,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 2,
                "hash": "98a60b2a5f782f4d88784754e8964c43425be316"
            },
            "content": [
                "the task;(2)LLM Annotation:Generate annotations usingChain-of-Thought(CoT)reasoning;(3)Answer Extraction:Use regular expressions to extract the answer y+from thegenerated response.We then apply the aforementioned methodfor fine-tuning."
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 10.0,
            "bold": False
        },
        {
            "id": "52345fae87f54319a81f3ed43149aee8",
            "parent": "c0b33274971f4bb0998a729f38f7a6d9",
            "order": 59,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 6209,
                        "y1": 6530,
                        "x2": 11259,
                        "y2": 7929,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 2,
                "hash": "2386776e5028e90b8655b328c552245431c932e6"
            },
            "content": [
                "Table四presents the results of training for one iterationusing RL on three different tasks.We used DeepSeek-R1-1.5Bto synthesize data,which was subsequently used to train thesame model.The SFT parameters remain consistent with thosepresented in Table!We observe performance improvementsacross all tasks."
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 10.0,
            "bold": False
        },
        {
            "id": "b211a0e6efb2444199579650bcf3362e",
            "parent": "c0b33274971f4bb0998a729f38f7a6d9",
            "order": 60,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 6200,
                        "y1": 8019,
                        "x2": 11259,
                        "y2": 10190,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 2,
                "hash": "af11a1d151086fbda52853631bf6583fc167ed22"
            },
            "content": [
                "d)Data Scaling Law:An essential property of largelanguage models is the presence of scaling laws,which allowus to predict the performance of larger-scale models based onthe results of a small number of smaller-scale experiments.To investigate the data scaling behavior on financial tasks,weaggregated all available data and conducted fine-tuning withvarying fractions of the dataset(100%,50%,25%,and 12.5%).The average F¹scores obtained from each setting are presentedin Figure2"
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 14.5,
            "bold": False
        },
        {
            "id": "37fdc3fbb8354ab6b9b4509ee9f8d60d",
            "parent": "c0b33274971f4bb0998a729f38f7a6d9",
            "order": 61,
            "dst_type": "image",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 6549,
                        "y1": 10570,
                        "x2": 11010,
                        "y2": 13819,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 2,
                "hash": "9e5e3d746af4c1a7f56b04f594d06d8c4e228eea"
            },
            "content": [
                "http://kna-common.ks3-cn-beijing.ksyuncs.com/insight-ai/doc-parser/e3ea76f321f3459ea056f9b9c7ad7864_2.png?Signature=Qn0ytDU7SecT%2BfHIOyp9N8dG%2BPs%3D&Expires=3243410128&KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb"
            ],
            "image_pixel": [
                447,
                324
            ],
            "mark": None,
            "font_size": -1,
            "bold": False
        },
        {
            "id": "072a72f8c8e1400eb01a57232e71f320",
            "parent": "c0b33274971f4bb0998a729f38f7a6d9",
            "order": 62,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 6449,
                        "y1": 12010,
                        "x2": 6579,
                        "y2": 12279,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 2,
                "hash": "caf3c3b730b1b64ee7c3e6703a6a9b063f9da3b4"
            },
            "content": [
                "1-F"
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 9.5,
            "bold": False
        },
        {
            "id": "705d5c20444f4e2493b77b77bc36cadd",
            "parent": "c0b33274971f4bb0998a729f38f7a6d9",
            "order": 63,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 8439,
                        "y1": 13809,
                        "x2": 9429,
                        "y2": 13920,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 2,
                "hash": "7b0fba5db4e1cb5615cb6b09e57c81b949060385"
            },
            "content": [
                "Data Fraction d"
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 5.5,
            "bold": False
        },
        {
            "id": "4b14185d252c4a34a70b495428e044ec",
            "parent": "c0b33274971f4bb0998a729f38f7a6d9",
            "order": 64,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 7179,
                        "y1": 14140,
                        "x2": 10299,
                        "y2": 14319,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 2,
                "hash": "41d64c6db1309e230181706507b8d7b683004b31"
            },
            "content": [
                "Fig.2.The data scaling law on financial tasks."
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 10.0,
            "bold": False
        },
        {
            "id": "94e064207b054716bd3f93764d26873a",
            "parent": "c0b33274971f4bb0998a729f38f7a6d9",
            "order": 65,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 949,
                        "y1": 1019,
                        "x2": 6000,
                        "y2": 3359,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 3,
                "hash": "e38bfa39fbe1f81f1ba149c18ca95d5d597951a9"
            },
            "content": [
                "It is worth noting that the data critical exponent we mea-sured is highly consistent with results reported in previousliterature.Specifically,14]reports the following scaling lawsL～p-0.076,L～d-0.095,where L denotes the test loss,pthe number of parameters,and d the dataset size.A relationof the form x～y corresponds to log(x)=αlog(y)+Cfor some constant C,and the exponent a is referred toas the critical exponent.On the other hand,[15l reportsE～p-0.195,where E is the test error rate.By combiningthese results,it is straightforward that"
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 8.0,
            "bold": True
        },
        {
            "id": "9be5e5fdd7cb4315aad464efd0da9f74",
            "parent": "c0b33274971f4bb0998a729f38f7a6d9",
            "order": 66,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 1770,
                        "y1": 3470,
                        "x2": 5990,
                        "y2": 3679,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 3,
                "hash": "c23654dc6ab6bb5dc51fad41170d7d6cee22271d"
            },
            "content": [
                "E～L⁰.195/0.076=2.57～d².57×0.095=0.24(1)"
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 8.0,
            "bold": False
        },
        {
            "id": "6c03284c61114e289401aed843b01bde",
            "parent": "c0b33274971f4bb0998a729f38f7a6d9",
            "order": 67,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 940,
                        "y1": 3860,
                        "x2": 5990,
                        "y2": 5729,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 3,
                "hash": "4fa0286db8fef65aab6d9a58e5eb26dfc752de0c"
            },
            "content": [
                "Our measured exponent of 0.28 is very close to this derivedvalue of 0.24,which not only supports the correctness of ourexperiments,but also suggests a broader implication:whilethe results in [4 are obtained on general text and those inI15]on math,our findings are based on financial tasks.Thisconsistency across domains implies that the critical exponentmay be independent of tasks;in other words,it exhibits a formof universality I16."
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 10.0,
            "bold": False
        },
        {
            "id": "e34f255599ab4c96a87ce9d5c7f81c9b",
            "parent": "c0b33274971f4bb0998a729f38f7a6d9",
            "order": 68,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 949,
                        "y1": 5770,
                        "x2": 6000,
                        "y2": 7140,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 3,
                "hash": "1cbada0f0f342a016a2f399854ac452c9e20b2ec"
            },
            "content": [
                "e)Computational Cost:The tasks require the use ofhigh-performance GPUs.Fine-tuning each 1.5B model re-quired approximately 3 to 5 hours on 4x2080Ti.Additionally,generating new training sets for specific datasets withoutpublicly available training sets took an additional 1 to 2 hoursper dataset."
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 8.0,
            "bold": False
        },
        {
            "id": "52ad360f75074f9c99197f8cd3004c5c",
            "parent": "37e9dcd06f584928a43a00241932374e",
            "order": 69,
            "dst_type": "text",
            "attributes": {
                "level": 2,
                "position": {
                    "bbox": {
                        "x1": 2130,
                        "y1": 7269,
                        "x2": 4839,
                        "y2": 7419,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 3,
                "hash": "ef3c68ff86ea21cb87e96680120bceb50688429c"
            },
            "content": [
                "V.DISCUSSION AND PROSPECT"
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 8.0,
            "bold": False
        },
        {
            "id": "06448abaffa64d74af6c8df56dbfae82",
            "parent": "52ad360f75074f9c99197f8cd3004c5c",
            "order": 70,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 940,
                        "y1": 7559,
                        "x2": 6000,
                        "y2": 11579,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 3,
                "hash": "82183bd431a5f6b5f8145ac1382cc17cee0ff111"
            },
            "content": [
                "a)Preprocessing and Postprocessing:In this competi-tion,we can only upload models and cannot make modifica-tions to preprocessing(prompt engineering)or postprocessing(generation configuration and parsing of output).However,in practice,preprocessing and postprocessing are often moreconvenient and can be as effective as fine-tuning in improvingmodel performance.For example,the issue of DeepSeekmodels receiving lower scores due to the output of chain-of-thought(CoT)reasoning can be addressed by adding moreexamples and instructions in the prompts or by using regularexpressions in postprocessing to remove the CoT process.Another example is the repeater phenomenon after modelfine-tuning,which is often mitigated by increasing the repeatpenalty or using top_p(nucleus sampling)instead of top_k.Webelieve that if the organizers grant us the flexibility to applypreprocessing and postprocessing,we can further enhance themodel's performance."
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 8.0,
            "bold": False
        },
        {
            "id": "08f315b2d3b249c9834a448f6ce2b79e",
            "parent": "52ad360f75074f9c99197f8cd3004c5c",
            "order": 71,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 949,
                        "y1": 11640,
                        "x2": 6000,
                        "y2": 12969,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 3,
                "hash": "1a4164e92288129db66cd1144a3016663a3b6357"
            },
            "content": [
                "b)Prospect:Due to time constraints,many potentiallybeneficial experiments were left unexplored:(1)Testing largermodels;(2)Exploring task-specific prompts to improve per-formance on reasoning-intensive tasks further;(3)Runningadditional iterations of RL training to examine whether per-formance saturates."
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 8.0,
            "bold": False
        },
        {
            "id": "134ab9cffca440db8021ff28dc989181",
            "parent": "37e9dcd06f584928a43a00241932374e",
            "order": 72,
            "dst_type": "text",
            "attributes": {
                "level": 2,
                "position": {
                    "bbox": {
                        "x1": 2610,
                        "y1": 13170,
                        "x2": 4360,
                        "y2": 13310,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 3,
                "hash": "e58bde99a8a210dcd5ffdffb5234c19218309c36"
            },
            "content": [
                "ACKNOWLEDGMENT"
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 8.0,
            "bold": False
        },
        {
            "id": "40dc8df3758d4bd981bb1e857fa352dd",
            "parent": "134ab9cffca440db8021ff28dc989181",
            "order": 73,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 1140,
                        "y1": 13439,
                        "x2": 5990,
                        "y2": 13649,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 3,
                "hash": "05e2e2a008089954a5566af2715b80754c926383"
            },
            "content": [
                "The authors were partially supported by the US Na-"
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 10.0,
            "bold": False
        },
        {
            "id": "85a8430ac22442d6bb9b4f6ed8057357",
            "parent": "134ab9cffca440db8021ff28dc989181",
            "order": 74,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 940,
                        "y1": 13680,
                        "x2": 5990,
                        "y2": 13840,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 3,
                "hash": "6bc64214c72c80e54a38e3bb980c4208f3b1192e"
            },
            "content": [
                "tional Science Foundation under awards DMS-2244988,"
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 8.0,
            "bold": False
        },
        {
            "id": "f226d97ef9c342248d1b9f88098f8193",
            "parent": "134ab9cffca440db8021ff28dc989181",
            "order": 75,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 940,
                        "y1": 13920,
                        "x2": 5979,
                        "y2": 14070,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 3,
                "hash": "ae4e95de4f8c06a00cd14dd1460fcdfa9c6b5e68"
            },
            "content": [
                "DMS2206333,the Office of Naval Research Award N00014-"
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 8.0,
            "bold": False
        },
        {
            "id": "1a75c290fd3145e9b6a518bb41fa0820",
            "parent": "134ab9cffca440db8021ff28dc989181",
            "order": 76,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 940,
                        "y1": 14160,
                        "x2": 4709,
                        "y2": 14310,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 3,
                "hash": "a51b4631e750918d19af6c93a643eabd01fa0b32"
            },
            "content": [
                "23-1-2007,and the DARPA D24AP00325-00."
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 8.0,
            "bold": False
        },
        {
            "id": "db216d9794664707987af6402db06f6f",
            "parent": "37e9dcd06f584928a43a00241932374e",
            "order": 77,
            "dst_type": "text",
            "attributes": {
                "level": 2,
                "position": {
                    "bbox": {
                        "x1": 8169,
                        "y1": 1019,
                        "x2": 9280,
                        "y2": 1169,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 3,
                "hash": "d4730753022e62db3135b00e0df88859d25187e8"
            },
            "content": [
                "REFERENCES"
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 8.0,
            "bold": False
        },
        {
            "id": "972a4da1b68a47b5b622850fc1df9e25",
            "parent": "db216d9794664707987af6402db06f6f",
            "order": 78,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 6290,
                        "y1": 1329,
                        "x2": 11259,
                        "y2": 1849,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 3,
                "hash": "23051ee159ff001be256d9bbbf6525bf9c32a954"
            },
            "content": [
                "[1]Q.Xie,W.Han,Z.Chen,R.Xiang,X.Zhang,Y.He,M.Xiao,D.Li,Y.Dai,D.Feng,Y.Xu,H.Kang,Z.Kuang,C.Yuan,K.Yang,Z.Luo,T.Zhang,Z.Liu,G.Xiong,Z.Deng,Y.Jiang,Z.Yao,H.Li,Y.Yu,"
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 8.0,
            "bold": False
        },
        {
            "id": "c7a2087745b44c3b949f37eea8afd24b",
            "parent": "db216d9794664707987af6402db06f6f",
            "order": 79,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 6590,
                        "y1": 1880,
                        "x2": 11259,
                        "y2": 2040,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 3,
                "hash": "f420daa08e372ac6a5c71169a28931dce2622b86"
            },
            "content": [
                "G.Hu,JHuang,X.-YLiu,A.Lopez-LraB.Wang,Y.Lai,H.Wang,"
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 8.0,
            "bold": False
        },
        {
            "id": "0e4c015ac2e747f79f1cfc191920e429",
            "parent": "db216d9794664707987af6402db06f6f",
            "order": 80,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 6300,
                        "y1": 2059,
                        "x2": 11259,
                        "y2": 3659,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 3,
                "hash": "00b9f7776c4f8ebcc8d7f76955b634c23dd63694"
            },
            "content": [
                "M.Peng,S.Ananiadou,and J.Huang,\"Finben:A holistic financialbenchmark for large language models,\"2024.[Online].Available:https://arxiv.org/abs/2402.12659[2]Z.Dong,X.Fan,and Z.Peng,\"Fnspid:A comprehensivefinancial news dataset in time series,”in Proceedings of the 30thACM SIGKDD Conference on Knowledge Discovery and DataMining,ser.KDD '24.  New York,NY,USA:Association forComputing Machinery,2024,p.4918-4927.[Online].Available:https://doi.org/10.1145/3637528.3671629"
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 8.0,
            "bold": False
        },
        {
            "id": "a73ec27c2f004271934ce8ce22bf57d5",
            "parent": "db216d9794664707987af6402db06f6f",
            "order": 81,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 6279,
                        "y1": 3689,
                        "x2": 11259,
                        "y2": 5639,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 3,
                "hash": "d090c8bb426551f41ae345d84c58d2ff59cbfee5"
            },
            "content": [
                "[3]X.-Y.Liu,Z.Xia,J.Rui,J.Gao,H.Yang,M.Zhu,C.D.Wang,Z.Wang,and J.Guo,\"Finrl-meta:Market environments andbenchmarks for data-driven financial reinforcement learning,”2022.[Online].Available:https://arxiv.org/abs/2211.03107[4]X.-Y.Liu,Z.Xia,H.Yang,J.Gao,D.Zha,M.Zhu,C.D.Wang,Z.Wang,and J.Guo,\"Dynamic datasets and market environmentsfor financial reinforcement learning,”2023.[Online].Available:https://arxiv.org/abs/2304.13174[5]M.Benhenda,\"Finrl-deepseek:Llm-infused risk-sensitive reinforcementlearning for trading agents,”\"2025.[Online].Available:https:/arxiv.org/abs/2502.07393"
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 8.0,
            "bold": True
        },
        {
            "id": "a60a3bda7e914930acd68b15284f9684",
            "parent": "db216d9794664707987af6402db06f6f",
            "order": 82,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 6300,
                        "y1": 5649,
                        "x2": 11259,
                        "y2": 5799,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 3,
                "hash": "958fa760a1ca64ba3f3fe45fa2410d32e7b7d308"
            },
            "content": [
                "[6]K.Wang,J.Patel,C.Shen,D.Kim,A.Zhu,A.Lin,L.Borella,"
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 8.0,
            "bold": False
        },
        {
            "id": "1cf183f5cc2541bc968a07b8769290da",
            "parent": "db216d9794664707987af6402db06f6f",
            "order": 83,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 6290,
                        "y1": 5819,
                        "x2": 11259,
                        "y2": 9369,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 3,
                "hash": "f311c4250df92b8c9b8e0481e4d78756fdd8d8ec"
            },
            "content": [
                "C.Osborne,M.White,S.Yang,K.Xiao,and X.-Y.L.Yanglet,“A report on financial regulations challenge at coling 2025,”2025.[Online].Available:https://arxiv.org/abs/2412.11159[7]P.Chantangphol,P.Balee,K.Sucharitpongpan,C.Saetia,andT.Chalothorn,\"FinMind-Y-me at the regulations challenge task:Financial mind your meaning based on THaLLE,”in Proceedingsof the Joint Workshop of the 9th Financial Technology and NaturalLanguage Processing(FinNLP),the 6th Financial Narrative Processing(FNP),and the Ist Workshop on Large Language Models for Financeand Legal(LLMFinLegal),C.-C.Chen,A.Moreno-Sandoval,J.Huang,Q.Xie,S.Ananiadou,and H.-H.Chen,Eds.Abu Dhabi,UAE:Association for Computational Linguistics,Jan.2025,pp.349-362.[Online].Available:https://aclanthology.org/2025.finnlp-1.41/[8]S.Gao,Y.Wen,M.Zhu,J.Wei,Y.Cheng,Q.Zhang,and S.Shang,\"Simulating financial market via large language model based agents,\"2024.[Online].Available:https://arxiv.org/abs/2406.19966[9]N.Li,C.Gao,M.Li,Y.Li,and Q.Liao,\"EconAgent:Large languagemodel-empowered agents for simulating macroeconomic activities,\"in Proceedings of the 62nd Annual Meeting of the Associationfor Computational Linguistics(Volume 1:Long Papers),L.-W.Ku,"
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 8.0,
            "bold": False
        },
        {
            "id": "f03fe9a8d2b9469084799ab9d1c64000",
            "parent": "db216d9794664707987af6402db06f6f",
            "order": 84,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 6579,
                        "y1": 9399,
                        "x2": 11259,
                        "y2": 9909,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 3,
                "hash": "93158f605956eb61071d8f729329e1a801fe92ec"
            },
            "content": [
                "A.Martins,and V.Srikumar,Eds.Bangkok,Thailand:Associationfor Computational Linguistics,Aug.2024,pp.15523-15536.[Online].Available:https://aclanthology.org/2024.acl-long.829/"
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 8.0,
            "bold": False
        },
        {
            "id": "180e1e24301141c3a768cf2c78122af6",
            "parent": "db216d9794664707987af6402db06f6f",
            "order": 85,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 6209,
                        "y1": 9940,
                        "x2": 11259,
                        "y2": 12790,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 3,
                "hash": "c18146afe9d42faa479fff949d1286ff3e744b67"
            },
            "content": [
                "[10]A.Holtzman,J.Buys,L.Du,M.Forbes,and Y.Choi,“Thecurious case of neural text degeneration,”2020.[Online].Available:https://arxiv.org/abs/1904.09751[11]Y.Ren and D.J.Sutherland,“Learning dynamics of llm finetuning,”2025.[Online].Available:https://arxiv.org/abs/2407.10490[12]Y.Zheng,R.Zhang,J.Zhang,Y.Ye,Z.Luo,Z.Feng,and YMa,\"Llamafactory:Unified efficient fine-tuning of 100+language models,\"2024.[Online].Available:https://arxiv.org/abs/2403.13372[13]S.A.Jacobs,M.Tanaka,C.Zhang,M.Zhang,S.L.Song,S.Rajbhandari,and Y.He,\"Deepspeed ulysses:System optimizationsfor enabling training of extreme long sequence transformer modes,”2023.[Online].Available:https://arxiv.org/abs/2309.14509[14]J.Kaplan,S.McCandlish,T.Henighan,T.B.Brown,B.Chess,R.Child,S.Gray,A.Radford,J.Wu,and D.Amodei,\"Scalinglaws for neural language models,”2020[Online].Available:https://arxiv.org/abs/2001.08361"
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 8.0,
            "bold": False
        },
        {
            "id": "b682144ee5634b31b9b0a37f7461bb95",
            "parent": "db216d9794664707987af6402db06f6f",
            "order": 86,
            "dst_type": "text",
            "attributes": {
                "level": 10,
                "position": {
                    "bbox": {
                        "x1": 6220,
                        "y1": 12809,
                        "x2": 11269,
                        "y2": 14039,
                        "rotate": None
                    },
                    "block_coordinate": None
                },
                "page": 3,
                "hash": "9895632bf4444a69df08dfa3c3e214fab6a05cc4"
            },
            "content": [
                "[15]Y.Wu,Z.Sun,S.Li,S.Welleck,and Y.Yang,\"Inferencescaling laws:An empirical analysis of compute-optimal inference forproblem-solving with language models,\"2024.[Online].Available:https://arxiv.org/abs/2408.00724[16]Y.Sun and B.Haghighat,“Phase transitions in large languagemodels and the o(n)model,”2025.[Online].Available:https:/arxiv.org/abs/2501.16241"
            ],
            "image_pixel": None,
            "mark": None,
            "font_size": 8.0,
            "bold": False
        }
    ]


from collections import defaultdict
from typing import List





class TestPdfParse(TestCase):

    def setUp(self):
        self.parser = PdfParse()
        self.dsts = kdc_pdf_data_3()
        self.contxt = PipelineContext()

    def tearDown(self):
        pass

    def test_dst_generate(self):
        # self.contxt.file_info=FileInfo()
        dst = convert_to_dst_list(self.dsts)
        page_map = get_page_dst(dst)
        layout = determine_layout_with_middle_line(page_map)
        print("layout:", layout)
        # if layout == "left-right":
        #     dst = reorder_dst_by_page(page_map)
        # print(print_dst_indent_tree(dst))
