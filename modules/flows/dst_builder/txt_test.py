# coding: utf-8
# <AUTHOR> linqi
# @date    : 2025/4/25 10:14

import json

import unittest
from modules.flows.dst_builder.txt import TXTParse
from commons.db.storedao import StoreDao, StoreType
from modules.entity.dst_entity import print_dst_tree
import os

from modules.pipeline.context import PipelineContext


class TXTParseTest(unittest.TestCase):
    def setUp(self):
        StoreDao().init(
            host="ks3-cn-beijing.ksyun.com",
            ak=os.environ.get("ks3_common_ak", ""),
            sk=os.environ.get("ks3_common_sk", ""),
            bucket="kna-common", store_type=StoreType.Ks3
        )
        self.parser = TXTParse()
        self.kdc_url = "http://kna-common.ks3-cn-beijing.ksyuncs.com/zdq1?Signature=XuNPYpHUBnhIcUqe2vFKEvkBP%2BI%3D&Expires=16666213115&KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb"
        self.contxt = PipelineContext()
    def tearDown(self):
        pass

    def test_parse_pdf(self):
        kdc_data = self.parser.data_process(kdc_ks3_url=self.kdc_url)
        res = self.parser.dst_generate(self.contxt,kdc_data)

        json_list = [item.model_dump() for item in res]
        print(json.dumps(json_list, indent=2, ensure_ascii=False))
        print("====================================================")
        print_dst_tree(res)
