from unittest import TestCase

from modules.entity.dst_entity import print_dst_tree, print_dst_indent_tree
from modules.flows.chunk.test_docx import convert_to_dst_list
from modules.flows.dst_builder.pdf import PdfParse
from modules.pipeline.context import PipelineContext


def kdc_pdf_data_3():
    return [
    {
        "id": "fb9cead2881e4ca782a801f3a90435d5",
        "parent": "-1",
        "order": 0,
        "dst_type": "root",
        "attributes": {
            "level": 0,
            "position": {
                "bbox": {
                    "x1": 1,
                    "y1": 2,
                    "x2": 3,
                    "y2": 4,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 0,
            "hash": "roothashhashhashhashhashhashhashhashhashhash"
        },
        "content": [
            "根节点"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": -1,
        "bold": False
    },
    {
        "id": "05c95ebc1e2747cca6e3131ab79a9004",
        "parent": "fb9cead2881e4ca782a801f3a90435d5",
        "order": 1,
        "dst_type": "text",
        "attributes": {
            "level": 1,
            "position": {
                "bbox": {
                    "x1": 1149,
                    "y1": 2130,
                    "x2": 11059,
                    "y2": 2389,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 0,
            "hash": "3d79dfd3fc327679907d68f74d21e0a658c35a65"
        },
        "content": [
            "STYLEMOTIF:Multi-Modal Motion Stylization using Style-Content Cross Fusion"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 13.0,
        "bold": True
    },
    {
        "id": "7a1cd36c259a401b87cd8f821c7748a5",
        "parent": "05c95ebc1e2747cca6e3131ab79a9004",
        "order": 2,
        "dst_type": "image",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6239,
                    "y1": 5780,
                    "x2": 11110,
                    "y2": 9330,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 0,
            "hash": "665b006f06adc8ca3021a75a99a500f1c60264a5"
        },
        "content": [
            "http://kna-common.ks3-cn-beijing.ksyuncs.com/insight-ai/doc-parser/07815f42ac0a4a40a80859c14d31c6ea_0.png?Signature=yGU8GKkbF8G69AB7ChqpGH2T1IE%3D&Expires=3243326895&KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb"
        ],
        "image_pixel": [
            488,
            355
        ],
        "mark": None,
        "font_size": -1,
        "bold": False
    },
    {
        "id": "eaba20f03c6945d48ae6f8f84259b082",
        "parent": "05c95ebc1e2747cca6e3131ab79a9004",
        "order": 3,
        "dst_type": "image",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6239,
                    "y1": 5780,
                    "x2": 11110,
                    "y2": 9330,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 0,
            "hash": "970e009c54ed8d05989b80c229133a9cb590678e"
        },
        "content": [
            "http://kna-common.ks3-cn-beijing.ksyuncs.com/insight-ai/doc-parser/d06a319108c749c797ba9092ddc905e4_1.png?Signature=e5PeiKhchP6AHajWfgJM5Au5PBQ%3D&Expires=3243326894&KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb"
        ],
        "image_pixel": [
            488,
            355
        ],
        "mark": None,
        "font_size": -1,
        "bold": False
    },
    {
        "id": "f992c59a81b34d908143c489aa662d49",
        "parent": "05c95ebc1e2747cca6e3131ab79a9004",
        "order": 4,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 2149,
                    "y1": 2909,
                    "x2": 3130,
                    "y2": 3139,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 0,
            "hash": "54826b97e1fa9f236ca2259f1f59e21f20dc9189"
        },
        "content": [
            "Ziyu Guo"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 10.5,
        "bold": False
    },
    {
        "id": "33307a444af247908df9f7cd0d26b8e5",
        "parent": "05c95ebc1e2747cca6e3131ab79a9004",
        "order": 5,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 4270,
                    "y1": 2909,
                    "x2": 10599,
                    "y2": 3139,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 0,
            "hash": "4e9add5629272dc0e98e8bd92413deff72bc9915"
        },
        "content": [
            "Young Yoon LeeJoseph LiuYizhak Ben-Shabat"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.0,
        "bold": False
    },
    {
        "id": "340a9459214d4bb68dd671900afb904e",
        "parent": "05c95ebc1e2747cca6e3131ab79a9004",
        "order": 6,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1689,
                    "y1": 3219,
                    "x2": 10039,
                    "y2": 3400,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 0,
            "hash": "399ffd3bdeb2c1d5af62d923da01fd10fa336621"
        },
        "content": [
            "CUHK,MiuLar LabRobloxRobloxRoblox"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.0,
        "bold": False
    },
    {
        "id": "16d0f1d0182242138bf35007391c38b6",
        "parent": "05c95ebc1e2747cca6e3131ab79a9004",
        "order": 7,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1589,
                    "y1": 3539,
                    "x2": 10620,
                    "y2": 3720,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 0,
            "hash": "dbf479e6bed9e3f2847faaafa1d46dfe668bd15d"
        },
        "content": [
            "<EMAIL>@<EMAIL>@roblox.com"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.0,
        "bold": False
    },
    {
        "id": "3fa6ae366a804b6ebce9055c8b9c36ee",
        "parent": "05c95ebc1e2747cca6e3131ab79a9004",
        "order": 8,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 4079,
                    "y1": 3909,
                    "x2": 5430,
                    "y2": 4090,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 0,
            "hash": "3dd4a4f2eb980fe5c3fcccd8071b39d6133bf94a"
        },
        "content": [
            "Victor Zordan"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.0,
        "bold": False
    },
    {
        "id": "c89ce80ca5894a708b7b0f42e59b6bb3",
        "parent": "05c95ebc1e2747cca6e3131ab79a9004",
        "order": 9,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6530,
                    "y1": 3899,
                    "x2": 8309,
                    "y2": 4129,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 0,
            "hash": "85ecdbcce8a6b76614251126fbe6aac85d8d4aa9"
        },
        "content": [
            "Mubbasir Kapadia"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 10.5,
        "bold": False
    },
    {
        "id": "7bd1b096f5a34429aeab4d1c64515dc1",
        "parent": "05c95ebc1e2747cca6e3131ab79a9004",
        "order": 10,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 329,
                    "y1": 4170,
                    "x2": 700,
                    "y2": 11160,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 0,
            "hash": "68b7249b2c9d38baa2271a1a527db9ca6a6eddd8"
        },
        "content": [
            "arXiv:2503.21775vl [cs.CV] 27 Mar 2025"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 25.5,
        "bold": False
    },
    {
        "id": "e33e4b3a668042cfab47bedad3a658bb",
        "parent": "05c95ebc1e2747cca6e3131ab79a9004",
        "order": 11,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 4400,
                    "y1": 4219,
                    "x2": 5109,
                    "y2": 4390,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 0,
            "hash": "51bb451ecc30e1f5f4aa3cdb568a97fd7afb668e"
        },
        "content": [
            "Roblox"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.0,
        "bold": False
    },
    {
        "id": "f1ec63c72693409e91f3b286083fee7f",
        "parent": "05c95ebc1e2747cca6e3131ab79a9004",
        "order": 12,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 7060,
                    "y1": 4219,
                    "x2": 7779,
                    "y2": 4400,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 0,
            "hash": "51bb451ecc30e1f5f4aa3cdb568a97fd7afb668e"
        },
        "content": [
            "Roblox"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.0,
        "bold": False
    },
    {
        "id": "dcd35b8b21c84e7195a663cae2d1a06a",
        "parent": "05c95ebc1e2747cca6e3131ab79a9004",
        "order": 13,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 3909,
                    "y1": 4529,
                    "x2": 8630,
                    "y2": 5129,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 0,
            "hash": "66de62a97184dc7e811bfc8f8cb1e1fd3905d1b0"
        },
        "content": [
            "<EMAIL>@roblox.comProject Page:https://stylemotif.github.io"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 10.5,
        "bold": False
    },
    {
        "id": "d6ea5c65c131492b8c1d44b649374a73",
        "parent": "05c95ebc1e2747cca6e3131ab79a9004",
        "order": 14,
        "dst_type": "text",
        "attributes": {
            "level": 2,
            "position": {
                "bbox": {
                    "x1": 3060,
                    "y1": 5879,
                    "x2": 3970,
                    "y2": 6049,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 0,
            "hash": "d79da395b5d7a09c6439a5f2660d8c568186ab7e"
        },
        "content": [
            "Abstract"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.0,
        "bold": True
    },
    {
        "id": "a34011419d594bda8fa258b53f433d2f",
        "parent": "d6ea5c65c131492b8c1d44b649374a73",
        "order": 15,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1129,
                    "y1": 6380,
                    "x2": 5919,
                    "y2": 10400,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 0,
            "hash": "f28ee056c7940949b81385c0aef75fff91c3dd5c"
        },
        "content": [
            "We present STYLEMOTIF,a novel Stylized Motion LatentDiffusion model,generating motion conditioned on bothcontent and style from multiple modalities.Unlike existingapproaches that either focus on generating diverse motioncontent or transferring style from sequences,STYLEMOTIFseamlessly synthesizes motion across awide range of contentwhile incorporating stylistic cues from multi-modal inputs,including motion,text,image,video,and audio.To achievethis,we introduce a style-content cross fusion mechanismand align a style encoder with a pre-trained multi-modalmodel,ensuring that the generated motion accurately cap-tures the reference style while preserving realism.Extensiveexperiments demonstrate that our framework surpasses ex-isting methods in stylized motion generation and exhibitsemergent capabilities for multi-modal motion stylizationen-abling more nuanced motion synthesis.Source code andpre-trained models will be released upon acceptance."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.0,
        "bold": True
    },
    {
        "id": "453c868389404b77a05c55db57ae2b54",
        "parent": "d6ea5c65c131492b8c1d44b649374a73",
        "order": 16,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6309,
                    "y1": 9500,
                    "x2": 11069,
                    "y2": 10560,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 0,
            "hash": "e98ec976d4ad2b1163413a67b3bc910c3c4473eb"
        },
        "content": [
            "Figure 1.Comparison of Our Proposed STYLEMOTIF Frame-work with SMooDi.Unlike SMooDi's dual-branch design,whichincreases model complexity and training overhead,STYLEMoTIFemploys a streamlined single-branch structure,enabling efficientmulti-modal motion stylization while preserving motion realism."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.0,
        "bold": False
    },
    {
        "id": "d882431fdf5149d89eb8ff21cb07ef14",
        "parent": "05c95ebc1e2747cca6e3131ab79a9004",
        "order": 17,
        "dst_type": "text",
        "attributes": {
            "level": 2,
            "position": {
                "bbox": {
                    "x1": 1149,
                    "y1": 11119,
                    "x2": 2689,
                    "y2": 11310,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 0,
            "hash": "4d03f3d0087de6de04ff967a7aa714c1f47daf1a"
        },
        "content": [
            "1.Introduction"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.0,
        "bold": True
    },
    {
        "id": "f79211081955427088ea600e364dc1ad",
        "parent": "d882431fdf5149d89eb8ff21cb07ef14",
        "order": 18,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6309,
                    "y1": 11129,
                    "x2": 11090,
                    "y2": 12039,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 0,
            "hash": "862477b6405e7c1d4cec5adcd838934ceda672dc"
        },
        "content": [
            "duction,and virtual reality.However,traditional approachesto stylized motion generation often depend on manual pro-cesses such as motion capture or keyframe animation,whichare costly,time-consuming,and labor-intensive."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.0,
        "bold": False
    },
    {
        "id": "dd87e38d03e44c8fa91c019a26d88b2c",
        "parent": "d882431fdf5149d89eb8ff21cb07ef14",
        "order": 19,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1140,
                    "y1": 11529,
                    "x2": 5930,
                    "y2": 13879,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 0,
            "hash": "47054fbad64255f4c8a5f3d771b83946c07c095d"
        },
        "content": [
            "Human motion generation is a fundamental task in computergraphics and animation,enabling the synthesis of realisticand expressive human movements.Broadly,human motioncan be characterized by two complementary aspects:content,which defines the underlying action (e.g.,walking,jumping),and style,which encodes variations such as personal flair,emotional expression,or cultural influences(e.g.,jubilant,aggressive).This separation allows for greater control andflexibility in generating motion,making it particularly valu-able in creative industries like game development,film pro-"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.0,
        "bold": False
    },
    {
        "id": "b068234429b743de8d06eaca2f6f478a",
        "parent": "d882431fdf5149d89eb8ff21cb07ef14",
        "order": 20,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6309,
                    "y1": 12139,
                    "x2": 11099,
                    "y2": 14240,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 0,
            "hash": "fdb1c632e11eec006cf1428fda1bdda103e72ffd"
        },
        "content": [
            "Recent progress in text-to-motion (T2M)diffusion frame-works [6,26,52]has greatly advanced the ability to translatenatural-language prompts into realistic human motions.Byleveraging powerful denoising diffusion models,these ap-proaches capture intricate spatiotemporal dependencies inthe data,enabling coherent sequences of human movementsto be generated directly from brief textual descriptions.De-spite their success in content fidelity and diversity,mostcurrent T2M diffusion methods concentrate primarily on"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.0,
        "bold": False
    },
    {
        "id": "250712bbd89048588cb4e659de6c0888",
        "parent": "d882431fdf5149d89eb8ff21cb07ef14",
        "order": 21,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1429,
                    "y1": 14039,
                    "x2": 3730,
                    "y2": 14199,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 0,
            "hash": "50b6e4664df261033844056b4c405d9586ac538b"
        },
        "content": [
            "t Work done as an intern at Roblox."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.0,
        "bold": False
    },
    {
        "id": "d85d704a29c646ea98c580a0f15b2e04",
        "parent": "d882431fdf5149d89eb8ff21cb07ef14",
        "order": 22,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6060,
                    "y1": 14670,
                    "x2": 6149,
                    "y2": 14809,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 0,
            "hash": "a26028c0193e643abb7f25b450d13a4883066ed4"
        },
        "content": [
            "工"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 5.5,
        "bold": False
    },
    {
        "id": "36dfe4be3d4c492fa8ece2fe936dbdfa",
        "parent": "d882431fdf5149d89eb8ff21cb07ef14",
        "order": 23,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1149,
                    "y1": 1479,
                    "x2": 5909,
                    "y2": 2640,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 1,
            "hash": "36490914beb2db4ddbf12b85010b5fc53e595b46"
        },
        "content": [
            "what action is performed,while overlooking how it is per-formed,namely,its stylistic details.Simply appending aseparate style-transfer module to text-driven motion diffu-sion pipelines can introduce additional complexity and riskof compounding errors in the final output."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.5,
        "bold": False
    },
    {
        "id": "84cdd188edb14760b58b9017fa2c123b",
        "parent": "d882431fdf5149d89eb8ff21cb07ef14",
        "order": 24,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1140,
                    "y1": 2710,
                    "x2": 5919,
                    "y2": 5300,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 1,
            "hash": "f931cd510ddc388aeb6cdfcc48a472a544c1aed5"
        },
        "content": [
            "In parallel,motion style transfer has been actively studiedto infuse stylistic cues from a reference motion(or styledata)into another sequence [1,23,35,51].Although manyof these methods effectively disentangle content and stylefor small-scale tasks,the pipeline becomes cumbersomewhen a large variety of content motions need to be styl-ized.Moreover,they commonly assume that the input ortarget sequences are high-quality motion data.In scenarioswhere content motions are synthetically generated,or par-tially noisy,the transfer process can deteriorate,leading toundesirable motion artifacts or compromised style fidelity."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.5,
        "bold": False
    },
    {
        "id": "ec58009194e3478b9bfa0c93e755f00a",
        "parent": "d882431fdf5149d89eb8ff21cb07ef14",
        "order": 25,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1149,
                    "y1": 5370,
                    "x2": 5930,
                    "y2": 9369,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 1,
            "hash": "1f0a9ea5a9796388205e6047b330ba1cc418a34a"
        },
        "content": [
            "To address the need for simultaneously controlling bothcontent and style,some recent works have merged styleencoding with diffusion-based motion generation.Amongthese,the most recent and representative approach [71]aug-ments a pre-trained latent diffusion model [6]with a styleadaptor and classifier-based style guidance,achieving styl-ized motion from textual prompts and motion-style refer-ences.While effective,this method relies on additionaltraining branches,which shares structural similarities withControlNet[64]as shown in Figure 1,which increases modelcomplexity and training overhead.It is also constrained tomotion as the primary style input.Concurrent work [31]proposes a bidirectional control flow mechanism to mitigateconflicts between style and content,extending style controlto multiple modalities.However,this approach also adopts adual-branch design with substantial training overhead andlimited applicability across modalities."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.5,
        "bold": False
    },
    {
        "id": "492c2227e5714ac9a24f3be1a440a5cb",
        "parent": "d882431fdf5149d89eb8ff21cb07ef14",
        "order": 26,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1129,
                    "y1": 9469,
                    "x2": 5919,
                    "y2": 13460,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 1,
            "hash": "61af2c7c3fe43680fa77514f9a0cf16cc15c281b"
        },
        "content": [
            "To this end,we propose STYLEMoTIF,anew frameworkfor multi-modalmotion stylization that unifies text-to-motiondiffusion with style conditioning in a single-branch struc-ture.Specifically,we leverage a pre-trained motion latentdiffusion model(MLD)[6]to preserve strong content gen-eration capabilities,and seamlessly integrate style featuresextracted from a dedicated encoder,which is aligned with amulti-modal foundation model[12].In contrast to previousworks,STYLEMOTIF avoids duplicating large portions ofthe network or relying on specialized style branches.Instead,we introduce a style-content cross fusion mechanism,whichinjects stylistic cues into the diffusion process while main-taining motion realism.As a result,our STYLEMOTIF notonly yields more robust stylized outputs but also supportsdiverse style signals,such as motion,text,images,audio,orvideo clips,via the alignment in multi-modal feature space.We summarize our main contributions as follows:"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.5,
        "bold": True
    },
    {
        "id": "7858e12aec3c4f3fa54cc56f82f0f912",
        "parent": "d882431fdf5149d89eb8ff21cb07ef14",
        "order": 27,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1159,
                    "y1": 13569,
                    "x2": 5930,
                    "y2": 14259,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 1,
            "hash": "9546239975309022f20e9b96ddad68be8a051a63"
        },
        "content": [
            "·We present STYLEMOTIF,a stylized latent motion dif-fusion framework that unifies diverse motion content andmulti-modal styles within a compact single-branch design."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.5,
        "bold": True
    },
    {
        "id": "36f2aec705a9482181dfdf7b55f57a6f",
        "parent": "d882431fdf5149d89eb8ff21cb07ef14",
        "order": 28,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6320,
                    "y1": 1479,
                    "x2": 11090,
                    "y2": 2379,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 1,
            "hash": "990745173fd3e36a24f689a4e10be1c1cd39a541"
        },
        "content": [
            "·We propose a style-content cross fusion module that in-jects stylistic cues into the diffusion denoising process,achieving faithful stylization without compromising mo-tion realism and ensuring efficiency."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.5,
        "bold": False
    },
    {
        "id": "171b35201e8b47909574f791d4d77d29",
        "parent": "d882431fdf5149d89eb8ff21cb07ef14",
        "order": 29,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6320,
                    "y1": 2439,
                    "x2": 11039,
                    "y2": 3579,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 1,
            "hash": "769080130c22eeded3f25fab24e20e623ab6c44b"
        },
        "content": [
            "·We achieve a unified multi-modal style feature space andunveil new emergent capabilities through multi-modalalignment,which accommodate various sources includingmotion,text,images,audio,and video,for flexible andversatile multi-modal style control."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.5,
        "bold": False
    },
    {
        "id": "88aa29b5c9824d598c7213c76905bd26",
        "parent": "d882431fdf5149d89eb8ff21cb07ef14",
        "order": 30,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6320,
                    "y1": 3630,
                    "x2": 11039,
                    "y2": 4299,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 1,
            "hash": "f87a47a343c664450b29f13f124c0d6208de83f1"
        },
        "content": [
            "·Extensive experiments demonstrate that STYLEMOTIFconsistently outperforms existing methods regarding styleexpressiveness,content preservation,and efficiency."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.5,
        "bold": False
    },
    {
        "id": "7757bfcd20304c40ae5f908f7ecb56a3",
        "parent": "05c95ebc1e2747cca6e3131ab79a9004",
        "order": 31,
        "dst_type": "text",
        "attributes": {
            "level": 2,
            "position": {
                "bbox": {
                    "x1": 6330,
                    "y1": 4529,
                    "x2": 7999,
                    "y2": 4700,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 1,
            "hash": "c44013a4f4283f6447fc6f1a2b36a30b0b209783"
        },
        "content": [
            "2.Related Work"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.5,
        "bold": True
    },
    {
        "id": "e747abc865e342daaf47dc80082ea97a",
        "parent": "7757bfcd20304c40ae5f908f7ecb56a3",
        "order": 32,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6309,
                    "y1": 4920,
                    "x2": 11099,
                    "y2": 10389,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 1,
            "hash": "bd31afcb56dc9e9b4e53531b3174d743462b6b8f"
        },
        "content": [
            "Human Motion Generation.Recent progress in humanmotion generation [3,5,8,13,14,39-41,44,53,55,56,60]has been driven by transformer [14,32]and diffusion mod-els [2,8,47,64],showing great potential in producing re-alistic and diverse motions.These approaches have showngreat potential in producing realistic and diverse motionsequences.For example,Momask [14]improves motiongeneration using a residual VQ-VAE.Similarly,LaMP[32]introduces a motion-aware text encoder and a motion-to-textlanguage model to enhance motion quality through text con-ditioning.Diffusion models,in particular,have become a keyapproach in motion generation [6,22,26,46,52,59,63,65].MDM[52]introduces a motion diffusion model that oper-ates directly on raw motion data to capture the relationshipbetween motions and input text conditions.MLD [6]im-prove efficiency by embedding the diffusion process in latentspace,reducing computational cost.They also allow condi-tioning on specific constraints,such as predefined trajecto-ries [26,53,57]or human-object interactions [7,38,55],en-abling greater control and diversity.Our work leverages pre-trained motion latent diffusion model [6]and multi-modalfoundation models [12]to achieve stylized human motiongeneration while maintaining high motion quality."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.5,
        "bold": False
    },
    {
        "id": "b18ca93ff100406795c01e34db5edfea",
        "parent": "7757bfcd20304c40ae5f908f7ecb56a3",
        "order": 33,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6320,
                    "y1": 10700,
                    "x2": 11090,
                    "y2": 10890,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 1,
            "hash": "d63707efce80a89c02667827e1620245c410c873"
        },
        "content": [
            "Motion Stylization.Motion stylization [1,23,27,35,37,"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.5,
        "bold": False
    },
    {
        "id": "adcccb228a824089ab5a6d75b68233c9",
        "parent": "7757bfcd20304c40ae5f908f7ecb56a3",
        "order": 34,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6320,
                    "y1": 10940,
                    "x2": 11099,
                    "y2": 14240,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 1,
            "hash": "38e71f440959a302293b092daeabc5d75c1c49da"
        },
        "content": [
            "42,43,48,49,51,54,58]involves transferring stylistic fea-tures from a reference motion to a source motion/textualprompt,enabling creative transformations while preservingthe original motion content.Early methods,like those inmotion style transfer[1,23],typically separate motion con-tent and style for recombination.For instance,Abermanet al.[1]used a generative adversarial network to decouplestyle from content without paired data,while Motion Puz-zle [23]allows style control for individual body parts,andGuo et al.[15]utilized pretrained motion models for betterstyle integration.Recent approaches,like SMooDi[71],gen-erate stylized motion from text and style sequences usingstyle guidance.However,these models face two main chal-lenges:(1)parallel-inefficient dual-branch frameworks,and"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.5,
        "bold": False
    },
    {
        "id": "e6c007fc8da24787b2fe72999a9ff775",
        "parent": "7757bfcd20304c40ae5f908f7ecb56a3",
        "order": 35,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6030,
                    "y1": 14639,
                    "x2": 6139,
                    "y2": 14809,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 1,
            "hash": "da4b9237bacccdf19c0760cab7aec4a8359010b0"
        },
        "content": [
            "2"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 7.0,
        "bold": False
    },
    {
        "id": "847313d8417f47a6874477904eb348b6",
        "parent": "7757bfcd20304c40ae5f908f7ecb56a3",
        "order": 36,
        "dst_type": "image",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1129,
                    "y1": 1370,
                    "x2": 11080,
                    "y2": 6169,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 2,
            "hash": "b47c780627194848fc30701c4925110111563885"
        },
        "content": [
            "http://kna-common.ks3-cn-beijing.ksyuncs.com/insight-ai/doc-parser/8f32b40c7a904fcf8f69d63b5d8811fc_2.png?Signature=jRpWH40PksngiQweRiomEh9ksL0%3D&Expires=3243326895&KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb"
        ],
        "image_pixel": [
            996,
            479
        ],
        "mark": None,
        "font_size": -1,
        "bold": False
    },
    {
        "id": "4f1a080bb5d34c32a5c724a0b125b30c",
        "parent": "7757bfcd20304c40ae5f908f7ecb56a3",
        "order": 37,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1140,
                    "y1": 6329,
                    "x2": 11090,
                    "y2": 7169,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 2,
            "hash": "cce30d90fcd6850369b202825db72bde51b306ee"
        },
        "content": [
            "Figure 2.Overall Pipeline of STYLEMOTIF,a single difusion branch framework for multi-modal motion stylization.Given a text promptand a reference style from various modalities,our model extract style features and fuse them with content by style-content cross fusion.Through multi-modal alignment with contrastive learning,we enable seamless multi-modal conditioning and flexible stylization acrossmotion,text,images,audio,and video."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.5,
        "bold": True
    },
    {
        "id": "368976d0d5904fdd910188530868c4aa",
        "parent": "7757bfcd20304c40ae5f908f7ecb56a3",
        "order": 38,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1140,
                    "y1": 7649,
                    "x2": 5909,
                    "y2": 10219,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 2,
            "hash": "9962a1d2b9c35f85e5e2061a71a2c441665fa726"
        },
        "content": [
            "(2)limited to style motion for guidance.Another concurrentapproach,MulSMo [31],attempts to address some of theseby introducing a bidirectional control flow between styleand content networks,reducing conflicts.Still,it remainsrestricted to a few modalities and relies on dual branches.In this work we eliminate the need for a dual-branch frame-work,resulting in a simpler,more effective,and efficientapproach to stylized motion generation.Our method alsoenables efficient multi-modal motion stylization,supportingtext,image and multi-modal inputs while maintaining bothhigh stylization quality and motion realism."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.5,
        "bold": False
    },
    {
        "id": "7df7faaecef441969431b74d8430dd6c",
        "parent": "7757bfcd20304c40ae5f908f7ecb56a3",
        "order": 39,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1149,
                    "y1": 10700,
                    "x2": 5930,
                    "y2": 12819,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 2,
            "hash": "6ffad21f83c31e8d8086ada3256cf23c1d6f3300"
        },
        "content": [
            "Multi-modality Learning.Recent advancements in multi-modality learning [9,10,36]have revolutionized variousdomains by enabling joint understanding across diversedata types.Foundational models like CLIP [45]and itsextension [16,66,67,73]establish robust vision-languagealignment,while ImageBind [12],Point-Bind [17],andLanguage-Bind [72]expand this paradigm to diverse modal-ities,demonstrating emergent cross-modal capabilities.Therise of multi-modal large language models [4,19,21,29,30,"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.5,
        "bold": False
    },
    {
        "id": "4a8cd3e310b54584b74178b1babbe9cc",
        "parent": "7757bfcd20304c40ae5f908f7ecb56a3",
        "order": 40,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1149,
                    "y1": 12850,
                    "x2": 5909,
                    "y2": 14259,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 2,
            "hash": "43bd6e37fb3dad791cfbe4a86d8ede50edb624fe"
        },
        "content": [
            "62,70]further enhances semantic understanding through uni-fied text-visual processing,achieving superior performanceacross 2D images [11,28,68],3D point clouds [17,18,50],and complex reasoning scenarios [20,25,69].For humanmotion,some approaches [5,14,24]incorporate multiplemodalities into human motion understanding and generation"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.5,
        "bold": False
    },
    {
        "id": "06a3e3285d0f4fe89b5ea0e7849d7807",
        "parent": "7757bfcd20304c40ae5f908f7ecb56a3",
        "order": 41,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6309,
                    "y1": 7649,
                    "x2": 11080,
                    "y2": 8539,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 2,
            "hash": "e5c3ce1fe60a6a1637f8e9c828a132282c58ebf7"
        },
        "content": [
            "tasks.Our work introduces the first framework for multi-modal guided motion stylization,achieving seamless styleinjection from diverse modalities through a single-branchdiffusion architecture with style-content cross fusion."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.5,
        "bold": False
    },
    {
        "id": "c40c77b617994cf6bb5a3f1dcf979fec",
        "parent": "05c95ebc1e2747cca6e3131ab79a9004",
        "order": 42,
        "dst_type": "text",
        "attributes": {
            "level": 2,
            "position": {
                "bbox": {
                    "x1": 6309,
                    "y1": 8800,
                    "x2": 11090,
                    "y2": 9019,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 2,
            "hash": "a1defdbc08b783748afb76c1d8736e794cae8724"
        },
        "content": [
            "3.STYLEMOTIF:Multi-Modal Motion Styliza-"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.5,
        "bold": True
    },
    {
        "id": "4e72c035b5e14e2ba22849a986887672",
        "parent": "c40c77b617994cf6bb5a3f1dcf979fec",
        "order": 43,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6679,
                    "y1": 9069,
                    "x2": 10450,
                    "y2": 9309,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 2,
            "hash": "ee825592e4bbd4419b0ec3006edbb777500a394b"
        },
        "content": [
            "tion with Style-Content Cross Fusion"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.5,
        "bold": True
    },
    {
        "id": "0da97c6594cb413193d9c0229b5ce5b5",
        "parent": "c40c77b617994cf6bb5a3f1dcf979fec",
        "order": 44,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6309,
                    "y1": 9490,
                    "x2": 11090,
                    "y2": 12550,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 2,
            "hash": "e959e42e6856936f50f614cbebbe4c7266bc1994"
        },
        "content": [
            "We propose STYLEMOTIF,a novel framework for stylizedmotion synthesis that combines style-content cross fusionand multi-modal motion stylization,as illustrated in Figure 2.Our approach integrates a style-content cross fusion mech-anism that allows for coherent feature blending,ensuringthe generated motion accurately reflects the reference stylewhile maintaining the content's realism(§3.2).This mecha-nism is complemented by a multi-modal motion stylizationstrategy,which leverages inputs from diverse modalities suchas image,video,audio,and text to provide control over thestylization process(§3.3).This framework enables highlyflexible and realistic stylized motion synthesis,which offersgreater control and diversity."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.5,
        "bold": False
    },
    {
        "id": "f73368fd05404e47ac01c78d4480ec6c",
        "parent": "c40c77b617994cf6bb5a3f1dcf979fec",
        "order": 45,
        "dst_type": "text",
        "attributes": {
            "level": 3,
            "position": {
                "bbox": {
                    "x1": 6309,
                    "y1": 12729,
                    "x2": 7609,
                    "y2": 12899,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 2,
            "hash": "bc30bc1cb0a6fe9b0959de91da360e2d86205a8a"
        },
        "content": [
            "3.1.Overview"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.5,
        "bold": True
    },
    {
        "id": "98142c7bd5d049cc9eb37cab3da27f66",
        "parent": "f73368fd05404e47ac01c78d4480ec6c",
        "order": 46,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6320,
                    "y1": 13080,
                    "x2": 11099,
                    "y2": 14240,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 2,
            "hash": "9ffe3c8a52e32c213541d349c614089b1e95778d"
        },
        "content": [
            "Preliminaries.Motion Latent Diffusion (MLD)[6]for-mulates a conditional latent diffusion model by trainingEe(zt,t,c)to denoise a sequence of latents{zt}E=0,wheret∈Rn×d represents the motion latent at timestep t,con-ditioned on the distribution p(zt|c).A conditional domain"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.5,
        "bold": False
    },
    {
        "id": "77ef2b9409334b479bee22f0ce40cfc9",
        "parent": "f73368fd05404e47ac01c78d4480ec6c",
        "order": 47,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6049,
                    "y1": 14649,
                    "x2": 6149,
                    "y2": 14799,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 2,
            "hash": "77de68daecd823babbb58edb1c8e14d7106e83bb"
        },
        "content": [
            "3"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 7.0,
        "bold": False
    },
    {
        "id": "5f9523f5b2784d4696ede43f2522b8e3",
        "parent": "f73368fd05404e47ac01c78d4480ec6c",
        "order": 48,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1149,
                    "y1": 1469,
                    "x2": 5919,
                    "y2": 4299,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 3,
            "hash": "5ce7580b6022faa8e64179548461fb278b4e3fcf"
        },
        "content": [
            "encoder Te(c),such as CLIP [45],enables text-to-motiontasks,and the model is trained as ee(zt,t,Te(c)).To in-corporate additional style conditioning,a style condition scan be introduced with its own encoder ψe(s),which maytake the form of a motion or text encoder.This extendsthe model to ee(zt,t,Te(c),ψe(s)).SMooDi[71]adopts aControlNet [64]-style approach for style conditioning bycreating a trainable copy of the neural network weightsθs from the original MLD modelθc.This copy includeszero-initialized linear layers,denoted as Z(;·).The out-put F'(zt,t,Te(c),ψe(s);θc)of the i-th MLD block,nowconditioned on style,is computed as:"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "e7e322b0ac204bdc897177873544feec",
        "parent": "f73368fd05404e47ac01c78d4480ec6c",
        "order": 49,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1300,
                    "y1": 4560,
                    "x2": 5890,
                    "y2": 5070,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 3,
            "hash": "9cc63feee247be88946a720fd763d368d0668908"
        },
        "content": [
            "F'(zt,t,Te(c),ψa(s);θc)=F'(zt,t,Te(c);θc)+Z(F'(zt,t,Te(c),ψe(s);θs);θz)(1)"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "54d92e2fa4c74447b5919e572cd95634",
        "parent": "f73368fd05404e47ac01c78d4480ec6c",
        "order": 50,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1140,
                    "y1": 5330,
                    "x2": 5909,
                    "y2": 6470,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 3,
            "hash": "7241bf228d248b4cdaad9b0f3a52bee863233c94"
        },
        "content": [
            "A key property of this formulation is that since θz is initial-ized to zero,thus F'(zt,t,Te(c),ψe(s))=Fi(zt,t,Te(c))at the beginning.However,the tradeoff of that method isthat it needs to maintain the additional parameters θg and θziin order for style transfer."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "673db6fc39d041c2867cc5353c3478ad",
        "parent": "f73368fd05404e47ac01c78d4480ec6c",
        "order": 51,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1149,
                    "y1": 6840,
                    "x2": 5919,
                    "y2": 11100,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 3,
            "hash": "****************************************"
        },
        "content": [
            "STYLEMOTIF.We propose STYLEMOTIF,eschewingzero linear layers in favor of injecting it directly via sta-tistical manipulation,with the pipeline shown in Figure 2describing our approach.Our STYLEMOTIF utilizes latentspace diffusion within a single generative branch,build-ing upon a pretrained MLD model [6].Instead of perturb-ing the outputs of each block i via zero initialized layerZ(Fi(zt,t,Te(c),ψe(s);θs);θz;),STYLEMOTIF replacesthis with a statistically transformed style embedding that isinjected into the original MLD branch.This simplifies themodel while ensuring high-quality stylization results,whosedetails are presented in§3.2.Also,we follow SMooDi's gen-eration guidance and training scheme to ensure high-qualitystylized motion synthesis.We use a hybrid guidance strategythat balances content fidelity and style adherence by com-bining classifier-free and classifier-based techniques duringdiffusion sampling.Implementation details of the learningscheme are provided in the Supplementary Material."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "52b38d8415fb4b2e96c136d06d3dd1ad",
        "parent": "c40c77b617994cf6bb5a3f1dcf979fec",
        "order": 52,
        "dst_type": "text",
        "attributes": {
            "level": 3,
            "position": {
                "bbox": {
                    "x1": 1149,
                    "y1": 11320,
                    "x2": 4100,
                    "y2": 11519,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 3,
            "hash": "a35a8505859d9374f5060d3ae66cafff4d62e8c3"
        },
        "content": [
            "3.2.Style-Content Cross Fusion"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "c8aafdb1e2df421fa510622c49a17e2a",
        "parent": "52b38d8415fb4b2e96c136d06d3dd1ad",
        "order": 53,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1140,
                    "y1": 11659,
                    "x2": 5919,
                    "y2": 14240,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 3,
            "hash": "da3875b8ccabd21cddd8b9b9f7e83388a9aacb86"
        },
        "content": [
            "Style Encoder Pre-training.To establish a robust founda-tion for style-content fusion,we combine the content knowl-edge from the pre-trained MLD's [6]VAE with the styleknowledge from the 100STYLE[35]dataset.The MLD'sVAE is pre-trained on the HumanML3D[13]dataset,whichprovides extensive understanding of content motion.Build-ing on this,we further fine-tune the model on the 100STYLEdataset in a variational autoencoding manner to initially alignthe content and style data distributions in the latent space.After training,we discard the decoder and retain only theencoder as the motion style encoder.This reconstruction"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "439836708a594aef82536aa917568a1a",
        "parent": "52b38d8415fb4b2e96c136d06d3dd1ad",
        "order": 54,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6309,
                    "y1": 1500,
                    "x2": 11090,
                    "y2": 2640,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 3,
            "hash": "d8c0a5831971552c9dc826b32d479190b5b77649"
        },
        "content": [
            "task enables the encoder to learn robust motion feature repre-sentations,which are essential for supporting the subsequentstylization process.By integrating content and style knowl-edge in this way,we ensure a strong foundation for seamlessstyle-content fusion."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "fa73a2f1e97b445cb795400d6f2864d2",
        "parent": "52b38d8415fb4b2e96c136d06d3dd1ad",
        "order": 55,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6309,
                    "y1": 2950,
                    "x2": 11090,
                    "y2": 5489,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 3,
            "hash": "55dec8f83f5d9b44225b385aae0b801795d526d8"
        },
        "content": [
            "Style-Content Cross Normalization.To train a stylizeddiffusion model eB(zt,t,Te(c),ψe(s)),we effectively fusecontent and style features directly within the latent spacediffusion process instead of using dual-branch networks orseparate control mechanisms [64].Given the output fea-tures Fi of the i-th block,we derive the content featuresfrom the input text c,and the stylefeatures Fs=ψe(s)are extracted from the reference mo-tion sequence s using the pre-trained style encoder.To per-form the fusion,we first compute the mean μcand varianceof the content features across the feature dimension:"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "b9ca6a064e0840e5bf992046d7078a7b",
        "parent": "52b38d8415fb4b2e96c136d06d3dd1ad",
        "order": 56,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 10839,
                    "y1": 6060,
                    "x2": 11080,
                    "y2": 6249,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 3,
            "hash": "0902ac4af971870547e03b039a7e5fcecf60148a"
        },
        "content": [
            "(2)"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "c475173a8e2d49b9b515718e8e2ba537",
        "parent": "52b38d8415fb4b2e96c136d06d3dd1ad",
        "order": 57,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 10829,
                    "y1": 6809,
                    "x2": 11059,
                    "y2": 6970,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 3,
            "hash": "d77df6f4a77bbd6aa02c6035a0267d136736336a"
        },
        "content": [
            "(3)"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "4f016b0d98494966b772fdbee4a67751",
        "parent": "52b38d8415fb4b2e96c136d06d3dd1ad",
        "order": 58,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6320,
                    "y1": 7380,
                    "x2": 11090,
                    "y2": 8309,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 3,
            "hash": "3e455b23e4ca896b8a48a60e7716fa9239be92d8"
        },
        "content": [
            "wherei denotes the content features of the i-th block andthe j-th feature element.Next,we normalize the style fea-tures Fsusing these content statistics,ensuring that the stylefeatures are adapted to the content's statistical properties:"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "ec5bdcee7daf4031a0eea6b4cf7aed5e",
        "parent": "52b38d8415fb4b2e96c136d06d3dd1ad",
        "order": 59,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 10839,
                    "y1": 8559,
                    "x2": 11080,
                    "y2": 8730,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 3,
            "hash": "10720c7cfed258d6a1fa627a781799fd12b2cc6a"
        },
        "content": [
            "(4)"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "6c422db897934daa9ed3b20ccd6313cf",
        "parent": "52b38d8415fb4b2e96c136d06d3dd1ad",
        "order": 60,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6309,
                    "y1": 9079,
                    "x2": 11090,
                    "y2": 10430,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 3,
            "hash": "60d16818273ee1c42bd3a7c19c9335318856adb0"
        },
        "content": [
            "where η is a constant added for numerical stability.Thisstyle-content cross normalization ensures that the style fea-tures are smoothly integrated with the content features whilemaintaining the content's original structure.After that,weadd the normalized style features to the content features,formulating our final cross-normalization as"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "0d6f1de262ec433d835fda6c4e44f99d",
        "parent": "52b38d8415fb4b2e96c136d06d3dd1ad",
        "order": 61,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 10829,
                    "y1": 10609,
                    "x2": 11059,
                    "y2": 10790,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 3,
            "hash": "28bd264a290377b8124b9b0b38bde400148a500d"
        },
        "content": [
            "(5)"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "a9f7c4811ce848c586dc86047a6452e3",
        "parent": "52b38d8415fb4b2e96c136d06d3dd1ad",
        "order": 62,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6309,
                    "y1": 10979,
                    "x2": 11080,
                    "y2": 13059,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 3,
            "hash": "aa610c112aa44668d08ed319558b987a9626b95b"
        },
        "content": [
            "where γ is a parameter used for scaling the normalized value.Fs,ccan be thought of as a perturbation of Fias it is scaledwithin its range.Notably,the fusion process is performedonly once after the m-th block during the denoising processto avoid distorting the content while effectively introducingthe style.This efficient fusion method eliminates the needfor additional learnable parameters,as it is based solelyon statistical transformations,achieving high-quality resultswith minimal computational overhead."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "60f12b960cdc49d1b6b7021381135bca",
        "parent": "c40c77b617994cf6bb5a3f1dcf979fec",
        "order": 63,
        "dst_type": "text",
        "attributes": {
            "level": 3,
            "position": {
                "bbox": {
                    "x1": 6320,
                    "y1": 13230,
                    "x2": 9720,
                    "y2": 13429,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 3,
            "hash": "fd8e20b18035147fac8c14f0025417c5137b44c3"
        },
        "content": [
            "3.3.Multi-Modal Motion Stylization"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "77913070c8ed49fd90891584643fb9b4",
        "parent": "60f12b960cdc49d1b6b7021381135bca",
        "order": 64,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6320,
                    "y1": 13579,
                    "x2": 11090,
                    "y2": 14240,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 3,
            "hash": "ae990017035009a1c58544e3c5b7de2f95a618ee"
        },
        "content": [
            "Our STYLEMoTIF extends to support multi-modal motionstylization.To achieve this,we integrate a pre-trained multi-modal foundation model [12],which provides a unified,"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "d7f110fb471b4d219e01889f36009150",
        "parent": "60f12b960cdc49d1b6b7021381135bca",
        "order": 65,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6049,
                    "y1": 14649,
                    "x2": 6169,
                    "y2": 14799,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 3,
            "hash": "1b6453892473a467d07372d45eb05abc2031647a"
        },
        "content": [
            "4"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "3a11870c446b4a46bde9cd29f826afd9",
        "parent": "60f12b960cdc49d1b6b7021381135bca",
        "order": 66,
        "dst_type": "image",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 7579,
                    "y1": 6569,
                    "x2": 9809,
                    "y2": 7249,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 3,
            "hash": "1e43dc58925552e3adb6ace3ea420ec4c379c2c2"
        },
        "content": [
            "http://kna-common.ks3-cn-beijing.ksyuncs.com/insight-ai/doc-parser/ec02bbf7868b429aa5f2253abd105c65_3.png?Signature=LMJ6kLtbBYbscB7O2zmzLxqXrEU%3D&Expires=3243326893&KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb"
        ],
        "image_pixel": [
            223,
            68
        ],
        "mark": None,
        "font_size": -1,
        "bold": False
    },
    {
        "id": "05d15ce146b2445d9a7d11c98fe54b72",
        "parent": "60f12b960cdc49d1b6b7021381135bca",
        "order": 67,
        "dst_type": "image",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 7590,
                    "y1": 5860,
                    "x2": 9069,
                    "y2": 6530,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 3,
            "hash": "693ac9f7b378cde79440b1403ff7086328967452"
        },
        "content": [
            "http://kna-common.ks3-cn-beijing.ksyuncs.com/insight-ai/doc-parser/a4cd42acde214dd996e0c9e73f4cd510_4.png?Signature=iCJkS9EdTfrl3MtUdjurVrS2kvg%3D&Expires=3243326893&KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb"
        ],
        "image_pixel": [
            148,
            67
        ],
        "mark": None,
        "font_size": -1,
        "bold": False
    },
    {
        "id": "8bae67849613435b8b673497952d3b55",
        "parent": "60f12b960cdc49d1b6b7021381135bca",
        "order": 68,
        "dst_type": "image",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 7909,
                    "y1": 8409,
                    "x2": 9490,
                    "y2": 8970,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 3,
            "hash": "e846f97023b8a0f630c9f675c6844ce75621aabd"
        },
        "content": [
            "http://kna-common.ks3-cn-beijing.ksyuncs.com/insight-ai/doc-parser/cfb8d7bee1c74ec7b32badfa4a772e08_5.png?Signature=0iShEznwLVJZ0%2F%2FItAnj6khPAMw%3D&Expires=3243326893&KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb"
        ],
        "image_pixel": [
            158,
            56
        ],
        "mark": None,
        "font_size": -1,
        "bold": False
    },
    {
        "id": "a95afee199e54cd9b20b3594c33c0fdf",
        "parent": "60f12b960cdc49d1b6b7021381135bca",
        "order": 69,
        "dst_type": "image",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6320,
                    "y1": 4359,
                    "x2": 8309,
                    "y2": 4610,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 3,
            "hash": "1d83cf0eb00586cdb8322ea1115a3a38ebd028d3"
        },
        "content": [
            "http://kna-common.ks3-cn-beijing.ksyuncs.com/insight-ai/doc-parser/a08bb7595b1149db8d5582ce96d14b28_6.png?Signature=sZOHNNutylhibjD5PNwtTVdCO1E%3D&Expires=3243326893&KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb"
        ],
        "image_pixel": [
            199,
            25
        ],
        "mark": None,
        "font_size": -1,
        "bold": False
    },
    {
        "id": "4508d2a2635446ec8e09541924527706",
        "parent": "60f12b960cdc49d1b6b7021381135bca",
        "order": 70,
        "dst_type": "image",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6839,
                    "y1": 7350,
                    "x2": 7179,
                    "y2": 7610,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 3,
            "hash": "3213cf3792058f7dabc47621c6b3d1592d3a2f9f"
        },
        "content": [
            "http://kna-common.ks3-cn-beijing.ksyuncs.com/insight-ai/doc-parser/51c77edae5294c2583cd6ce7d6da2e73_7.png?Signature=O9xs1Mh1SXsDmKrgT%2B2EuZNNBiI%3D&Expires=3243326893&KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb"
        ],
        "image_pixel": [
            34,
            26
        ],
        "mark": None,
        "font_size": -1,
        "bold": False
    },
    {
        "id": "fee4f2e07814410db18e8f75db2473e4",
        "parent": "60f12b960cdc49d1b6b7021381135bca",
        "order": 71,
        "dst_type": "image",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6330,
                    "y1": 5300,
                    "x2": 6530,
                    "y2": 5559,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 3,
            "hash": "de91a1a564932a1ff89df5fb60e3e563dd3b7ad1"
        },
        "content": [
            "http://kna-common.ks3-cn-beijing.ksyuncs.com/insight-ai/doc-parser/7fae93e382664f81a5462ee2ea18571a_8.png?Signature=FbbMihW6IAmBCiArz%2BmpXnAJDX8%3D&Expires=3243326893&KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb"
        ],
        "image_pixel": [
            20,
            26
        ],
        "mark": None,
        "font_size": -1,
        "bold": False
    },
    {
        "id": "f42e53e3476444b3900841f9b2920b90",
        "parent": "60f12b960cdc49d1b6b7021381135bca",
        "order": 72,
        "dst_type": "image",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6949,
                    "y1": 10529,
                    "x2": 10550,
                    "y2": 10870,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 3,
            "hash": "30557c1c477146ee147b89b02c79308a8e5d91af"
        },
        "content": [
            "http://kna-common.ks3-cn-beijing.ksyuncs.com/insight-ai/doc-parser/47e65a62063e4812a4c38211f5fa82ad_9.png?Signature=5AWXnjNPajB3lRIVJwLHUd2nifo%3D&Expires=3243326893&KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb"
        ],
        "image_pixel": [
            360,
            34
        ],
        "mark": None,
        "font_size": -1,
        "bold": False
    },
    {
        "id": "65e2d1187f2a44d6bca86cbae0a5f0b0",
        "parent": "60f12b960cdc49d1b6b7021381135bca",
        "order": 73,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1149,
                    "y1": 1500,
                    "x2": 5919,
                    "y2": 3350,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 4,
            "hash": "28773bc9da894ac945e26ad53bfd4a398262b48b"
        },
        "content": [
            "multi-modal aligned feature space,enabling effective cross-modal alignment between motion and other modalities.Byleveraging this alignment,our model can flexibly combinemultiple input modalities,such as text,image,audio,andvideo,to guide the stylization process in a comprehensivemanner.This capability results in emergent abilities formulti-modal motion stylization,allowing for more nuancedoutputs."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.5,
        "bold": False
    },
    {
        "id": "cbbae55eefbc4eb7bb4e0c776d78110e",
        "parent": "60f12b960cdc49d1b6b7021381135bca",
        "order": 74,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1140,
                    "y1": 3749,
                    "x2": 5919,
                    "y2": 6080,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 4,
            "hash": "295190dc7f48de64fd6a0e4e0f759a84eb1b3c03"
        },
        "content": [
            "Motion-Text Pair Curation.To align motion with othermodalities,we process a set of motion-text pairs using thecurated 100STYLE subset [35],which is carefully selectedto avoid conflicts between content and style motions,so thatthe model can effectively learn the relationships betweenmotion and text.Each motion sequence is paired with acorresponding single textual label,which serves as the textprompt.The curated motion-text pairs will be used for thefollowing cross-model alignment.Please refer to the Supple-mentary Material for more details."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.5,
        "bold": False
    },
    {
        "id": "5490c43be4914f2883b9dd34ccbaf88e",
        "parent": "60f12b960cdc49d1b6b7021381135bca",
        "order": 75,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1149,
                    "y1": 6479,
                    "x2": 5919,
                    "y2": 8590,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 4,
            "hash": "92341e7e2a6537a062e6c14cee384680174b6183"
        },
        "content": [
            "Multi-Modal Alignment.To maintain the alignmentwithin the multi-modal space,we freeze the text encoderof ImageBind [12]and introduce a lightweight projectionlayer after the encoder.This projection layer aligns the fea-ture dimensions of the text and motion encoders.Since themulti-modal model represents each modality with a globalfeature,our alignment process focuses on these global fea-tures to achieve robust alignment between text and motionrepresentations,formalized as:"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.5,
        "bold": False
    },
    {
        "id": "1e8e5f9890c54e15a6f5732cc497c2b5",
        "parent": "60f12b960cdc49d1b6b7021381135bca",
        "order": 76,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 2830,
                    "y1": 8839,
                    "x2": 4209,
                    "y2": 9050,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 4,
            "hash": "29f17b3b735efad1861b46cbfa0e04b919638d83"
        },
        "content": [
            "Ft=π(ext(L)),"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.5,
        "bold": False
    },
    {
        "id": "7e76f9bc4dc341ef914ce8d9451a4fb0",
        "parent": "60f12b960cdc49d1b6b7021381135bca",
        "order": 77,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 2819,
                    "y1": 9159,
                    "x2": 3909,
                    "y2": 9360,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 4,
            "hash": "b2fb1b161ecd92fa6140cb8d5357adcec0d13521"
        },
        "content": [
            "Fs=ψe(s),"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.5,
        "bold": False
    },
    {
        "id": "161047db14734007a28ce5930bcbec91",
        "parent": "60f12b960cdc49d1b6b7021381135bca",
        "order": 78,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 5660,
                    "y1": 8869,
                    "x2": 5900,
                    "y2": 9009,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 4,
            "hash": "d531ab20079dd9771b54bfab648528904866b8b4"
        },
        "content": [
            "(6)"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.5,
        "bold": False
    },
    {
        "id": "7595d6ed3a7549e286e38f4b6db30cca",
        "parent": "60f12b960cdc49d1b6b7021381135bca",
        "order": 79,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 5660,
                    "y1": 9159,
                    "x2": 5900,
                    "y2": 9340,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 4,
            "hash": "f0b422b67ebefe17c506ae529e6f54d8ccea5242"
        },
        "content": [
            "(7)"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.5,
        "bold": False
    },
    {
        "id": "6a45126a71ae4ce58bdc19cadd976a30",
        "parent": "60f12b960cdc49d1b6b7021381135bca",
        "order": 80,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1140,
                    "y1": 9619,
                    "x2": 5909,
                    "y2": 11690,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 4,
            "hash": "bbd7931b7c112ff5574dd674aa9fff7e6697dea7"
        },
        "content": [
            "wherel and s are the paired input text label and style motion,Ftand Fs denote the feature representations from the textand style motion encoders,Eext and 4ψeg,respectively,andπrepresents the projection operation that maps text featuresinto the motion feature space.We employ a contrastivelearning loss [61]to align the feature spaces of motion andtext,bringing them closer together in the shared multi-modalspace.Thus,we obtain a unified space between motion andall the modalties.We formulated it as"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.5,
        "bold": False
    },
    {
        "id": "ecd9504639cc441aabce26118b5a4ed5",
        "parent": "60f12b960cdc49d1b6b7021381135bca",
        "order": 81,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 5660,
                    "y1": 12649,
                    "x2": 5900,
                    "y2": 12819,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 4,
            "hash": "630d9ccc0aa5428d4f10037c8cbdf0d7bec5cfb3"
        },
        "content": [
            "(8)"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.5,
        "bold": False
    },
    {
        "id": "dedcab4f306f4266881c979182193c4c",
        "parent": "60f12b960cdc49d1b6b7021381135bca",
        "order": 82,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1140,
                    "y1": 13099,
                    "x2": 5919,
                    "y2": 14240,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 4,
            "hash": "bc895a2b61cde653bfde11e48ede3207463f213a"
        },
        "content": [
            "where t and s represent two modalities (text and style mo-tion)and(i,j)indicates a positive pair in each training batch,k indexes all samples in the batch,including both positiveand negative ones,and To is a temperature parameter.Duringinference,we obtain the multi-modal(text,image,video,or"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.5,
        "bold": False
    },
    {
        "id": "e3a040382f6847aa877ca611227eb8ef",
        "parent": "60f12b960cdc49d1b6b7021381135bca",
        "order": 83,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6320,
                    "y1": 1500,
                    "x2": 10009,
                    "y2": 1679,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 4,
            "hash": "ea0cfcf54bd9d4842e5ae9cc93a920239ec0cf2a"
        },
        "content": [
            "audio)features from the multi-modal input m,"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.5,
        "bold": False
    },
    {
        "id": "568f7e8b93d84c8baf854b0afd981271",
        "parent": "60f12b960cdc49d1b6b7021381135bca",
        "order": 84,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 7830,
                    "y1": 1939,
                    "x2": 9520,
                    "y2": 2139,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 4,
            "hash": "a215d3bb83b24d572bd3528529e1685995e94fb5"
        },
        "content": [
            "Fm=&mageBind(m),"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.5,
        "bold": False
    },
    {
        "id": "46b1eb5759334ffb96b52202cb352863",
        "parent": "60f12b960cdc49d1b6b7021381135bca",
        "order": 85,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 10829,
                    "y1": 1939,
                    "x2": 11059,
                    "y2": 2100,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 4,
            "hash": "f565b2b5f3419cfa729b792b5d948992be0d69f1"
        },
        "content": [
            "(9)"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.5,
        "bold": False
    },
    {
        "id": "6a8db5053bf0417caf467bcf45ed0321",
        "parent": "60f12b960cdc49d1b6b7021381135bca",
        "order": 86,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6309,
                    "y1": 2389,
                    "x2": 11090,
                    "y2": 4230,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 4,
            "hash": "aed1fedf46462197ef1eabefec08fcfc888009e4"
        },
        "content": [
            "where EmageBind denotes ImageBind [12].We use the multi-modal features Fm to retrieve the most semantically similarmotion features from the unified multi-modal space.The re-trieved motion features are then used to guide the stylizationprocess,ensuring that fine-grained style details are preservedand accurately reflected in the final output.This approachenhances the model's ability to generate stylized motionsthat are both contextually relevant and visually coherent."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.5,
        "bold": False
    },
    {
        "id": "38743c4e4c214252bd5061750334377f",
        "parent": "05c95ebc1e2747cca6e3131ab79a9004",
        "order": 87,
        "dst_type": "text",
        "attributes": {
            "level": 2,
            "position": {
                "bbox": {
                    "x1": 6309,
                    "y1": 4569,
                    "x2": 7779,
                    "y2": 4780,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 4,
            "hash": "fd6e157d479ebb665706f7a054ac5eb24247fc56"
        },
        "content": [
            "4.Experiment"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 12.5,
        "bold": False
    },
    {
        "id": "e3e01d31f7384d5fad48ebb5ca896104",
        "parent": "38743c4e4c214252bd5061750334377f",
        "order": 88,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6320,
                    "y1": 4969,
                    "x2": 11059,
                    "y2": 5639,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 4,
            "hash": "16ac7181f9c867f17d733b7fcb8b18b84cfd175d"
        },
        "content": [
            "We provide extensive quantitative and qualitative analysesacross multiple tasks in this section,with additional videosavailable in the Supplementary Material."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.5,
        "bold": False
    },
    {
        "id": "602f1c1d9f1249c4820cbf621d7a3938",
        "parent": "38743c4e4c214252bd5061750334377f",
        "order": 89,
        "dst_type": "text",
        "attributes": {
            "level": 3,
            "position": {
                "bbox": {
                    "x1": 6309,
                    "y1": 5920,
                    "x2": 8779,
                    "y2": 6109,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 4,
            "hash": "52dcae7398768838aeb54b796ede41fc005c665a"
        },
        "content": [
            "4.1.Experimental Settings"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.5,
        "bold": False
    },
    {
        "id": "94ae2aa7ca4246a4bb832b4545adf228",
        "parent": "602f1c1d9f1249c4820cbf621d7a3938",
        "order": 90,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6309,
                    "y1": 6249,
                    "x2": 11090,
                    "y2": 8350,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 4,
            "hash": "1e7197a6c09059f2d76a47405c6d4069f0fdc90b"
        },
        "content": [
            "Implementation Details.We adopt the pre-trainedMLD [6]as the foundation for motion generation.The styleencoder of our model derives from the encoder of MLD'sVAE,while the projection layer after text encoder is a singleLinear layer.During diffusion training,we only enable thestyle encoder to be trainable while freezing other parameters.The model is optimized using the AdamW optimizer [33]with a constant learning rate of 10-5.More implementationdetails are presented in the Supplementary Material."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.5,
        "bold": False
    },
    {
        "id": "00c76ee012a04f35a640cda29d07f4d7",
        "parent": "602f1c1d9f1249c4820cbf621d7a3938",
        "order": 91,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6320,
                    "y1": 8709,
                    "x2": 11090,
                    "y2": 9870,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 4,
            "hash": "76f81ac615a2ef47dae99d1d4ecdc0fe1de361a2"
        },
        "content": [
            "Dataset Settings.We use the HumanML3D [13]datasetas our primary motion content dataset,which collects 14,616motion sequences from AMASS [34]and annotates 44,970sequence-level textual description.To train the style net-work,we utilize the 100STYLE[35]dataset,containing"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.5,
        "bold": False
    },
    {
        "id": "ffe987f598214b12bc13361d9f9a5a39",
        "parent": "602f1c1d9f1249c4820cbf621d7a3938",
        "order": 92,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6309,
                    "y1": 9929,
                    "x2": 11080,
                    "y2": 11069,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 4,
            "hash": "707c6c6dd28cbc5bea2f20d5524c82baf2afc584"
        },
        "content": [
            "45,303 style motions.We also adopt the text annotationsfor 100STYLE from previous work [71],which are pseudotext descriptions generated from MotionGPT [24].We uti-lize the consistent root-velocity motion representations fromHumanML3D for both the content and style data."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.5,
        "bold": False
    },
    {
        "id": "b1f8f32940954ffcbb1d11dc6192db30",
        "parent": "602f1c1d9f1249c4820cbf621d7a3938",
        "order": 93,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6309,
                    "y1": 11420,
                    "x2": 11090,
                    "y2": 13279,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 4,
            "hash": "30edd186e1ff9bd8f976906b41eeeaab58a91356"
        },
        "content": [
            "Baselines.We mainly evaluate our method againstSMooDi[71]on motion-guided stylization and motion styletransfer tasks.We also compare our text-guided stylicationwith ‘ChatGPT+MLD’approach,which utilize ChatGPTto combine style text and content text,and functions as astraightforward text-to-motion model without control capa-bilities [71].Additionally,we qualitatively compare ourmodel with SMooDi on motion-guided stylization."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.5,
        "bold": False
    },
    {
        "id": "973df2dffc744557b842a632fc1126b5",
        "parent": "602f1c1d9f1249c4820cbf621d7a3938",
        "order": 94,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6309,
                    "y1": 13809,
                    "x2": 11099,
                    "y2": 14259,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 4,
            "hash": "3b397275778d8311f0a2bf1402da3d6e12d8d19b"
        },
        "content": [
            "Evaluation Metrics.To evaluate our model,we use sev-eral metrics following previous works [71].We measure"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.5,
        "bold": False
    },
    {
        "id": "9e1bd75dcc2347038bcad5db4dae7cf1",
        "parent": "602f1c1d9f1249c4820cbf621d7a3938",
        "order": 95,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6039,
                    "y1": 14649,
                    "x2": 6149,
                    "y2": 14799,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 4,
            "hash": "ac3478d69a3c81fa62e60f5c3696165a4e5e6ac4"
        },
        "content": [
            "5"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.5,
        "bold": False
    },
    {
        "id": "2afd763ca4344985b5fb637cf0a6fd39",
        "parent": "602f1c1d9f1249c4820cbf621d7a3938",
        "order": 96,
        "dst_type": "image",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1159,
                    "y1": 11909,
                    "x2": 5919,
                    "y2": 12649,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 4,
            "hash": "f2ac7c3af6e229f157cd145a1de0a56e790cbd74"
        },
        "content": [
            "http://kna-common.ks3-cn-beijing.ksyuncs.com/insight-ai/doc-parser/5cd06e323dbd4007a5181563440912c9_10.png?Signature=YDjcJ61JxHVpVTCYct6zGrhpZpM%3D&Expires=3243326893&KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb"
        ],
        "image_pixel": [
            476,
            74
        ],
        "mark": None,
        "font_size": -1,
        "bold": False
    },
    {
        "id": "662fae520b494772831023708deba8e7",
        "parent": "602f1c1d9f1249c4820cbf621d7a3938",
        "order": 97,
        "dst_type": "image",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 2730,
                    "y1": 7029,
                    "x2": 5330,
                    "y2": 9890,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 5,
            "hash": "32414403ae1c90acc85a24f4dd83a3d2bc5588e7"
        },
        "content": [
            "http://kna-common.ks3-cn-beijing.ksyuncs.com/insight-ai/doc-parser/e5cec1f8b2eb4ff28883e3164d2fc5b6_18.png?Signature=sBJpy6j17JCcmkwEMUW9CJ%2Fu6yM%3D&Expires=3243326894&KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb"
        ],
        "image_pixel": [
            260,
            287
        ],
        "mark": None,
        "font_size": -1,
        "bold": False
    },
    {
        "id": "ab48720ddd684710bd49adb6f888adcc",
        "parent": "602f1c1d9f1249c4820cbf621d7a3938",
        "order": 98,
        "dst_type": "table",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1229,
                    "y1": 1479,
                    "x2": 7100,
                    "y2": 3480,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 5,
            "hash": "8bcb8eb239390a25ccef88c60095915d371236b6"
        },
        "content": [
            "<table><tr><td rowspan=\"1\" colspan=\"1\">Method</td><td rowspan=\"1\" colspan=\"1\">SRA↑</td><td rowspan=\"1\" colspan=\"1\">FID↓</td><td rowspan=\"1\" colspan=\"1\">MMDist↓</td><td rowspan=\"1\" colspan=\"1\">R-Precision↑(Top-3)</td><td rowspan=\"1\" colspan=\"1\">Diversity→</td><td rowspan=\"1\" colspan=\"1\">Foot SkateRatio↓</td></tr><tr><td rowspan=\"1\" colspan=\"7\">Motion-Guided Stylization</td></tr><tr><td rowspan=\"1\" colspan=\"1\">MLD+Aberman [1]</td><td rowspan=\"1\" colspan=\"1\">54.37</td><td rowspan=\"1\" colspan=\"1\">3.309</td><td rowspan=\"1\" colspan=\"1\">5.983</td><td rowspan=\"1\" colspan=\"1\">0.406</td><td rowspan=\"1\" colspan=\"1\">8.816</td><td rowspan=\"1\" colspan=\"1\">0.347</td></tr><tr><td rowspan=\"1\" colspan=\"1\">MLD+Motion Puzzle [23]</td><td rowspan=\"1\" colspan=\"1\">63.77</td><td rowspan=\"1\" colspan=\"1\">6.127</td><td rowspan=\"1\" colspan=\"1\">6.467</td><td rowspan=\"1\" colspan=\"1\">0.290</td><td rowspan=\"1\" colspan=\"1\">6.476</td><td rowspan=\"1\" colspan=\"1\">0.185</td></tr><tr><td rowspan=\"1\" colspan=\"1\">SMooDi[71]</td><td rowspan=\"1\" colspan=\"1\">72.42</td><td rowspan=\"1\" colspan=\"1\">1.609</td><td rowspan=\"1\" colspan=\"1\">4.477</td><td rowspan=\"1\" colspan=\"1\">0.571</td><td rowspan=\"1\" colspan=\"1\">9.235</td><td rowspan=\"1\" colspan=\"1\">0.124</td></tr><tr><td rowspan=\"1\" colspan=\"1\">STYLEMOTIF(Ours)</td><td rowspan=\"1\" colspan=\"1\">77.65</td><td rowspan=\"1\" colspan=\"1\">1.551</td><td rowspan=\"1\" colspan=\"1\">4.354</td><td rowspan=\"1\" colspan=\"1\">0.586</td><td rowspan=\"1\" colspan=\"1\">7.567</td><td rowspan=\"1\" colspan=\"1\">0.097</td></tr><tr><td rowspan=\"1\" colspan=\"7\">Text-Guided Srylization</td></tr><tr><td rowspan=\"1\" colspan=\"1\">MLD+ChatGPT [71]</td><td rowspan=\"1\" colspan=\"1\">4.82</td><td rowspan=\"1\" colspan=\"1\">0.614</td><td rowspan=\"1\" colspan=\"1\">4.313</td><td rowspan=\"1\" colspan=\"1\">0.605</td><td rowspan=\"1\" colspan=\"1\">8.836</td><td rowspan=\"1\" colspan=\"1\">0.131</td></tr><tr><td rowspan=\"1\" colspan=\"1\">STYLEMOTIF(Ours)</td><td rowspan=\"1\" colspan=\"1\">56.71</td><td rowspan=\"1\" colspan=\"1\">0.603</td><td rowspan=\"1\" colspan=\"1\">3.684</td><td rowspan=\"1\" colspan=\"1\">0.690</td><td rowspan=\"1\" colspan=\"1\">9.101</td><td rowspan=\"1\" colspan=\"1\">0.101</td></tr></table>"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": -1,
        "bold": False
    },
    {
        "id": "b0dc0ce782f840cb9bff2990a216da0b",
        "parent": "602f1c1d9f1249c4820cbf621d7a3938",
        "order": 99,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1240,
                    "y1": 3689,
                    "x2": 7120,
                    "y2": 4299,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 5,
            "hash": "45f70966c33a23c49a00827463382b9e87835808"
        },
        "content": [
            "Table 1.Quantitative Results for Motion-Guided and Text-Guided Stylization.Bold values denote the best performance.As there is no ground-truth reference forDiversity,no value is highlighted in bold;but the metric is provided for reference."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.0,
        "bold": True
    },
    {
        "id": "63e5c939818e44ffa8551b903b3a53e9",
        "parent": "602f1c1d9f1249c4820cbf621d7a3938",
        "order": 100,
        "dst_type": "table",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 7369,
                    "y1": 1479,
                    "x2": 10880,
                    "y2": 2609,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 5,
            "hash": "6bb874fd8c182822663b4a179e20afe4e39d9170"
        },
        "content": [
            "<table><tr><td rowspan=\"1\" colspan=\"1\">Method</td><td rowspan=\"1\" colspan=\"1\">SRA个</td><td rowspan=\"1\" colspan=\"1\">FID↓</td><td rowspan=\"1\" colspan=\"1\">Foot SkateRatio↓</td></tr><tr><td rowspan=\"1\" colspan=\"1\">MLD+Aberman [1]</td><td rowspan=\"1\" colspan=\"1\">61.01</td><td rowspan=\"1\" colspan=\"1\">3.892</td><td rowspan=\"1\" colspan=\"1\">0.338</td></tr><tr><td rowspan=\"1\" colspan=\"1\">MLD+Motion Puzzle [23]</td><td rowspan=\"1\" colspan=\"1\">67.23</td><td rowspan=\"1\" colspan=\"1\">6.871</td><td rowspan=\"1\" colspan=\"1\">0.197</td></tr><tr><td rowspan=\"1\" colspan=\"1\">SMooDi[71]</td><td rowspan=\"1\" colspan=\"1\">65.15</td><td rowspan=\"1\" colspan=\"1\">1.582</td><td rowspan=\"1\" colspan=\"1\">0.095</td></tr><tr><td rowspan=\"1\" colspan=\"1\">STYLEMoTIF(Ours)</td><td rowspan=\"1\" colspan=\"1\">68.81</td><td rowspan=\"1\" colspan=\"1\">1.375</td><td rowspan=\"1\" colspan=\"1\">0.094</td></tr></table>"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": -1,
        "bold": False
    },
    {
        "id": "44c858bf43c844428e3f859499338c72",
        "parent": "602f1c1d9f1249c4820cbf621d7a3938",
        "order": 101,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 7279,
                    "y1": 2819,
                    "x2": 10980,
                    "y2": 4320,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 5,
            "hash": "ea61e17b22d36c570f1b68b076f306244f38d1e0"
        },
        "content": [
            "Table 2.Quantitative Results of Motion StyleTransfer on HumanML3D [13]dataset.Ourmethod outperforms previous works in all metrics,which demonstrates effective style-content fusionfor high-quality motion style transfer,providing sig-nificant advantages for downstream tasks besidesmotion stylization."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.0,
        "bold": False
    },
    {
        "id": "ba6a5f3e3740456b90180ca48979f682",
        "parent": "602f1c1d9f1249c4820cbf621d7a3938",
        "order": 102,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1200,
                    "y1": 4710,
                    "x2": 10750,
                    "y2": 9440,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 5,
            "hash": "f498b67d24666bc61f4071eb5c794b945429b99b"
        },
        "content": [
            "Motion-guided StylizationFirstLastFrameFrameInputStyle MotionInput“A person walks forwardA person walks in a circle.\"\"A person hops.\"Content Textthen turns around.\"SMooDiStyleMotif(Ours)"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.0,
        "bold": True
    },
    {
        "id": "0891a688c0c64b09b205c61dfb036ff9",
        "parent": "602f1c1d9f1249c4820cbf621d7a3938",
        "order": 103,
        "dst_type": "image",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 2730,
                    "y1": 5100,
                    "x2": 5330,
                    "y2": 6530,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 5,
            "hash": "144d5105b88373d25e9229b0c4e49f10d25701db"
        },
        "content": [
            "http://kna-common.ks3-cn-beijing.ksyuncs.com/insight-ai/doc-parser/a32c7b10ce6b4144a6f5d8dd57fc5cc2_12.png?Signature=eNMmE5u7phZwIe50iXEd5PGDNoI%3D&Expires=3243326894&KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb"
        ],
        "image_pixel": [
            260,
            143
        ],
        "mark": None,
        "font_size": -1,
        "bold": False
    },
    {
        "id": "53c36afb53544402a2262ae434701af0",
        "parent": "602f1c1d9f1249c4820cbf621d7a3938",
        "order": 104,
        "dst_type": "image",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 5609,
                    "y1": 5119,
                    "x2": 8199,
                    "y2": 6549,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 5,
            "hash": "9dd480c99f97728ed930c8c968412f675568e7c4"
        },
        "content": [
            "http://kna-common.ks3-cn-beijing.ksyuncs.com/insight-ai/doc-parser/b770b1de554240b0ad1f079810d24429_11.png?Signature=dTeMtSzNP8x7LUfS0DbH3NBv09g%3D&Expires=3243326894&KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb"
        ],
        "image_pixel": [
            259,
            143
        ],
        "mark": None,
        "font_size": -1,
        "bold": False
    },
    {
        "id": "57594c40d3c4403b96d871651e1f7676",
        "parent": "602f1c1d9f1249c4820cbf621d7a3938",
        "order": 105,
        "dst_type": "image",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 8479,
                    "y1": 5090,
                    "x2": 11059,
                    "y2": 6530,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 5,
            "hash": "244123352dc1fcd50e6fbce12ca9ed272785b774"
        },
        "content": [
            "http://kna-common.ks3-cn-beijing.ksyuncs.com/insight-ai/doc-parser/fe19f6939e514baeb9a9c7e9af8b454a_14.png?Signature=Y%2Bc%2Bi3vj1m3jHv%2BkectBFOwyQKY%3D&Expires=3243326894&KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb"
        ],
        "image_pixel": [
            258,
            144
        ],
        "mark": None,
        "font_size": -1,
        "bold": False
    },
    {
        "id": "ab70ae4d41694a26b52f1b59664f2986",
        "parent": "602f1c1d9f1249c4820cbf621d7a3938",
        "order": 106,
        "dst_type": "image",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 5619,
                    "y1": 7019,
                    "x2": 8199,
                    "y2": 8450,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 5,
            "hash": "b5b0d29b0141e6d79af3b8ad718da238b22a4c86"
        },
        "content": [
            "http://kna-common.ks3-cn-beijing.ksyuncs.com/insight-ai/doc-parser/b93326a7b68440c797ac79e4b24f4f10_15.png?Signature=Gf2f9UjKcXyl3wMzSTTkUPQEz64%3D&Expires=3243326894&KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb"
        ],
        "image_pixel": [
            258,
            144
        ],
        "mark": None,
        "font_size": -1,
        "bold": False
    },
    {
        "id": "196f52f3b0bf42fc8117cdcef279e2a4",
        "parent": "602f1c1d9f1249c4820cbf621d7a3938",
        "order": 107,
        "dst_type": "image",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 8499,
                    "y1": 7019,
                    "x2": 11069,
                    "y2": 8450,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 5,
            "hash": "e4dc1c101066995bbc0f2c619fcc66e5ce2e41aa"
        },
        "content": [
            "http://kna-common.ks3-cn-beijing.ksyuncs.com/insight-ai/doc-parser/f258bc2592554d648dcfb7403340b961_13.png?Signature=GDL4TB8r5oHtqT2aB%2BLBUdDFOdA%3D&Expires=3243326894&KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb"
        ],
        "image_pixel": [
            257,
            144
        ],
        "mark": None,
        "font_size": -1,
        "bold": False
    },
    {
        "id": "131c3dc3d92a47ca9493880c60bf6fcf",
        "parent": "602f1c1d9f1249c4820cbf621d7a3938",
        "order": 108,
        "dst_type": "image",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 5619,
                    "y1": 8450,
                    "x2": 8199,
                    "y2": 9909,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 5,
            "hash": "2193e60484adce9ba3c6c639398dc583f79ca7fa"
        },
        "content": [
            "http://kna-common.ks3-cn-beijing.ksyuncs.com/insight-ai/doc-parser/3810944c73b74d89a21ea387e01aa4ee_16.png?Signature=2Cfwq0DEzHwU9aAdan%2FH%2FqqrT4Y%3D&Expires=3243326894&KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb"
        ],
        "image_pixel": [
            258,
            145
        ],
        "mark": None,
        "font_size": -1,
        "bold": False
    },
    {
        "id": "487ab7935f15444dafe6b5abcf75c0dc",
        "parent": "602f1c1d9f1249c4820cbf621d7a3938",
        "order": 109,
        "dst_type": "image",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 8499,
                    "y1": 8459,
                    "x2": 11039,
                    "y2": 9900,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 5,
            "hash": "7da99f1ad4931a5c5751636ab1384a2824323a00"
        },
        "content": [
            "http://kna-common.ks3-cn-beijing.ksyuncs.com/insight-ai/doc-parser/fa9f562a9101487d8974b826a68f3aa2_17.png?Signature=XzzKCBsRujvnfQrpTCvbO7v8St0%3D&Expires=3243326894&KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb"
        ],
        "image_pixel": [
            254,
            145
        ],
        "mark": None,
        "font_size": -1,
        "bold": False
    },
    {
        "id": "2a5cc218f06b4b01805771d99318b7c5",
        "parent": "602f1c1d9f1249c4820cbf621d7a3938",
        "order": 110,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1149,
                    "y1": 10229,
                    "x2": 11050,
                    "y2": 10839,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 5,
            "hash": "83ac0354417a2bb956fab5f454e97eb1d50c479e"
        },
        "content": [
            "Figure 3.Qualitative Results of Motion-Guided Stylization.Our model generates cohesive and realistic motions that effectively alignstyle and content,such as preserving the 'circular'trajectory (first column)and \"hop'content (third column).In contrast,SMooDi[71]struggles to maintain content fidelity and sometimes fails to reflect the specified style(e.g.,‘phone on the left'in the second colum)."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.0,
        "bold": True
    },
    {
        "id": "344ca06e733c49b89707f65f4ab16438",
        "parent": "602f1c1d9f1249c4820cbf621d7a3938",
        "order": 111,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1140,
                    "y1": 11310,
                    "x2": 5919,
                    "y2": 14130,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 5,
            "hash": "02738e20bd69e60b518f3fc847847683164953d5"
        },
        "content": [
            "R-precision,Multi-modal Distance(MM Dist),Diversity,and Frechet Inception Distance (FID)to assess how well thecontent from text is preserved and how realistic the gener-ated motions are.We use Style Recognition Accuracy(SRA)to evaluate how accurately the style of the reference motionis reflected in the generated motion.Also,we adopt the FootSkate Ratio,which helps assess the realism of the generatedmotion,reducing artifacts like foot sliding.During evalu-ation,following previous works [71],we randomly selecta content text from HumanML3D [13]and a style motionfrom 100STYLE[35],and compute SRA for the generatedmotion using a pre-trained classifier[71]."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.0,
        "bold": False
    },
    {
        "id": "408bd380118a4eec980e17767adcf029",
        "parent": "38743c4e4c214252bd5061750334377f",
        "order": 112,
        "dst_type": "text",
        "attributes": {
            "level": 3,
            "position": {
                "bbox": {
                    "x1": 6320,
                    "y1": 11310,
                    "x2": 9150,
                    "y2": 11499,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 5,
            "hash": "a77ee979d0086593549047614d92463745844099"
        },
        "content": [
            "4.2.Motion-guided Stylization"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.0,
        "bold": True
    },
    {
        "id": "82d48823c0a14a8a8b0476ab7a69b52f",
        "parent": "408bd380118a4eec980e17767adcf029",
        "order": 113,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6309,
                    "y1": 11659,
                    "x2": 11099,
                    "y2": 14259,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 5,
            "hash": "ec9ab330fc91bb94beb560195f5428303fa53fa1"
        },
        "content": [
            "Quantitative Analysis.In Table 1,we report the quantita-tive results for motion-guided stylization,where the motionserves as style input while text prompt as content one.Ourmethod outperforms three baseline approaches [1,23,71]inall metrics.Specifically,we achieve a 5.23%improvementin SRA while maintaining competitive performance in FIDcompared to the best baseline [71].It demonstrates that ourmethod,by effectively utilizing style-content cross fusion,generates motions that better align with the style referencewhile maintaining content integrity,showcasing the strengthof our design in balancing style and content preservation."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.0,
        "bold": False
    },
    {
        "id": "0bc6f405e92c4a5eb4cca98085d20257",
        "parent": "408bd380118a4eec980e17767adcf029",
        "order": 114,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6039,
                    "y1": 14670,
                    "x2": 6149,
                    "y2": 14809,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 5,
            "hash": "c1dfd96eea8cc2b62785275bca38ac261256e278"
        },
        "content": [
            "6"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.0,
        "bold": False
    },
    {
        "id": "8d3b016ae22c4859952788e3f4b3ef29",
        "parent": "408bd380118a4eec980e17767adcf029",
        "order": 115,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1630,
                    "y1": 1479,
                    "x2": 11210,
                    "y2": 3120,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 6,
            "hash": "b65f5284a1c58dd58f277aec1ba21e43e38cb46d"
        },
        "content": [
            "Text-guided StylizationFirstLastStyle InterpolationFirstLastFrameFrameFrameFrameInput Content TextInput Content Text\"A person is walking\"A person is walkingA person is walking.\"\"A person is walking.\"in a circle.\"in a circle.\"Input Style TextInput Style Text\"Depressed\"\"Heavyset\"Crouched\"\"Balance\"+\"Drunk\"\"Elated\"+\"Balance\"\"Rushed\"+“OnPhoneLeft'Stylized Motion from StyleMotifStylized Motion from StyleMotif"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": True
    },
    {
        "id": "642cf8d7c6bd42a0b9b14df770645ded",
        "parent": "408bd380118a4eec980e17767adcf029",
        "order": 116,
        "dst_type": "image",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1110,
                    "y1": 3069,
                    "x2": 11229,
                    "y2": 4119,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 6,
            "hash": "32be62d807cd4219bc557a24c3784f05a1c92060"
        },
        "content": [
            "http://kna-common.ks3-cn-beijing.ksyuncs.com/insight-ai/doc-parser/3415c85d08a74f49af01ffbc48a8913d_24.png?Signature=rms4YSQ2NNcwgjM77mP7utYUP3A%3D&Expires=3243326895&KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb"
        ],
        "image_pixel": [
            1011,
            105
        ],
        "mark": None,
        "font_size": -1,
        "bold": False
    },
    {
        "id": "f8d4baabe5c249a39a29119f9ca6e8f5",
        "parent": "408bd380118a4eec980e17767adcf029",
        "order": 117,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1129,
                    "y1": 4279,
                    "x2": 6209,
                    "y2": 4889,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 6,
            "hash": "99914884a1833632b667038bf80cc6d7f91ae24c"
        },
        "content": [
            "Figure 4.Qualitative Results of Text-Guided Stylization.Our modelseamlessly integrates textual style descriptions with content,producingvisually coherent and stylistically consistent results."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": True
    },
    {
        "id": "66af83e8c9524b7783088e26c876ee94",
        "parent": "408bd380118a4eec980e17767adcf029",
        "order": 118,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6320,
                    "y1": 4289,
                    "x2": 11220,
                    "y2": 4909,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 6,
            "hash": "002dd472307b8cc0452eab2dd45d817bda6bc61d"
        },
        "content": [
            "Figure 5.Qualitative Results of Style Interpolation.Our modelblends multiple style inputs while preserving content integrity,demonstrating effective style-content fusion of our model"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": True
    },
    {
        "id": "ac6dc2e8c92c468590db3a7406c99762",
        "parent": "408bd380118a4eec980e17767adcf029",
        "order": 119,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1229,
                    "y1": 5279,
                    "x2": 10899,
                    "y2": 8130,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 6,
            "hash": "54ca9e97ff008d86b3f2d8c7d83d78d6bbf0f88a"
        },
        "content": [
            "FirstLastMulti-modal Motion StylizationFrameFrameMulti-modal Input StyleInput Conten Text“Rushed”\"A person is walking.\"[Audio of Chicken][Image of Duck][Video of Dinosaur][Text Expression]Stylized Motionfrom StyleMotif"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": True
    },
    {
        "id": "aefe2802ff3842e684a970aa3f10c592",
        "parent": "408bd380118a4eec980e17767adcf029",
        "order": 120,
        "dst_type": "image",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 5139,
                    "y1": 5959,
                    "x2": 6509,
                    "y2": 7019,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 6,
            "hash": "dcdb994f2212b6c439d9f1bca12c21b7063188a7"
        },
        "content": [
            "http://kna-common.ks3-cn-beijing.ksyuncs.com/insight-ai/doc-parser/63553b5e06d14d8793b629f626e8fa2c_21.png?Signature=MCYALwhwBB%2Bil5%2FThLZTu0R9oPo%3D&Expires=3243326894&KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb"
        ],
        "image_pixel": [
            137,
            106
        ],
        "mark": None,
        "font_size": -1,
        "bold": False
    },
    {
        "id": "2b2a1726d27b41988952907404701783",
        "parent": "408bd380118a4eec980e17767adcf029",
        "order": 121,
        "dst_type": "image",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 7209,
                    "y1": 5959,
                    "x2": 8609,
                    "y2": 7009,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 6,
            "hash": "5c19013763c6a28c21ffaa699cac1d057775204d"
        },
        "content": [
            "http://kna-common.ks3-cn-beijing.ksyuncs.com/insight-ai/doc-parser/9735173ce83949909637af2f77ed3390_23.png?Signature=xjJUdVXd%2B2wFon6QUtjkVM7tBig%3D&Expires=3243326894&KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb"
        ],
        "image_pixel": [
            140,
            105
        ],
        "mark": None,
        "font_size": -1,
        "bold": False
    },
    {
        "id": "5630094f17b747d79b73ca7728567bac",
        "parent": "408bd380118a4eec980e17767adcf029",
        "order": 122,
        "dst_type": "image",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 3270,
                    "y1": 6029,
                    "x2": 4130,
                    "y2": 6889,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 6,
            "hash": "e4e9932d5f171f897c240de982e36bf277291944"
        },
        "content": [
            "http://kna-common.ks3-cn-beijing.ksyuncs.com/insight-ai/doc-parser/179ce827ba15470a8859361a13681d4e_22.png?Signature=d78dWc5RlTazWosZOMjDNOlFK6Y%3D&Expires=3243326893&KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb"
        ],
        "image_pixel": [
            86,
            86
        ],
        "mark": None,
        "font_size": -1,
        "bold": False
    },
    {
        "id": "c041889341d64e23830a0146a056340f",
        "parent": "408bd380118a4eec980e17767adcf029",
        "order": 123,
        "dst_type": "image",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 2630,
                    "y1": 7269,
                    "x2": 4719,
                    "y2": 8529,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 6,
            "hash": "fbd1f1643c40f8ab09a36d0882277a30f1688979"
        },
        "content": [
            "http://kna-common.ks3-cn-beijing.ksyuncs.com/insight-ai/doc-parser/1f52c37e8103454cb047176698c04538_20.png?Signature=9vsI%2BgeS3BD98mltEpHCpJ4wKaA%3D&Expires=3243326894&KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb"
        ],
        "image_pixel": [
            208,
            126
        ],
        "mark": None,
        "font_size": -1,
        "bold": False
    },
    {
        "id": "1ef3031a82324466b093df785d2eebca",
        "parent": "408bd380118a4eec980e17767adcf029",
        "order": 124,
        "dst_type": "image",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 4749,
                    "y1": 7259,
                    "x2": 8960,
                    "y2": 8529,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 6,
            "hash": "be76cac0d541b95834e371d95c328656a81ff427"
        },
        "content": [
            "http://kna-common.ks3-cn-beijing.ksyuncs.com/insight-ai/doc-parser/4d366846d35b44f28e11d05e1ac88b4d_25.png?Signature=nWP56%2B1ocyDtyyoDnI42PwIgHBo%3D&Expires=3243326894&KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb"
        ],
        "image_pixel": [
            422,
            127
        ],
        "mark": None,
        "font_size": -1,
        "bold": False
    },
    {
        "id": "02421d7328e04189bf74efd22378c45d",
        "parent": "408bd380118a4eec980e17767adcf029",
        "order": 125,
        "dst_type": "image",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 8960,
                    "y1": 7290,
                    "x2": 11069,
                    "y2": 8529,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 6,
            "hash": "f0f130aefb3e07b032214e1c01c072b4f56f5cec"
        },
        "content": [
            "http://kna-common.ks3-cn-beijing.ksyuncs.com/insight-ai/doc-parser/b9fae65101c94480bbfbf9999bad3b9a_19.png?Signature=Kuif8koJaw2elzIAZJTXhHE9LAQ%3D&Expires=3243326894&KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb"
        ],
        "image_pixel": [
            210,
            123
        ],
        "mark": None,
        "font_size": -1,
        "bold": False
    },
    {
        "id": "3bd456d2890d45f08ba153ba7fc8bb6a",
        "parent": "408bd380118a4eec980e17767adcf029",
        "order": 126,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1129,
                    "y1": 8720,
                    "x2": 11050,
                    "y2": 9110,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 6,
            "hash": "c4f0723ff3b772d6fc6663be02b925e3597691ea"
        },
        "content": [
            "Figure 6.Qualitative Results of Multi-Modal Motion Stylization.Our model generates stylized motions guided by diverse modalities(e.g.,text,image,video,audio),effectively transferring style while preserving content integrity."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": True
    },
    {
        "id": "dc3e518519cf452c8b173e548d41282c",
        "parent": "408bd380118a4eec980e17767adcf029",
        "order": 127,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1140,
                    "y1": 9580,
                    "x2": 5930,
                    "y2": 11930,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 6,
            "hash": "5dbe61f97bfad3e20e9c629807b209f96d046531"
        },
        "content": [
            "Qualitative Analysis.In Figure 3,we show some qualita-tive results of motion-guided stylization from our model andbaseline [71].Compared with the baseline,our model pro-duces more cohesive stylized motions,with better alignmentof style and content.For instance,in the provided examples,SMooDi fails to maintain the ‘circular'trajectory or ‘hop'action specified by the content or cannot accurately reflectthe intended style,'phone on the left'.This indicates thatour approach more effectively integrates style and content,resulting in more realistic and consistent stylized motions."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.5,
        "bold": False
    },
    {
        "id": "464ddb88498f48ce9056e4dfb07fe42b",
        "parent": "38743c4e4c214252bd5061750334377f",
        "order": 128,
        "dst_type": "text",
        "attributes": {
            "level": 3,
            "position": {
                "bbox": {
                    "x1": 1149,
                    "y1": 12240,
                    "x2": 3689,
                    "y2": 12439,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 6,
            "hash": "36dd2a07f45a3b5fdb67bf68272b85351d93dfa4"
        },
        "content": [
            "4.3.Text-guided Stylization"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.5,
        "bold": True
    },
    {
        "id": "5ddb49469a0c4595be731bfb7511a3e7",
        "parent": "464ddb88498f48ce9056e4dfb07fe42b",
        "order": 129,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1149,
                    "y1": 12620,
                    "x2": 5930,
                    "y2": 14259,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 6,
            "hash": "ab93bd1f5cd74a3083974077cb205fa86fe79675"
        },
        "content": [
            "Quantitative Analysis.For the text-guided stylizationtask,we use the HumanML3D[13]dataset for motion con-tent and employ the single text label[35]as the style con-trol.As reported in Table 1(Bottom),our method signifi-cantly outperforms the baseline,achieving 56.71%in SRA,compared to 4.82%for ‘ChatGPT+MLD'’.Moreover,ourmethod maintains competitive FID,balancing style reflec-"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.5,
        "bold": False
    },
    {
        "id": "e6e0df8a0a214d57b801e3739bc83dce",
        "parent": "464ddb88498f48ce9056e4dfb07fe42b",
        "order": 130,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6320,
                    "y1": 9590,
                    "x2": 11059,
                    "y2": 10490,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 6,
            "hash": "f3faf6125dd4b3b711b122071efe8432ab108922"
        },
        "content": [
            "tion with content preservation.This indicates that our design,which combines multi-modal alignment,help utilizes thestyle signal in the motion-text shared spaces and achievesignificant effectiveness in text-guided stylization."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "a3e58873777a4be5a167f43bdfc252cc",
        "parent": "464ddb88498f48ce9056e4dfb07fe42b",
        "order": 131,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6320,
                    "y1": 10880,
                    "x2": 11090,
                    "y2": 11069,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 6,
            "hash": "7536d08bf16c0b1ed864695c0d8709b5c5acbc6e"
        },
        "content": [
            "Qualitative Analysis.In Figure 4,we showcase qualita-"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.5,
        "bold": False
    },
    {
        "id": "66fe05120fe541e3837f944a1434e2e7",
        "parent": "464ddb88498f48ce9056e4dfb07fe42b",
        "order": 132,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6320,
                    "y1": 11119,
                    "x2": 11059,
                    "y2": 11310,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 6,
            "hash": "e0c052c9622fddf40aa9c5b455270f2a6b4f5795"
        },
        "content": [
            "tive results for text-guided stylization,where our model also"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.5,
        "bold": False
    },
    {
        "id": "fb4bbd22552f4f5c916550bbdd243143",
        "parent": "464ddb88498f48ce9056e4dfb07fe42b",
        "order": 133,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6320,
                    "y1": 11379,
                    "x2": 11059,
                    "y2": 11560,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 6,
            "hash": "4b484bf1a498b683ca8d130a23b993baa55cfd8c"
        },
        "content": [
            "demonstrates strong capability in harmonizing style and con-"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "c30c0677237b4ecfb4309e8329636aef",
        "parent": "464ddb88498f48ce9056e4dfb07fe42b",
        "order": 134,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6330,
                    "y1": 11609,
                    "x2": 10969,
                    "y2": 11800,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 6,
            "hash": "7677f0f0cecfb26c8c33a2e6ba43069aef871e44"
        },
        "content": [
            "tent,generating high-quality and visually coherent results."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.5,
        "bold": False
    },
    {
        "id": "83c54f31dcb24898ba5fa07b3ef56906",
        "parent": "38743c4e4c214252bd5061750334377f",
        "order": 135,
        "dst_type": "text",
        "attributes": {
            "level": 3,
            "position": {
                "bbox": {
                    "x1": 6330,
                    "y1": 12030,
                    "x2": 8779,
                    "y2": 12230,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 6,
            "hash": "20b6b3a423572530254db8c8db4bd27fc25d3ef0"
        },
        "content": [
            "4.4.Motion Style Transfer"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.5,
        "bold": True
    },
    {
        "id": "f97c6683fffd447f85459d809919eba9",
        "parent": "83c54f31dcb24898ba5fa07b3ef56906",
        "order": 136,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6320,
                    "y1": 12400,
                    "x2": 11090,
                    "y2": 14240,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 6,
            "hash": "c692eaf52754c7012087c71c6c4163e4d5fcef1f"
        },
        "content": [
            "For the motion style transfer task,we use the Hu-manML3D [13]dataset for motion content and the100STYLE dataset [35]for motion styles.As shown in Ta-ble 2,our method outperforms the baseline models across allkey metrics.Specifically,we achieve a 3.66%improvementin SRA,indicating better style reflection,and a 0.207 reduc-tion in FID reflecting improved realism.These demonstratethat our framework not only improves the alignment between"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "df2d67b259694126b7963715e771f374",
        "parent": "83c54f31dcb24898ba5fa07b3ef56906",
        "order": 137,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6060,
                    "y1": 14649,
                    "x2": 6149,
                    "y2": 14799,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 6,
            "hash": "902ba3cda1883801594b6e1b452790cc53948fda"
        },
        "content": [
            "7"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "2aa98dafafba490aa2aaa60c15b8c96d",
        "parent": "83c54f31dcb24898ba5fa07b3ef56906",
        "order": 138,
        "dst_type": "image",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1219,
                    "y1": 1420,
                    "x2": 3900,
                    "y2": 3139,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 7,
            "hash": "b0b0a5d69ac77a33d196a778147f29b42b2c3baa"
        },
        "content": [
            "http://kna-common.ks3-cn-beijing.ksyuncs.com/insight-ai/doc-parser/ccc284215def4ba7b239a777034da552_26.png?Signature=Y7h2%2FlGc4xaNKLc1CkkjFkEEGbU%3D&Expires=3243326894&KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb"
        ],
        "image_pixel": [
            269,
            171
        ],
        "mark": None,
        "font_size": -1,
        "bold": False
    },
    {
        "id": "373ce43ce4e145bf926b9777da56b41c",
        "parent": "83c54f31dcb24898ba5fa07b3ef56906",
        "order": 139,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 2419,
                    "y1": 3170,
                    "x2": 2730,
                    "y2": 3279,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 7,
            "hash": "794f65e9d2064ab6bfab0fa351dcff2763db23d3"
        },
        "content": [
            "Ratio"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 5.5,
        "bold": False
    },
    {
        "id": "cf2ed26acaa14eceb86ea90f1e56fce1",
        "parent": "83c54f31dcb24898ba5fa07b3ef56906",
        "order": 140,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1289,
                    "y1": 3519,
                    "x2": 3900,
                    "y2": 4579,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 7,
            "hash": "73730844e8d15f5e0146a65db2730ca401a2caca"
        },
        "content": [
            "Figure 7.Ablation Study on Scal-ing Ratioγin Eq.5.We report bothSRA and FID to show the impact ofthe scaling ratio on both stylizationand content preservation."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.5,
        "bold": True
    },
    {
        "id": "4afc6bc20c7b45828141aa45bee10591",
        "parent": "83c54f31dcb24898ba5fa07b3ef56906",
        "order": 141,
        "dst_type": "table",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 4260,
                    "y1": 1410,
                    "x2": 10890,
                    "y2": 3770,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 7,
            "hash": "918f09b4e41e233588b9b7b4c12582308cb453d0"
        },
        "content": [
            "<table><tr><td rowspan=\"1\" colspan=\"8\">Sryle Encoder Pre-training Strategy</td></tr><tr><td rowspan=\"1\" colspan=\"1\">HumanML3D </td><td rowspan=\"1\" colspan=\"1\">100STYLE</td><td rowspan=\"1\" colspan=\"1\">SRA↑</td><td rowspan=\"1\" colspan=\"1\">FID↓</td><td rowspan=\"1\" colspan=\"1\">MM Dist↓</td><td rowspan=\"1\" colspan=\"1\">R-Precision↑</td><td rowspan=\"1\" colspan=\"1\">Diversity→</td><td rowspan=\"1\" colspan=\"1\">Foot Skate Ratio↓]</td></tr><tr><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\">√</td><td rowspan=\"1\" colspan=\"1\">76.73</td><td rowspan=\"1\" colspan=\"1\">1.788</td><td rowspan=\"1\" colspan=\"1\">4.349</td><td rowspan=\"1\" colspan=\"1\">0.571</td><td rowspan=\"1\" colspan=\"1\">7.505</td><td rowspan=\"1\" colspan=\"1\">0.101</td></tr><tr><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\"></td><td rowspan=\"1\" colspan=\"1\">76.58</td><td rowspan=\"1\" colspan=\"1\">1.635</td><td rowspan=\"1\" colspan=\"1\">4.458</td><td rowspan=\"1\" colspan=\"1\">0.572</td><td rowspan=\"1\" colspan=\"1\">8.534</td><td rowspan=\"1\" colspan=\"1\">0.109</td></tr><tr><td rowspan=\"1\" colspan=\"1\">√</td><td rowspan=\"1\" colspan=\"1\">√</td><td rowspan=\"1\" colspan=\"1\">77.65</td><td rowspan=\"1\" colspan=\"1\">1.551</td><td rowspan=\"1\" colspan=\"1\">4.354</td><td rowspan=\"1\" colspan=\"1\">0.586</td><td rowspan=\"1\" colspan=\"1\">7.567</td><td rowspan=\"1\" colspan=\"1\">0.097</td></tr><tr><td rowspan=\"1\" colspan=\"8\">Text in Multi-modal Alignment</td></tr><tr><td rowspan=\"1\" colspan=\"2\">Textual Expression</td><td rowspan=\"1\" colspan=\"1\">SRA个</td><td rowspan=\"1\" colspan=\"1\">FID↓</td><td rowspan=\"1\" colspan=\"1\">MM Dist↓</td><td rowspan=\"1\" colspan=\"1\">R-Precision↑</td><td rowspan=\"1\" colspan=\"1\">Diversiy→</td><td rowspan=\"1\" colspan=\"1\">Foot Skate Ratio↓]</td></tr><tr><td rowspan=\"1\" colspan=\"2\">Brief Description</td><td rowspan=\"1\" colspan=\"1\">76.84</td><td rowspan=\"1\" colspan=\"1\">1.580</td><td rowspan=\"1\" colspan=\"1\">4.378</td><td rowspan=\"1\" colspan=\"1\">0.578</td><td rowspan=\"1\" colspan=\"1\">7.251</td><td rowspan=\"1\" colspan=\"1\">0.099</td></tr><tr><td rowspan=\"1\" colspan=\"2\">Detaled Description</td><td rowspan=\"1\" colspan=\"1\">75.25</td><td rowspan=\"1\" colspan=\"1\">1.622</td><td rowspan=\"1\" colspan=\"1\">4.419</td><td rowspan=\"1\" colspan=\"1\">0.563</td><td rowspan=\"1\" colspan=\"1\">7.764</td><td rowspan=\"1\" colspan=\"1\">0.102</td></tr><tr><td rowspan=\"1\" colspan=\"2\">Single Text Label</td><td rowspan=\"1\" colspan=\"1\">77.65</td><td rowspan=\"1\" colspan=\"1\">1.551</td><td rowspan=\"1\" colspan=\"1\">4.354</td><td rowspan=\"1\" colspan=\"1\">0.586</td><td rowspan=\"1\" colspan=\"1\">7.567</td><td rowspan=\"1\" colspan=\"1\">0.097</td></tr></table>"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": -1,
        "bold": False
    },
    {
        "id": "30dec813df5e45caa6abc504a78c6d89",
        "parent": "83c54f31dcb24898ba5fa07b3ef56906",
        "order": 142,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 4260,
                    "y1": 4000,
                    "x2": 10920,
                    "y2": 4620,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 7,
            "hash": "de9d921f1b3a1f4142ba7d2e0a8bc8794b7462d2"
        },
        "content": [
            "Table 3.Ablation Study on Style Encoder Pre-training Strategies and Text Expressionfor Multi-modal Alignment'w.HumanML3D'andw.100STYLE’denote pre-trainingwith HumanML3D[13]and 100STYLE[35]data respectively."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.5,
        "bold": False
    },
    {
        "id": "4a86db3926b141eb8ca8ceb9cf2419f0",
        "parent": "83c54f31dcb24898ba5fa07b3ef56906",
        "order": 143,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1140,
                    "y1": 5090,
                    "x2": 5890,
                    "y2": 6470,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 7,
            "hash": "f6166357cd229f708958b17297a906d86d88d300"
        },
        "content": [
            "style and content but also provides significant advantages fordownstream tasks like motion style transfer with efficientstyle-content cross fusion.By maintaining content integritywhile seamlessly integrating style,STYLEMOTIF ensuresmore realistic and dynamically consistent stylized motions,which is critical for high-quality motion style transfer."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.5,
        "bold": False
    },
    {
        "id": "f8a24aedaa0c402e873149140c38b1ab",
        "parent": "38743c4e4c214252bd5061750334377f",
        "order": 144,
        "dst_type": "text",
        "attributes": {
            "level": 3,
            "position": {
                "bbox": {
                    "x1": 1149,
                    "y1": 6709,
                    "x2": 4530,
                    "y2": 6900,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 7,
            "hash": "bef59017f8ade4e0e5ef415fa84afefb4ae2b8a4"
        },
        "content": [
            "4.5.Multi-Modal Motion Stylization"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.5,
        "bold": True
    },
    {
        "id": "e8d4049fb017428db887b3189f5a79e9",
        "parent": "f8a24aedaa0c402e873149140c38b1ab",
        "order": 145,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1140,
                    "y1": 7070,
                    "x2": 5919,
                    "y2": 10829,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 7,
            "hash": "b24375c26dae3b94046c321049069266043642fe"
        },
        "content": [
            "With the power of the aligned multi-modal space,our modelsupports stylization guided by a variety of modalities,in-cluding motion,text,image,video,audio.Here,we utilizethe input style feature to retrieve the corresponding motionfeatures as the style condition,while the content input is pro-vided as text prompt.The model then generates a stylizedmotion that incorporates the style from the input modalitywhile maintaining the content integrity as specified by thetext prompt.We showcase several examples of multi-modalmotion stylization in Figure 6,where different modalitiesguide the motion generation.For instance,when a text con-tent“A person is walking.”is provided alongside an imageof a duck as style input,the model retrieves a relevant motionfeature and blends the content with the style of 'Duckfoot'.As shown,the style from various inputs (text,image,video,audio)is effectively transferred to the generated motion."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.5,
        "bold": False
    },
    {
        "id": "ad085047237c4b04ad52d99d4d3e0750",
        "parent": "38743c4e4c214252bd5061750334377f",
        "order": 146,
        "dst_type": "text",
        "attributes": {
            "level": 3,
            "position": {
                "bbox": {
                    "x1": 1140,
                    "y1": 11059,
                    "x2": 3289,
                    "y2": 11270,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 7,
            "hash": "7323e30660d0a6b1f93e8c5767e66c516f919747"
        },
        "content": [
            "4.6.Style Interpolation"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.5,
        "bold": True
    },
    {
        "id": "6f2a9d669759472db2c0fcdfba7fe9b7",
        "parent": "ad085047237c4b04ad52d99d4d3e0750",
        "order": 147,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1140,
                    "y1": 11420,
                    "x2": 5930,
                    "y2": 14240,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 7,
            "hash": "f940a948f2120a3cc7c5063a229e93230386b4f1"
        },
        "content": [
            "Leveraging the aligned multi-modal space,our model en-ables text-guided style interpolation.Given one content textalong with at least two style style texts,our model generatesa motion that combines the characteristics of all input styles.When style texts are provided,we retrieve the most similarstyle motion features from the shared space.These featuresare then combined by weighted summation.The combinedstyle features are fused with the content features using theproposed style-content cross fusion.In Figure 5,we show-case some qualitative results of style interpolation in,wherethe generated motions successfully blend the characteristicsof both styles while maintaining the content's integrity."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.5,
        "bold": False
    },
    {
        "id": "da704b964592413385f0b171c4252ddb",
        "parent": "38743c4e4c214252bd5061750334377f",
        "order": 148,
        "dst_type": "text",
        "attributes": {
            "level": 3,
            "position": {
                "bbox": {
                    "x1": 6320,
                    "y1": 5080,
                    "x2": 8120,
                    "y2": 5269,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 7,
            "hash": "3f21680d4dee27e0d462e2fe29efb8052c473d00"
        },
        "content": [
            "4.7.Ablation Study"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.5,
        "bold": True
    },
    {
        "id": "5bf6863bbd9e4ee9b906b054038e7fcb",
        "parent": "da704b964592413385f0b171c4252ddb",
        "order": 149,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6320,
                    "y1": 5419,
                    "x2": 11099,
                    "y2": 7789,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 7,
            "hash": "2d9dd6d1311abea01d6797663f10a0af9de251e7"
        },
        "content": [
            "Style Encoder Pre-training.In Table 3,we investigatethe impact of different pre-training strategies for the styleencoder.Our results show that pre-training on both Hu-manML3D [13]and 100STYLE[35]yields the best per-formance.Compared to pre-training solely on either one,the combined training on both datasets provides the styleencoder with a richer set of prior knowledge.This enablesthe model to effectively balance content preservation andstyle reflection,leveraging the diverse characteristics of bothdatasets to enhance the overall stylization quality."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.5,
        "bold": False
    },
    {
        "id": "********************************",
        "parent": "da704b964592413385f0b171c4252ddb",
        "order": 150,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6320,
                    "y1": 8159,
                    "x2": 11099,
                    "y2": 10030,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 7,
            "hash": "9cc2e0652bbcec0a091e3e33b5333b0bdfced433"
        },
        "content": [
            "Cross Normalization Scaling Ratio.In Figure 7,we ex-amine the effect of different scaling ratios y in Eq.5 onthe stylization performance.As shown,the model achievesthe best performance withγ=0.6,striking an optimal bal-ance between style reflection and content preservation.Thechoice of scaling ratio influences both SRA and FID,wherethe optimal value improves stylization without sacrificingcontent integrity."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.5,
        "bold": False
    },
    {
        "id": "0c260c8b207e45098b8b2bb1dd477286",
        "parent": "da704b964592413385f0b171c4252ddb",
        "order": 151,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6279,
                    "y1": 10410,
                    "x2": 11099,
                    "y2": 12519,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 7,
            "hash": "4058f504bf140038dd2ff53d911b15b995c44bc5"
        },
        "content": [
            "Text Expression for Multi-modal Alignment.In Table 3,we also explored different text representations for contrastivelearning alignment.Specifically,we tested single labels(e.g.,“Old”),brief descriptions(e.g.,“An old person”),and moredetailed descriptions(e.g.,“An old person is moving slowand stiff\").Our experiments show that using a single labelproduced the best results,providing a clearer and more con-cise style signal for the model while avoiding unnecessarycomplexity in the text representation."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.5,
        "bold": False
    },
    {
        "id": "6034282f58a84093b7fa7d5126e86673",
        "parent": "38743c4e4c214252bd5061750334377f",
        "order": 152,
        "dst_type": "text",
        "attributes": {
            "level": 3,
            "position": {
                "bbox": {
                    "x1": 6320,
                    "y1": 12729,
                    "x2": 8479,
                    "y2": 12940,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 7,
            "hash": "27adddcc3047fdae30c2a4e1e3c38423950b38fa"
        },
        "content": [
            "4.8.Efficiency Analysis"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.5,
        "bold": True
    },
    {
        "id": "0974310e0ee346418e59fba9770e19b3",
        "parent": "6034282f58a84093b7fa7d5126e86673",
        "order": 153,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6309,
                    "y1": 13090,
                    "x2": 11110,
                    "y2": 14240,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 7,
            "hash": "609e021e19f54f80039b125f48225172f68b9460"
        },
        "content": [
            "We compare the efficiency of our method with SMooDi [71]in terms of learnable parameters and inference speed(sec-onds per sample),under the same diffusion step setting.As shown in Table 4,our model reduces the number oftrainable parameters by 43.9%,significantly easing training."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.5,
        "bold": False
    },
    {
        "id": "c78a5aaa27604007ad6577a89b1ef30d",
        "parent": "6034282f58a84093b7fa7d5126e86673",
        "order": 154,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6049,
                    "y1": 14670,
                    "x2": 6139,
                    "y2": 14809,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 7,
            "hash": "fe5dbbcea5ce7e2988b8c69bcfdfde8904aabc1f"
        },
        "content": [
            "8"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 7.0,
        "bold": False
    },
    {
        "id": "ca5b02ee47d74117be6e18f537e358f7",
        "parent": "c78a5aaa27604007ad6577a89b1ef30d",
        "order": 155,
        "dst_type": "table",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1449,
                    "y1": 1559,
                    "x2": 5530,
                    "y2": 2849,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 8,
            "hash": "f2fb3a19a2b81d0742b830eb93821692d3c3d139"
        },
        "content": [
            "<table><tr><td rowspan=\"1\" colspan=\"1\">Method</td><td rowspan=\"1\" colspan=\"1\">OverallParameter</td><td rowspan=\"1\" colspan=\"1\">LearnableParameter</td><td rowspan=\"1\" colspan=\"1\">InferenceTime</td></tr><tr><td rowspan=\"1\" colspan=\"1\">SMooDi [71]</td><td rowspan=\"1\" colspan=\"1\">468M</td><td rowspan=\"1\" colspan=\"1\">13.9M</td><td rowspan=\"1\" colspan=\"1\">4.0s</td></tr><tr><td rowspan=\"1\" colspan=\"1\">StyleMotif</td><td rowspan=\"1\" colspan=\"1\">462M</td><td rowspan=\"1\" colspan=\"1\">7.8M</td><td rowspan=\"1\" colspan=\"1\">3.1s</td></tr><tr><td rowspan=\"1\" colspan=\"1\">Improvement</td><td rowspan=\"1\" colspan=\"1\">1.3%</td><td rowspan=\"1\" colspan=\"1\">43.9%</td><td rowspan=\"1\" colspan=\"1\">22.5%</td></tr></table>"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": -1,
        "bold": False
    },
    {
        "id": "62f98abe630f4db68b7032bbbd9131ab",
        "parent": "c78a5aaa27604007ad6577a89b1ef30d",
        "order": 156,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1140,
                    "y1": 3020,
                    "x2": 5879,
                    "y2": 3410,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 8,
            "hash": "9ad0e8ed9807ec793352e00f0f208c7da667373f"
        },
        "content": [
            "Table 4.Efficiency Comparison.For inference time,we report theaverage time cost (s)per sample on a single NVIDIA A100 GPU."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.5,
        "bold": True
    },
    {
        "id": "269812ff36184affac5ec3a0ef319823",
        "parent": "c78a5aaa27604007ad6577a89b1ef30d",
        "order": 157,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1149,
                    "y1": 3909,
                    "x2": 5930,
                    "y2": 6259,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 8,
            "hash": "ca8423405bb72e1b2018b2ea426300390ea838bf"
        },
        "content": [
            "While the overall parameter count remains comparable,oursingle-branch design boosts inference speed by 22.5%,out-performing SMooDi's dual-branch structure.Notably,ourstyle encoder is deeper and thus accounts for most of thecomputational cost,but our single-banch design allows forhighly parallelizable operations.In contrast,SMooDi's dual-branch approach requires output summation after each block,limiting parallel efficiency despite fewer overall parameters.Consequently,our method achieves faster practical inferenceand more efficient training."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.5,
        "bold": False
    },
    {
        "id": "4ce5e99a3710425497b8fc81f345a7c6",
        "parent": "c78a5aaa27604007ad6577a89b1ef30d",
        "order": 158,
        "dst_type": "text",
        "attributes": {
            "level": 1,
            "position": {
                "bbox": {
                    "x1": 1140,
                    "y1": 6530,
                    "x2": 2540,
                    "y2": 6709,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 8,
            "hash": "03f92e8ceabc1ba850d4e225574e1af16ae1c37d"
        },
        "content": [
            "5.Conclusion"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.5,
        "bold": True
    },
    {
        "id": "0dd60e0f447a4a4ab31875ce8f2e60d6",
        "parent": "4ce5e99a3710425497b8fc81f345a7c6",
        "order": 159,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1140,
                    "y1": 6949,
                    "x2": 5930,
                    "y2": 12160,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 8,
            "hash": "e5a9f5ba78044680e8b30e7e2f3e6263dadd3864"
        },
        "content": [
            "In this work,we introduce STYLEMOTIF,a novel StylizedMotion Diffusion model capable of generating motion con-ditioned on both content and style from multiple modalities.Unlike prior approaches that either focus on motion genera-tion across various content types or style transfer between se-quences,STYLEMOTIF effectively synthesizes motion whileincorporating stylistic cues from multi-modal inputs,includ-ing text,image,video,and audio.To achieve this,we intro-duce a style-content cross fusion mechanism and align a styleencoder with a pre-trained multi-modal model,ensuring thatthe generated motion accurately captures the reference stylewhile maintaining realism.Through extensive experimentsacross diverse applications,we demonstrate that STYLEMo-TIF outperforms existing methods in stylized motion genera-tion,producing high-quality,realistic results that faithfullyadhere to the given style references.Moreover,our modelexhibits emergent capabilities for multi-modal motion styl-ization,enabling richer and more nuanced motion synthesis.These findings indicate the potential of STYLEMOTIF inadvancing stylized motion generation and open new avenuesfor future research in multi-modal-driven motion synthesisand style-aware generative models."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.5,
        "bold": True
    },
    {
        "id": "863642a7eb404aef80ab2608d7bdd14b",
        "parent": "c78a5aaa27604007ad6577a89b1ef30d",
        "order": 160,
        "dst_type": "text",
        "attributes": {
            "level": 1,
            "position": {
                "bbox": {
                    "x1": 1159,
                    "y1": 12460,
                    "x2": 4189,
                    "y2": 12610,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 8,
            "hash": "773dfd1b24ca5f0d66d820668b93058620bb661e"
        },
        "content": [
            "Limitations and Future Work"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.5,
        "bold": True
    },
    {
        "id": "c49d0e3080234cceaf6c6581b9f8b3b1",
        "parent": "863642a7eb404aef80ab2608d7bdd14b",
        "order": 161,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1140,
                    "y1": 12850,
                    "x2": 5909,
                    "y2": 14259,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 8,
            "hash": "90d189e0504734c718a587d58850629830f3aa3e"
        },
        "content": [
            "The current limitations mainly exist in the relatively limitedavailability of style motion-text data,which constrains themodel's ability to fully generalize across a wide variety ofmotion styles.Future work is to explore ways to unlock thepotential of existing data by enhancing generalization withinthe current datasets,further extending the capabilities of the"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.5,
        "bold": False
    },
    {
        "id": "a25a94517cdd4fed89795870a8c61ec5",
        "parent": "863642a7eb404aef80ab2608d7bdd14b",
        "order": 162,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6309,
                    "y1": 1479,
                    "x2": 11059,
                    "y2": 1870,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 8,
            "hash": "a4225d75bcf2346d583ec4bf16aa7e61372a49c6"
        },
        "content": [
            "model to generate more diverse and complex motions,evenwithin the constraints of limited data."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.5,
        "bold": False
    },
    {
        "id": "ffda1eac18ff4c20a18623bd5584929b",
        "parent": "c78a5aaa27604007ad6577a89b1ef30d",
        "order": 163,
        "dst_type": "text",
        "attributes": {
            "level": 1,
            "position": {
                "bbox": {
                    "x1": 6320,
                    "y1": 2200,
                    "x2": 7430,
                    "y2": 2360,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 8,
            "hash": "5d20d0fee3b91643dd8d272ac33d01ca95179d82"
        },
        "content": [
            "References"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.5,
        "bold": True
    },
    {
        "id": "f0366b4d7da54e11adc1c868e98e4741",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 164,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6420,
                    "y1": 2599,
                    "x2": 11080,
                    "y2": 3170,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 8,
            "hash": "25a9f99b8054d4b1331185d47d803ae55bbd60f9"
        },
        "content": [
            "[1]K.Aberman,Y.Weng,D.Lischinski,D.Cohen-Or,and B.Chen.Unpaired motion style transfer from video to animation.TOG,2020.2,6"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.5,
        "bold": False
    },
    {
        "id": "a20a3d87a5a946bb82a2bf69b80adf98",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 165,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6400,
                    "y1": 3260,
                    "x2": 11090,
                    "y2": 4110,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 8,
            "hash": "ef5e7ae5a76f876fbec76fb10fc76fbc0963226a"
        },
        "content": [
            "[2]Nefeli Andreou,Xi Wang,Victoria Fernández Abrevaya,Marie-Paule Cani,Yiorgos Chrysanthou,and Vicky Kalo-geiton.Lead:Latent realignment for human motion diffusion.arXiv preprint arXiv:2410.14508,2024.2"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.5,
        "bold": False
    },
    {
        "id": "ccf66a6b8e274f70a4c6676ad65ae4dd",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 166,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6400,
                    "y1": 4160,
                    "x2": 11080,
                    "y2": 4780,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 8,
            "hash": "0e9b4996384fd6b0bdaabd319b6c499cd6de057d"
        },
        "content": [
            "[3]Z.Cen,H.Pi,S.Peng,Z.Shen,M.Yang,S.Zhu,H.Bao,and X.Zhou.Generating human motion in 3d scenes fromtext descriptions.In CVPR,2024.2"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.5,
        "bold": False
    },
    {
        "id": "8c555e69e2594dd989653cba4097cd56",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 167,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6400,
                    "y1": 4840,
                    "x2": 11099,
                    "y2": 8149,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 8,
            "hash": "4ff60a56e25d5264594dfd2ec22dd73f09daf1cd"
        },
        "content": [
            "[4]Kexin Chen,Yuyang Du,Tao You,Mobarakol Islam,ZiyuGuo,Yueming Jin,Guangyong Chen,and Pheng-Ann Heng.Llm-assisted multi-teacher continual learning for visual ques-tion answering in robotic surgery.ICRA 2024,2024.3[5]L.H.Chen,S.Lu,A.Zeng,H.Zhang,B.Wang,R.Zhang,and L.Zhang.Motionllm:Understanding human behaviorsfrom human motions and videos.ArXiv,2024.2,3[6]Xin Chen,Biao Jiang,Wen Liu,Zilong Huang,Bin Fu,TaoChen,and Gang Yu.Executing your commands via motiondiffusion in latent space.In CVPR,2023.1,2,3,4,5[7]Sammy Christen,Shreyas Hampali,Fadime Sener,EdoardoRemelli,Tomas Hodan,Eric Sauser,Shugao Ma,and BugraTekin.Diffh2o:Diffusion-based synthesis of hand-objectinteractions from textual descriptions.In SIGGRAPH Asia2024 Conference Papers,2024.2"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.5,
        "bold": False
    },
    {
        "id": "bbcaa3e15b0c448faea1f9b8abe417bb",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 168,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6400,
                    "y1": 8229,
                    "x2": 11080,
                    "y2": 9050,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 8,
            "hash": "370351cb02e4174188c2540f4f737a36541129b7"
        },
        "content": [
            "[8]Wenxun Dai,Ling-Hao Chen,Jingbo Wang,Jinpeng Liu,Bo Dai,and Yansong Tang.Motionlcm:Real-time control-lable motion generation via latent consistency model.arXivpreprint arXiv:2404.19759,2024.2"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.5,
        "bold": False
    },
    {
        "id": "1fb674eb6d9740d6bf4ddc0fba2daacb",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 169,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6400,
                    "y1": 9130,
                    "x2": 11080,
                    "y2": 9960,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 8,
            "hash": "731835c90ba3a152648efad5b01bcc0f66d3302f"
        },
        "content": [
            "[9]Karan Desai and Justin Johnson.Virtex:Learning visualrepresentations from textual annotations.In Proceedings ofthe IEEE/CVF conference on computer vision and patternrecognition,pages 11162-11173,2021.3"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.5,
        "bold": False
    },
    {
        "id": "697f19ea569b40a5a275cf17ef64b31e",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 170,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6330,
                    "y1": 10030,
                    "x2": 11080,
                    "y2": 10630,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 8,
            "hash": "8e2150bf53e21c98af0d03b5b5a2e4e420990dee"
        },
        "content": [
            "[10]Han Fang,Pengfei Xiong,Luhui Xu,and Yu Chen.Clip2video:Mastering video-text retrieval via image clip.arXiv preprint arXiv:2106.11097,2021.3"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.5,
        "bold": False
    },
    {
        "id": "079c479cdfdd4241aa1df894d760e15c",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 171,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6330,
                    "y1": 10710,
                    "x2": 11090,
                    "y2": 11729,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 8,
            "hash": "0e79eae10d2e40ce6325a4b6f76883dc36b47a0f"
        },
        "content": [
            "[11]Peng Gao*,Jiaming Han*,Renrui Zhang*,Ziyi Lin,Shi-jie Geng,Aojun Zhou,Wei Zhang,Pan Lu,Conghui He,Xiangyu Yue,et al.Llama-adapter v2:Parameter-efficientvisual instruction model.arXiv preprint arXiv:2304.15010,2023.3"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.5,
        "bold": False
    },
    {
        "id": "974bf7c783d44788b87a88024bc3ab29",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 172,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6320,
                    "y1": 11829,
                    "x2": 11080,
                    "y2": 14199,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 8,
            "hash": "a9a04d0c3d071a42f2bf523647200f8ef2f820d7"
        },
        "content": [
            "[12]Rohit Girdhar,Alaaeldin El-Nouby,Zhuang Liu,MannatSingh,Kalyan Vasudev Alwala,Armand Joulin,and IshanMisra.Imagebind:One embedding space to bind them all.InProceedings of the IEEE/CVF conference on computer visionand pattern recognition,pages 15180-15190,2023.2,3,4,5[13]Chuan Guo,Shihao Zou,Xinxin Zuo,Sen Wang,Wei Ji,Xingyu Li,and Li Cheng.Generating diverse and natural 3dhuman motions from text.In CVPR,2022.2,4,5,6,7,8[14]Chuan Guo,Yuxuan Mu,Muhammad Gohar Javed,Sen Wang,and Li Cheng.Momask:Generative masked modeling of 3dhuman motions.In CVPR,2024.2,3"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.5,
        "bold": False
    },
    {
        "id": "4735e8379b8a45f08d010dee31d1fbc5",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 173,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6039,
                    "y1": 14649,
                    "x2": 6149,
                    "y2": 14799,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 8,
            "hash": "0ade7c2cf97f75d009975f4d720d1fa6c19f4897"
        },
        "content": [
            "9"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.5,
        "bold": False
    },
    {
        "id": "478edbbb1e614870aa67089701e941a0",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 174,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1140,
                    "y1": 1500,
                    "x2": 5919,
                    "y2": 2299,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 9,
            "hash": "b051d6e5e04da5a6cf212dab3b9178eb871efd9e"
        },
        "content": [
            "[15]Chuan Guo,Yuxuan Mu,Xinxin Zuo,Peng Dai,YouliangYan,Juwei Lu,and Li Cheng.Generative human motionstylization in latent space.arXiv preprint arXiv:2401.13505,2024.2"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "af44af3b08aa4d17a0b200c20b0ee183",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 175,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1149,
                    "y1": 2379,
                    "x2": 5909,
                    "y2": 3199,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 9,
            "hash": "f85c56fc8b18125d0ead9ec1767719975ee45457"
        },
        "content": [
            "[16]Ziyu Guo*,Renrui Zhang*#,Longtian Qiu,Xianzheng Ma,Xupeng Miao,Xuming He,and Bin Cui.Calip:Zero-shotenhancement of clip with parameter-free attention.AAAI2023 Oral,2022.3"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "9b80a9384ca14fbbb408c476309f965c",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 176,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1129,
                    "y1": 3289,
                    "x2": 5919,
                    "y2": 4519,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 9,
            "hash": "dbe8e93e9391702ccd68bdd0abd23a7b5c44eb0e"
        },
        "content": [
            "[17]Ziyu Guo,Renrui Zhang,Xiangyang Zhu,Yiwen Tang,Xianzheng Ma,Jiaming Han,Kexin Chen,Peng Gao,Xi-anzhi Li,Hongsheng Li,et al.Point-bind &point-1lm:Aligning point cloud with multi-modality for 3d understand-ing,generation,and instruction following.arXiv preprintarXiv:2309.00615,2023.3"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "deaf1168c7be4c9fb8a20918b3a60863",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 177,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1149,
                    "y1": 4649,
                    "x2": 5919,
                    "y2": 5680,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 9,
            "hash": "4c5e7d07bfbf3d1f5dab3310a5290374eff260ef"
        },
        "content": [
            "[18]Zilu Guo,Hongbin Lin,Zhihao Yuan,Chaoda Zheng,Peng-shuo Qiu,Dongzhi Jiang,Renrui Zhang,Chun-Mei Feng,and Zhen Li.Pisa:A self-augmented data engine and train-ing strategy for 3d understanding with large models.arXivpreprint arXiv:2503.10529,2025.3"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "651d4651343a44f19e52ee722a0f7d08",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 178,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1159,
                    "y1": 5760,
                    "x2": 5919,
                    "y2": 6770,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 9,
            "hash": "49b84a273da74eec991f1b7c44f15218d8a9d7ee"
        },
        "content": [
            "[19]Ziyu Guo,Ray Zhang,Hao Chen,Jialin Gao,DongzhiJiang,Jiaze Wang,and Pheng-Ann Heng.Sciverse:Un-veiling the knowledge comprehension and visual reasoningof lmms on multi-modal scientific problems.arXiv preprintarXiv:2503.10627,2025.3"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "5c55637dd6454610a8b39b3c3e389664",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 179,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1149,
                    "y1": 6879,
                    "x2": 5919,
                    "y2": 7879,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 9,
            "hash": "535ca887d49176d793274d8e12e98367cc553d9d"
        },
        "content": [
            "[20]Ziyu Guo,Renrui Zhang,Chengzhuo Tong,Zhizheng Zhao,Peng Gao,Hongsheng Li,and Pheng-Ann Heng.Can wegenerate images with cot?let's verify and reinforce imagegeneration step by step.arXiv preprint arXiv:2501.13926,2025.3"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "8cbfc60f70b44b4fb12248c0a7eeb110",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 180,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1159,
                    "y1": 7999,
                    "x2": 5919,
                    "y2": 8830,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 9,
            "hash": "2b3854e8c6be7dffa70f85976a09deb7c2497baf"
        },
        "content": [
            "[21]Jiaming Han,Renrui Zhang,Wenqi Shao,Peng Gao,PengXu,Han Xiao,Kaipeng Zhang,Chris Liu,Song Wen,ZiyuGuo,et al.Imagebind-1lm:Multi-modality instruction tuning.arXiv preprint arXiv:2309.03905,2023.3"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.0,
        "bold": False
    },
    {
        "id": "9ea865cf603144c69e9a6814191f3a51",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 181,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1159,
                    "y1": 8900,
                    "x2": 5919,
                    "y2": 9710,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 9,
            "hash": "0af15b9bd847d34cd5b3f4da2281b11134217307"
        },
        "content": [
            "[22]Siyuan Huang,Zan Wang,Puhao Li,Baoxiong Jia,TengyuLiu,Yixin Zhu,Wei Liang,and Song-Chun Zhu.Diffusion-based generation,optimization,and planning in 3d scenes.InCVPR,2023.2"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "95362b3c812b4021a457f93a55b10630",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 182,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1140,
                    "y1": 9800,
                    "x2": 5900,
                    "y2": 10389,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 9,
            "hash": "3a10141911e622a2accfa0d0bca44a6fd75c193d"
        },
        "content": [
            "[23]Deok-Kyeong Jang,Soomin Park,and Sung-Hee Lee.Motionpuzzle:Arbitrary motion style transfer by body part.ACMTOG,2022.2,6"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "05773984973e463da94b58db2d87ce9a",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 183,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1149,
                    "y1": 10480,
                    "x2": 5919,
                    "y2": 11289,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 9,
            "hash": "44dc7fba7977dc4e434fa47dea02c61c610b8bfb"
        },
        "content": [
            "[24]Biao Jiang,Xin Chen,Wen Liu,Jingyi Yu,Gang Yu,and TaoChen.Motiongpt:Human motion as a foreign language.Ad-vances in Neural Information Processing Systems,36:20067-20079,2023.3,5"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "5e5d08510b3d40e4a721703be21c7d30",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 184,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1149,
                    "y1": 11390,
                    "x2": 5919,
                    "y2": 14199,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 9,
            "hash": "79efcb2f162eb0082028385e2e26145c507d3c97"
        },
        "content": [
            "[25]Dongzhi Jiang,Renrui Zhang,Ziyu Guo,Yanwei Li,Yu Qi,Xinyan Chen,Liuhui Wang,Jianhan Jin,Claire Guo,ShenYan,et al.Mme-cot:Benchmarking chain-of-thought in largemultimodal models for reasoning quality,robustness,andefficiency.arXiv preprint arXiv:2502.09621,2025.3[26]Korrawe Karunratanakul,Konpat Preechakul,Supasorn Suwa-janakorn,and Siyu Tang.Gmd:Controllable human motionsynthesis via guided diffusion models.In ICCV,2023.1,2[27]Boeun Kim,Jungho Kim,Hyung Jin Chang,and Jin YoungChoi.Most:Motion style transformer between diverse actioncontents.In Proceedings of the IEEE/CVF Conference onComputer Vision and Pattern Recognition,pages 1705-1714,2024.2"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "24314074dd2e4c79bfefb94a5687d477",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 185,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6320,
                    "y1": 1479,
                    "x2": 11099,
                    "y2": 2319,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 9,
            "hash": "bf0be50f2299399146717d75bc26e648aef3baea"
        },
        "content": [
            "[28]Bo Li,Yuanhan Zhang,Dong Guo,Renrui Zhang,Feng Li,Hao Zhang,Kaichen Zhang,Yanwei Li,Ziwei Liu,and Chun-yuan Li.Llava-onevision:Easy visual task transfer,2024a.URL https://arxiv.org/abs/2408.03326.3"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "b611830c5e8d4081806022abfaecd797",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 186,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6320,
                    "y1": 2420,
                    "x2": 11080,
                    "y2": 3250,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 9,
            "hash": "92a0c89d09c8168246d0c2b773b526a771556243"
        },
        "content": [
            "[29]Feng Li,Renrui Zhang,Hao Zhang,Yuanhan Zhang,BoLi,Wei Li,Zejun Ma,and Chunyuan Li.Llava-next-interleave:Tackling multi-image,video,and 3d in large multimodalmodels.ICLR 2025 Spotlight,2024.3"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "02473dd05b294d3497a211cbb4fd7957",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 187,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6320,
                    "y1": 3330,
                    "x2": 11080,
                    "y2": 5719,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 9,
            "hash": "13799ebcad17dae34671a04c6cb20b512fbefe54"
        },
        "content": [
            "[30]XiaoqiLi,Mingxu Zhang,Yiran Geng,Haoran Geng,Yux-ing Long,Yan Shen,Renrui Zhang,Jiaming Liu,and HaoDong.Manipllm:Embodied multimodal large languagemodel for object-centric robotic manipulation.In Proceed-ings of the IEEE/CVF Conference on Computer Vision andPattern Recognition,pages 18061-18070,2024.3[31]Zhe Li,Yisheng He,Lei Zhong,Weichao Shen,Qi Zuo,Lingteng Qiu,Zilong Dong,Laurence Tianruo Yang,andWeihao Yuan.  Mulsmo:Multimodal stylized motiongeneration by bidirectional control low.arXiv preprintarXiv:2412.09901,2024.2,3"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "b356ec3c19414a7e8c9533fb091fa6a3",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 188,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6320,
                    "y1": 5830,
                    "x2": 11080,
                    "y2": 6879,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 9,
            "hash": "b54a282b83296dedc060056841060c1afae99c5d"
        },
        "content": [
            "[32]Zhe Li,Weihao Yuan,Yisheng He,Lingteng Qiu,ShenhaoZhu,Xiaodong Gu,Weichao Shen,Yuan Dong,Zilong Dong,and Laurence T Yang.Lamp:Language-motion pretrain-ing for motion generation,retrieval,and captioning.arXivpreprint arXiv:2410.07093,2024.2"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "fce16b805f0a45f0a5b8cb00d534c42d",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 189,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6320,
                    "y1": 6959,
                    "x2": 11069,
                    "y2": 7370,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 9,
            "hash": "4b3f6de7f05f5c055aeae207188ac1a1fb0e5951"
        },
        "content": [
            "[33]ILoshchilov.Decoupled weight decay regularization.arXivpreprint arXiv:1711.05101,2017.5"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.0,
        "bold": False
    },
    {
        "id": "aeb6c28042284db791e4fea38d27ebec",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 190,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6330,
                    "y1": 7450,
                    "x2": 11090,
                    "y2": 8989,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 9,
            "hash": "59cc8f6626a47ebbc4e9129432cc014ee2b92094"
        },
        "content": [
            "[34]Naureen Mahmood,Nima Ghorbani,Nikolaus FTroje,Ger-ard Pons-Moll,and Michael J Black.Amass:Archive ofmotion capture as surface shapes.In ICCV,2019.5[35]Ian Mason,Sebastian Starke,and Taku Komura.Real-timestyle modelling of human locomotion via feature-wise trans-formations and local motion phases.Proceedings of the ACMon Computer Graphics and Interactive Techniques,2022.2,"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.0,
        "bold": False
    },
    {
        "id": "03df75c731d040feb438d439b27b9861",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 191,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6720,
                    "y1": 9030,
                    "x2": 7530,
                    "y2": 9180,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 9,
            "hash": "6a0fad14e56e0f5a6f4441f3698efaf15703d457"
        },
        "content": [
            "4,5,6,7,8"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "8ed0fd0f664d4d14b8f1f64e3a914eb6",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 192,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6320,
                    "y1": 9290,
                    "x2": 11090,
                    "y2": 10350,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 9,
            "hash": "863cd92406124ff506f5118c1e6b57ca8805b61c"
        },
        "content": [
            "[36]Arsha Nagrani,Paul Hongsuck Seo,Bryan Seybold,AnjaHauth,Santiago Manen,Chen Sun,and Cordelia Schmid.Learning audio-video modalities from image captions.InEuropean Conference on Computer Vision,pages 407-426.Springer,2022.3"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 9.0,
        "bold": False
    },
    {
        "id": "3655ab357024408fa6c19f45ecadf73b",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 193,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6320,
                    "y1": 10430,
                    "x2": 11080,
                    "y2": 11439,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 9,
            "hash": "2cace38dc55653502d4917746ecb22d5211cce37"
        },
        "content": [
            "[37]Soomin Park,Deok-Kyeong Jang,and Sung-Hee Lee.Di-verse motion stylization for multiple style domains via spatial-temporal graph-based generative model.Proceedings ofthe ACM on Computer Graphics and Interactive Techniques,2021.2"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "3b230e8623a145c0af6b9d1fd01a5055",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 194,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6320,
                    "y1": 11550,
                    "x2": 11099,
                    "y2": 12400,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 9,
            "hash": "594dad3a72c83765ec9ba6826b74b3c31d56a21e"
        },
        "content": [
            "[38]Xiaogang Peng,Yiming Xie,Zizhao Wu,Varun Jampani,Deqing Sun,and Huaizu Jiang.Hoi-diff:Text-driven synthe-sis of 3d human-object interactions using diffusion models.arXiv preprint arXiv:2312.06553,2023.2"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "409d40de52974daf8a0f3299fcaa41aa",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 195,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6320,
                    "y1": 12499,
                    "x2": 11080,
                    "y2": 13290,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 9,
            "hash": "a17a875d9186964ecab53a028db39ca60c39198e"
        },
        "content": [
            "[39]Mathis Petrovich,Or Litany,Umar Iqbal,Michael J Black,Gul Varol,Xue Bin Peng,and Davis Rempe.Multi-tracktimeline control for text-driven 3d human motion generation.In CVPR,2024.2"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "4f9c6843803d4d7ca1f4293d4c8f3516",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 196,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6320,
                    "y1": 13409,
                    "x2": 11090,
                    "y2": 14199,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 9,
            "hash": "5674df47a855c383e048691f9b1bff4be75b521b"
        },
        "content": [
            "[40]Ekkasit Pinyoanuntapong,Muhammad Usama Saleem,PuWang,Minwoo Lee,Srijan Das,and Chen Chen.Bamm:Bidirectional autoregressive motion model.arXiv preprintarXiv:2403.19435.2024."
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "b55bd20d5dff4530bdbf64f2386c8c60",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 197,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6009,
                    "y1": 14649,
                    "x2": 6220,
                    "y2": 14799,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 9,
            "hash": "b1d5781111d84f7b3fe45a0852e59758cd7a87e5"
        },
        "content": [
            "10"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "46e84bfb5b524e0e87fe9dc3a94230fb",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 198,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1149,
                    "y1": 1500,
                    "x2": 5909,
                    "y2": 2069,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 10,
            "hash": "f70642c56e6c2c8ff4e1b48ebd7a586fbeb350d0"
        },
        "content": [
            "[41]Ekkasit Pinyoanuntapong,Pu Wang,Minwoo Lee,and ChenChen.Mmm:Generative masked motion model.In CVPR,2024.2"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "f97c30c520e642a58918c4bf2736bdbe",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 199,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1149,
                    "y1": 2169,
                    "x2": 5900,
                    "y2": 3199,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 10,
            "hash": "b00d241751d2d3fd396e665f2162ad5540b9a0ea"
        },
        "content": [
            "[42]Ziyun Qian,Zeyu Xiao,Zhenyi Wu,Dingkang Yang,Mingcheng Li,Shunli Wang,Shuaibing Wang,DongliangKou,and Lihua Zhang.  Smcd:High realism motionstyle transfer via mamba-based diffusion.arXiv preprintarXiv:2405.02844,2024.2"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "9a4f5c172704467fa1c23c9c1242f6da",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 200,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1159,
                    "y1": 3289,
                    "x2": 5909,
                    "y2": 3909,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 10,
            "hash": "72b6835feaf9eb343f268a37e73a003edd8e8b2d"
        },
        "content": [
            "[43]Sigal Raab,Inbal Leibovitch,Guy Tevet,Moab Arar,Amit HBermano,and Daniel Cohen-Or.Single motion diffusion.arXiv preprint arXiv:2302.05905,2023.2"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "5b1fd01f78da49dd8fd5c6940a12b8b2",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 201,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1140,
                    "y1": 3979,
                    "x2": 5919,
                    "y2": 4979,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 10,
            "hash": "cc8748a8738054bf23afeea66619d83c2c798177"
        },
        "content": [
            "[44]Sigal Raab,Inbar Gat,Nathan Sala,Guy Tevet,Rotem Shalev-Arkushin,Ohad Fried,Amit HBermano,and Daniel Cohen-Or.Monkey see,monkey do:Harnessing self-attention inmotion diffusion for zero-shot motion transfer.arXiv preprintarXiv:2406.06508,2024.2"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "3325428c833f4478944b626fa10e2479",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 202,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1149,
                    "y1": 5090,
                    "x2": 5909,
                    "y2": 6119,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 10,
            "hash": "65ae3b7e44b82384e3f1eeb29276ab25bd903faf"
        },
        "content": [
            "[45]Alec Radford,Jong Wook Kim,Chris Hallacy,AdityaRamesh,Gabriel Goh,Sandhini Agarwal,Girish Sastry,Amanda Askell,Pamela Mishkin,Jack Clark,et al.Leamingtransferable visual models from natural language supervision.2021.3,4"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "54e1918393414b3b87b55f799d3f7661",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 203,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1159,
                    "y1": 6210,
                    "x2": 5890,
                    "y2": 7000,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 10,
            "hash": "f3cb1846f63bd692465bf83d7450939cc330030a"
        },
        "content": [
            "[46]Davis Rempe,Zhengyi Luo,Xue Bin Peng,Ye Yuan,KrisKitani,Karsten Kreis,Sanja Fidler,and Or Litany.Trace andpace:Controllable pedestrian animation via guided trajectorydiffusion.In CVPR,2023.2"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "1ac041bf8a8649d58f62711ab6211bff",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 204,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1149,
                    "y1": 7099,
                    "x2": 5919,
                    "y2": 9019,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 10,
            "hash": "f5b46803f0deb53ed5715fcfb36a2468cc936840"
        },
        "content": [
            "[47]Robin Rombach,Andreas Blattmann,Dominik Lorenz,Patrick Esser,and Björn Ommer.High-resolution imagesynthesis with latent diffusion models.In CVPR,2022.2[48]Wenfeng Song,Xingliang Jin,Shuai Li,Chenglizhao Chen,Aimin Hao,Xia Hou,Ning Li,and Hong Qin.Arbitrarymotion style transfer with multi-condition motion latent dif-fusion model.In Proceedings of the IEEE/CVF Conferenceon Computer Vision and Pattern Recognition,pages 821-830,2024.2"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "9b565231a51c42c885d5225edd78db2d",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 205,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1149,
                    "y1": 9130,
                    "x2": 5919,
                    "y2": 10159,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 10,
            "hash": "0950943fde3da8e131bcbf9b34ee30caf8a09c7b"
        },
        "content": [
            "[49]Xiangjun Tang,Linjun Wu,He Wang,Bo Hu,Xu Gong,Yuchen Liao,Songnan Li,Qilong Kou,and Xiaogang Jin.Rsmt:Real-time stylized motion transition for characters.InACM SIGGRAPH 2023 Conference Proceedings,pages 1-10,2023.2"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "f7191b3db69f440381165bf70aa68694",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 206,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1149,
                    "y1": 10250,
                    "x2": 5919,
                    "y2": 11979,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 10,
            "hash": "748623d12560330559851187b0aa757a3cbe05dd"
        },
        "content": [
            "[50]Yiwen Tang,Zoey Guo,Zhuhao Wang,Ray Zhang,QizhiChen,Junli Liu,Delin Qu,Zhigang Wang,Dong Wang,Xue-long Li,et al.Exploring the potential of encoder-free archi-tectures in 3d lmms.arXivpreprint arXiv:2502.09620,2025.3[51]Tianxin Tao,Xiaohang Zhan,Zhongquan Chen,and Michielvan de Panne.Style-erd:Responsive and coherent onlinemotion style transfer.2022.2"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "a2b32a8e44da42ddb34af520f7059cfe",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 207,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1149,
                    "y1": 12049,
                    "x2": 5879,
                    "y2": 12620,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 10,
            "hash": "6703e6dc82732bd9252a47491f7ab035246e2ba7"
        },
        "content": [
            "[52]Guy Tevet,Sigal Raab,Brian Gordon,Yonatan Shafir,DanielCohen-or,and Amit Haim Bermano.Human motion diffusionmodel.In ICLR,2023.1,2"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "f5e9b19efac9471f955067a24df6f1bf",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 208,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1159,
                    "y1": 12710,
                    "x2": 5909,
                    "y2": 13530,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 10,
            "hash": "2300cd7328e3a95e698add7f2e3021fa0f819c70"
        },
        "content": [
            "[53]Weilin Wan,Zhiyang Dou,Taku Komura,Wenping Wang,Dinesh Jayaraman,and Lingjie Liu.Tlcontrol:Trajectory andlanguage control for human motion synthesis.arXiv preprintarXiv:2311.17135,2023.2"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "82f0be4fb1f5418e8e2c7391eabc2e20",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 209,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1149,
                    "y1": 13630,
                    "x2": 5890,
                    "y2": 14240,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 10,
            "hash": "ed1237cd22dad5f8d175bda3129111cee869c9ad"
        },
        "content": [
            "[54]Yu-Hui Wen,Zhipeng Yang,Hongbo Fu,Lin Gao,Yanan Sun,and Yong-Jin Liu.Autoregressive stylized motion synthesiswith generative flow.In CVPR,2021.2"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "43d0d08f6dcb4e6ba0906820977ca93d",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 210,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6320,
                    "y1": 1479,
                    "x2": 11080,
                    "y2": 2299,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 10,
            "hash": "9905ac9a14746db0688a2b797c503d7db56aa7bc"
        },
        "content": [
            "[55]Qianyang Wu,Ye Shi,Xiaoshui Huang,Jingyi Yu,LanXu,and Jingya Wang.Thor:Text to human-object inter-action diffusion via relation intervention.arXiv preprintarXiv:2403.11208,2024.2"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "f0109a7a804e48c49118dd98d7916c67",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 211,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6330,
                    "y1": 2379,
                    "x2": 11080,
                    "y2": 3199,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 10,
            "hash": "c47ba244186ebf7daa99eacf15bc5b55f20d82ff"
        },
        "content": [
            "[56]Qi Wu,Yubo Zhao,Yifan Wang,Yu-Wing Tai,and Chi-KeungTang.Motionllm:Multimodal motion-language learning withlarge language models.arXiv preprint arXiv:2405.17013,2024.2"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "ccb1f37d90484ce980f16c1f32a6ec01",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 212,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6330,
                    "y1": 3289,
                    "x2": 11059,
                    "y2": 3889,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 10,
            "hash": "23c17905b4a51867b050a88e841a55db32d111f9"
        },
        "content": [
            "[57]Yiming Xie,Varun Jampani,Lei Zhong,Deqing Sun,andHuaizu Jiang.Omnicontrol:Control any joint at any time forhuman motion generation.In ICLR,2024.2"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "c79740de891947a7835ca420927fe55e",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 213,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6320,
                    "y1": 3960,
                    "x2": 11080,
                    "y2": 5250,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 10,
            "hash": "ce64f8e568cb63b521c7d182008c5efbeeeb87e4"
        },
        "content": [
            "[58]Jingwei Xu,Huazhe Xu,Bingbing Ni,Xiaokang Yang,Xi-aolong Wang,and Trevor Darrell.Hierarchical style-basednetworks for motion synthesis.In ECCV,2020.2[59]Sirui Xu,Zhengyuan Li,Yu-Xiong Wang,and Liang-YanGui.Interdiff:Generating 3d human-object interactions withphysics-informed diffusion.In ICCV,2023.2"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "4079f7383852429a9590a3dd16c17cfc",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 214,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6320,
                    "y1": 5320,
                    "x2": 11080,
                    "y2": 9030,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 10,
            "hash": "bc986aa58079cf1e7660905c6b85d907e6545dc5"
        },
        "content": [
            "[60]Sirui Xu,Ziyin Wang,Yu-Xiong Wang,and Liang-Yan Gui.Interdreamer:Zero-shot text to 3d dynamic human-objectinteraction.arXiv preprint arXiv:2403.19652,2024.2[61]Le Xue,Mingfei Gao,Chen Xing,Roberto Martín-Martín,Jiajun Wu,Caiming Xiong,Ran Xu,Juan Carlos Niebles,andSilvio Savarese.Ulip:Learning a unified representation oflanguage,images,and point clouds for 3d understanding.InProceedings of the IEEE/CVF conference on computer visionand pattern recognition,pages 1179-1189,2023.5[62]Senqiao Yang,Jiaming Liu,Ray Zhang,Mingjie Pan,ZiyuGuo,Xiaoqi Li,Zehui Chen,Peng Gao,Yandong Guo,andShanghang Zhang.Lidar-1lm:Exploring the potential of largelanguage models for 3d lidar understanding.AAAI 2025,2023.3[63]Ye Yuan,Jiaming Song,Umar Iqbal,Arash Vahdat,and JanKautz.Physdiff:Physics-guided human motion diffusionmodel.In ICCV,2023.2"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "82b2ea004ce6439e80e0b8a8e50739ed",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 215,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6320,
                    "y1": 9139,
                    "x2": 11050,
                    "y2": 9970,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 10,
            "hash": "d4e605d9827caee9640931f43509e4abe454618b"
        },
        "content": [
            "[64]Lvmin Zhang,Anyi Rao,and Maneesh Agrawala.Addingconditional control to text-to-image diffusion models.InProceedings of the IEEE/CVF international conference oncomputer vision,pages 3836-3847,2023.2,4"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "f14dd7fd2132411cb28120821bdb6043",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 216,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6320,
                    "y1": 10040,
                    "x2": 11090,
                    "y2": 10829,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 10,
            "hash": "a0b0bbe8c7b57aaf33eb9704780c36784ce5c03f"
        },
        "content": [
            "[65]Mingyuan Zhang,Zhongang Cai,Liang Pan,Fangzhou Hong,Xinying Guo,Lei Yang,and Ziwei Liu.Motiondiffuse:Text-driven human motion generation with diffusion model.PAMI,2024.2"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "e259eddb6a5a48e4b867ba25d49764dc",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 217,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6330,
                    "y1": 10940,
                    "x2": 11099,
                    "y2": 11729,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 10,
            "hash": "03a7f97523c4459cac63f31f9221f9a0f645ad11"
        },
        "content": [
            "[66]Renrui Zhang,Ziyu Guo,Wei Zhang,Kunchang Li,XupengMiao,Bin Cui,Yu Qiao,Peng Gao,and Hongsheng Li.Point-clip:Point cloud understanding by clip.In CVPR 2022,2022.3"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "0e4f2414fc1449afb62655faa827452e",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 218,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6320,
                    "y1": 11829,
                    "x2": 11090,
                    "y2": 13789,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 10,
            "hash": "f15910d119bc8926f3fa30d44f8cd87edab1e0ed"
        },
        "content": [
            "[67]Renrui Zhang,Wei Zhang,Rongyao Fang,Peng Gao,Kun-chang Li,Jifeng Dai,Yu Qiao,and Hongsheng Li.Tip-adapter:Training-free adaption of clip for few-shot classifi-cation.In ECCV 2022.Springer Nature Switzerland,2022.3[68]Renrui Zhang,Jiaming Han,Chris Liu,Aojun Zhou,PanLu,Yu Qiao,Hongsheng Li,and Peng Gao.Llama-adapter:Efficient fine-tuning of large language models with zero-initialized attention.In ICLR 2024,2024.3"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "d9aba30c3a134bff90d00c198dc90146",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 219,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6330,
                    "y1": 13850,
                    "x2": 11090,
                    "y2": 14240,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 10,
            "hash": "860584d1f8a897f5af78939417d6d9a954c9f66d"
        },
        "content": [
            "[69]Renrui Zhang,Dongzhi Jiang,Yichi Zhang,Haokun Lin,ZiyuGuo,Pengshuo Qiu,Aojun Zhou,Pan Lu,Kai-Wei Chang,"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "a2ef50d4aac54d80b9bbdac7a9ab59f3",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 220,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 6009,
                    "y1": 14649,
                    "x2": 6200,
                    "y2": 14809,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 10,
            "hash": "17ba0791499db908433b80f37c5fbc89b870084b"
        },
        "content": [
            "11"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.0,
        "bold": False
    },
    {
        "id": "0e99757de7a0418aab62bad91ba3ab7d",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 221,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1500,
                    "y1": 1529,
                    "x2": 5930,
                    "y2": 2110,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 11,
            "hash": "8417171433edaf37299ac70282dffaa16c5d261e"
        },
        "content": [
            "Peng Gao,et al.Mathverse:Does your multi-modal llm trulysee thediagrams in visual math problems?ECCV2024,2024.3"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 7.5,
        "bold": False
    },
    {
        "id": "6df26e4cc7f949bab6aecba9a4d99475",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 222,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1129,
                    "y1": 2200,
                    "x2": 5949,
                    "y2": 3250,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 11,
            "hash": "23c8080ba51bc71f60049caef7d3ef507c7c5951"
        },
        "content": [
            "[70]Renrui Zhang,Xinyu Wei,Dongzhi Jiang,Ziyu Guo,Shicheng Li,Yichi Zhang,Chengzhuo Tong,Jiaming Liu,Aojun Zhou,Bin Wei,et al.Mavis:Mathematical visual in-struction tuning with an automatic data engine.arXiv preprintarXiv:2407.08739,2024.3"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 7.5,
        "bold": False
    },
    {
        "id": "58e50e1bc247456ebe371e53c1d3e42c",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 223,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1129,
                    "y1": 3320,
                    "x2": 5919,
                    "y2": 3950,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 11,
            "hash": "5d074f882b1e858daa82353d793483af8a6a3d2f"
        },
        "content": [
            "[71]Lei Zhong,Yiming Xie,Varun Jampani,Deqing Sun,andHuaizu Jiang.Smoodi:Stylized motion diffusion model.InECCV,2024.2,4,5,6,7,8,9"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 7.5,
        "bold": False
    },
    {
        "id": "854ddceb0eea4894995282971a578c0b",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 224,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1149,
                    "y1": 4010,
                    "x2": 5930,
                    "y2": 5109,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 11,
            "hash": "0aba388bfa55b6d9c35187ed4750b76d75652223"
        },
        "content": [
            "[72]Bin Zhu,Bin Lin,Munan Ning,Yang Yan,Jiaxi Cui,HongFaWang,Yatian Pang,Wenhao Jiang,Junwu Zhang,ZongweiLi,et al.Languagebind:Extending video-language pretrainingto n-modality by language-based semantic alignment.arXivpreprint arXiv:2310.01852,2023.3"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 10.0,
        "bold": False
    },
    {
        "id": "9ffcaa386341479b8e47484b16ae1040",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 225,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1159,
                    "y1": 5160,
                    "x2": 5970,
                    "y2": 6029,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 11,
            "hash": "04ee38a5f27c1d5a31853236d06649b4fc3b309c"
        },
        "content": [
            "[73]Xiangyang Zhu*,Renrui Zhang*#,Bowei He,Ziyu Guo,Ziyao Zeng,Zipeng Qin,Shanghang Zhang,and Peng Gao.Pointclip v2:Prompting clip and gpt for powerful 3d open-world learning.ICCV 2023,2023.3"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 10.0,
        "bold": False
    },
    {
        "id": "0b26b8f9d3044b379d247ba5a094da9e",
        "parent": "ffda1eac18ff4c20a18623bd5584929b",
        "order": 226,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 5939,
                    "y1": 14680,
                    "x2": 6130,
                    "y2": 14840,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 11,
            "hash": "7b52009b64fd0a2a49e6d8a939753077792b0554"
        },
        "content": [
            "12"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 7.5,
        "bold": False
    }
]


class TestPdfParse(TestCase):

    def setUp(self):
        self.parser = PdfParse()
        self.dsts = kdc_pdf_data_3()
        self.contxt = PipelineContext()
    def tearDown(self):
        pass

    def test_dst_generate(self):
        # self.contxt.file_info=FileInfo()
        dst = convert_to_dst_list(self.dsts)
        dst_list = self.parser.dst_reprocess(self.contxt,dst)
        print(print_dst_tree(dst_list))
        print((print_dst_indent_tree(dst_list)))