from unittest import TestCase

from modules.entity.dst_entity import print_dst_tree
from modules.flows.chunk.test_docx import convert_to_dst_list
from modules.flows.dst_builder.pdf import PdfParse
from modules.pipeline.context import PipelineContext


def kdc_pdf_data_3():
    return  [
    {
        "id": "59daa879d1024c2b9b0e8fcf53d70e46",
        "parent": "-1",
        "order": 0,
        "dst_type": "root",
        "attributes": {
            "level": 0,
            "position": {
                "bbox": {
                    "x1": 1,
                    "y1": 2,
                    "x2": 3,
                    "y2": 4,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 0,
            "hash": "roothashhashhashhashhashhashhashhashhashhash"
        },
        "content": [
            "根节点"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": -1,
        "bold": False
    },
    {
        "id": "07eb15d5ef1a45ccb224da5af0aa1561",
        "parent": "59daa879d1024c2b9b0e8fcf53d70e46",
        "order": 1,
        "dst_type": "image",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1411,
                    "y1": 952,
                    "x2": 3804,
                    "y2": 1670,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 0,
            "hash": "9aeb34bbafee02d9f8a77fdee990961270db4994"
        },
        "content": [
            "http://kna-common.ks3-cn-beijing.ksyuncs.com/insight-ai/doc-parser/ebcc796be75a47028b64ee9ddb6019ea_0.png?Signature=z2wbxzg%2FSFhOD%2Flc3W9NmEzwcsw%3D&Expires=3243324800&KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb"
        ],
        "image_pixel": [
            240,
            73
        ],
        "mark": None,
        "font_size": -1,
        "bold": False
    },
    {
        "id": "d185a2810ab2462b9a57fc73da9de5ce",
        "parent": "59daa879d1024c2b9b0e8fcf53d70e46",
        "order": 2,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 8267,
                    "y1": 1324,
                    "x2": 10485,
                    "y2": 1588,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 0,
            "hash": "c3fd3db22891326abc6d1f87fbf0ecc7209e921b"
        },
        "content": [
            "川财证券研究报告"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 11.999999046325684,
        "bold": True
    },
    {
        "id": "77d357723ba940b481a01b3cd744386f",
        "parent": "59daa879d1024c2b9b0e8fcf53d70e46",
        "order": 3,
        "dst_type": "table",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1411,
                    "y1": 1634,
                    "x2": 10488,
                    "y2": 3889,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 0,
            "hash": "3963fb301d10bf647241488fb84fb6943b9c9214"
        },
        "content": [
            "<table><tr><td rowspan=\"1\" colspan=\"4\">美国三大主要股指全部上涨，科技板块表现较好—海外跟踪</td></tr><tr><td rowspan=\"1\" colspan=\"1\">所属部门：总量研究部分析师：张卓然</td><td rowspan=\"1\" colspan=\"2\">报告类别：策略研究报告执业证书： S1100522070001</td><td rowspan=\"1\" colspan=\"1\">报告时间： 2023 年 8 月 30 日联系方式： <EMAIL></td></tr><tr><td rowspan=\"1\" colspan=\"2\">北京：东城区建国门内大街 28 号民生金融中心 A 座 6 层， 100005</td><td rowspan=\"1\" colspan=\"2\">深圳：福田区福华一路 6 号免税商务大厦 32 层， 518000</td></tr><tr><td rowspan=\"1\" colspan=\"2\">上海：陆家嘴环路 1000 号恒生大厦 11 楼， 200120</td><td rowspan=\"1\" colspan=\"2\">成都：高新区交子大道 177 号中海国际中心 B 座 17 楼， 610041</td></tr></table>"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": -1,
        "bold": False
    },
    {
        "id": "9e58405cb2b24639b2b23178ea64ff81",
        "parent": "59daa879d1024c2b9b0e8fcf53d70e46",
        "order": 4,
        "dst_type": "text",
        "attributes": {
            "level": 1,
            "position": {
                "bbox": {
                    "x1": 1472,
                    "y1": 4057,
                    "x2": 2774,
                    "y2": 4323,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 0,
            "hash": "5ca118ee8af633afb9c775450dceabb2edf00f8d"
        },
        "content": [
            "􀂙 跟踪点评"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 11.999999046325684,
        "bold": True
    },
    {
        "id": "b44ad4f11ba44e9995baa83cbef0a892",
        "parent": "9e58405cb2b24639b2b23178ea64ff81",
        "order": 5,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1459,
                    "y1": 4522,
                    "x2": 10387,
                    "y2": 6932,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 0,
            "hash": "2728935898a08876e22b569e40367081f50e5805"
        },
        "content": [
            "周二，美国三大主要股指全部上涨。道琼斯工业平均指数收于 34852.67 点，上涨 0 .85%；标普 500 指数收于 4497.63 点 , 上涨 1.45%；纳斯达克综合指数收于 13943.76 点，上涨 1. 74%。行业方面，美股科技板块表现较好，大型科技股普遍上涨，谷歌上涨 2. 72%；银行股多数上涨，美国银行上涨 1.44%；中概股普遍上涨，老虎证券上涨 28.53%，理想汽车上涨 7. 79%。美国劳工部公布的最新数据显示，美国 7 月份的职位空缺数下降至 882 . 7 万个，创下了近 28 个月来最低记录，劳动力市场压力进一步改善。在此背景下，美联储加息预期有所降温，美股市场表现较好。"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 11.999999046325684,
        "bold": False
    },
    {
        "id": "2d1a5265e1d44c2bb48bf207985791a9",
        "parent": "9e58405cb2b24639b2b23178ea64ff81",
        "order": 6,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1455,
                    "y1": 7198,
                    "x2": 10526,
                    "y2": 9248,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 0,
            "hash": "359a5de56fb88a1322a872521d7497223ad88707"
        },
        "content": [
            "周三，香港三大主要股指全部下跌。恒生指数收于 18482.86 点，下跌 0.01%；恒生中国企业指数收于 6356.5 点，下跌 0.47%；恒生香港中资企业指数收于 3612.23 点，下跌0.24%。恒生科技指数收于 4195.41 点下跌 0.92%。行业方面，恒生原材料业、恒生金融业、恒生地产建筑业今日涨幅居前，恒生必需性消费业、恒生资讯科技业、恒生医疗保健业跌幅较大。个股方面，招金矿业上涨 3.85%，联想集团上涨 3.84%，中国宏桥上涨3. 12%。"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 11.999999046325684,
        "bold": False
    },
    {
        "id": "52fce6f109f14cdf86b2464c29f6e3f5",
        "parent": "59daa879d1024c2b9b0e8fcf53d70e46",
        "order": 7,
        "dst_type": "text",
        "attributes": {
            "level": 1,
            "position": {
                "bbox": {
                    "x1": 1472,
                    "y1": 9440,
                    "x2": 2774,
                    "y2": 9706,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 0,
            "hash": "7f8ed3730811d9a57fd4c94f7bc96e03cdb02bbf"
        },
        "content": [
            "􀂙 政策要闻"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 11.999999046325684,
        "bold": True
    },
    {
        "id": "e7d8e906b3104d5e8a2661ec8af176cb",
        "parent": "52fce6f109f14cdf86b2464c29f6e3f5",
        "order": 8,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1459,
                    "y1": 9874,
                    "x2": 10526,
                    "y2": 10844,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 0,
            "hash": "22d091e5819b0f6b6954a42434cbc8ec11af70e3"
        },
        "content": [
            "1. 日本央行： 日本央行委员田村直树： 日本央行退出宽松政策的顺序和速度将取决于届时的经济状况。如果确定目标已达成，将考虑政策退出的选项。终结负利率及 YCC 都是日本央行退出宽松政策的选项。 （新浪财经）"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 11.999999046325684,
        "bold": False
    },
    {
        "id": "782a8dfb0e184dc39c2b0584720bc4ba",
        "parent": "52fce6f109f14cdf86b2464c29f6e3f5",
        "order": 9,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1457,
                    "y1": 11048,
                    "x2": 10469,
                    "y2": 11658,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 0,
            "hash": "b5852e6623af78a94142d9202f4e132b8af0fd99"
        },
        "content": [
            "2. 英国央行：英国 7 月央行抵押贷款为 2. 3 亿英镑，预期 3 亿英镑，前值 1. 36 亿英镑；7 月央行消费信贷为 11.91 亿英镑，预期 13 亿英镑，前值 16.61 亿英镑。 （财联社）"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 11.999999046325684,
        "bold": False
    },
    {
        "id": "0c1daa0ec5de4d8980958c45c7d2feed",
        "parent": "59daa879d1024c2b9b0e8fcf53d70e46",
        "order": 10,
        "dst_type": "text",
        "attributes": {
            "level": 1,
            "position": {
                "bbox": {
                    "x1": 1472,
                    "y1": 11850,
                    "x2": 2774,
                    "y2": 12116,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 0,
            "hash": "de41661296c88c97164072792fe5a1ac58a18aff"
        },
        "content": [
            "􀂙 公司动态"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 11.999999046325684,
        "bold": True
    },
    {
        "id": "a79774ce17784faf97bbabf326314013",
        "parent": "0c1daa0ec5de4d8980958c45c7d2feed",
        "order": 11,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1480,
                    "y1": 12284,
                    "x2": 10526,
                    "y2": 13614,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 0,
            "hash": "c900d6c52b1100f204699cfeee57b998c0ee4766"
        },
        "content": [
            "1. 华润置地：上半年综合营业额为人民币 729. 7 亿元，同比微增 0. 1%，股东应占溢利为人民币 137.4 亿元，同比增长 29. 6%。上半年集团实现物业签约销售额人民币1,702.4 亿元，同比增长 40. 6%，实现签约面积 701 万平方米，同比增长 19.4%。 （财联社）"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 11.999999046325684,
        "bold": False
    },
    {
        "id": "4c84ea5a042d4fabb157e84d9495b469",
        "parent": "0c1daa0ec5de4d8980958c45c7d2feed",
        "order": 12,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1457,
                    "y1": 13818,
                    "x2": 10526,
                    "y2": 14788,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 0,
            "hash": "f7414589aefd579a1d4264a3902eabb632a6f220"
        },
        "content": [
            "2. 谷歌云：和英伟达宣布扩大合作伙伴关系，推进人工智能计算、软件和服务的发展。谷歌配置英伟达 H100 GPU 的新款超级计算机 A3 GPU 将于 9 月份大范围上市。（财联社）"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 11.999999046325684,
        "bold": False
    },
    {
        "id": "b63a7da2209a48d393901f4fc5796225",
        "parent": "0c1daa0ec5de4d8980958c45c7d2feed",
        "order": 13,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1457,
                    "y1": 14991,
                    "x2": 9223,
                    "y2": 15241,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 0,
            "hash": "b5a1ea4fce542f61f706130d031fc4424661b7e1"
        },
        "content": [
            "风险提示：经济增长不及预期，贸易保护主义的蔓延，美联储政策超预期。"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 11.999999046325684,
        "bold": True
    },
    {
        "id": "7a7cbe0265b4421592dd0cdcc8bef5ea",
        "parent": "0c1daa0ec5de4d8980958c45c7d2feed",
        "order": 14,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 3080,
                    "y1": 15397,
                    "x2": 8836,
                    "y2": 15615,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 0,
            "hash": "88b3ffcd7f5b4ed607b95a691e7baf470a0ebf04"
        },
        "content": [
            "本报告由川财证券有限责任公司编制 谨请参阅本页的重要声明"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 10.449999809265137,
        "bold": False
    },
    {
        "id": "47423f2e78a94a6cb5070ef5b6cf1231",
        "parent": "0c1daa0ec5de4d8980958c45c7d2feed",
        "order": 15,
        "dst_type": "image",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 5510,
                    "y1": 15693,
                    "x2": 6549,
                    "y2": 15993,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 0,
            "hash": "44fe78709631e61a692d7b6bc1ce6d758de476bd"
        },
        "content": [
            "http://kna-common.ks3-cn-beijing.ksyuncs.com/insight-ai/doc-parser/34c87df10d244414813e4ffb45ba3996_1.png?Signature=%2F34IWEKCi2w6LGgO%2FBwKF9Zd56Q%3D&Expires=3243324800&KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb"
        ],
        "image_pixel": [
            104,
            31
        ],
        "mark": None,
        "font_size": -1,
        "bold": False
    },
    {
        "id": "f4b1ceacec2c4bc39138206350de198a",
        "parent": "0c1daa0ec5de4d8980958c45c7d2feed",
        "order": 16,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 5921,
                    "y1": 15773,
                    "x2": 6209,
                    "y2": 15939,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 0,
            "hash": "803ab79310ab4c905d897e5b8b61272124906f8e"
        },
        "content": [
            "1/3"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 11.049999237060547,
        "bold": True
    },
    {
        "id": "a071ca4bb57b44de885c7a502a5e1e77",
        "parent": "59daa879d1024c2b9b0e8fcf53d70e46",
        "order": 17,
        "dst_type": "text",
        "attributes": {
            "level": 1,
            "position": {
                "bbox": {
                    "x1": 1439,
                    "y1": 1749,
                    "x2": 2541,
                    "y2": 2042,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 1,
            "hash": "418bd0ec0b2c463f2640eb8e73f83afd057d411a"
        },
        "content": [
            "川财证券"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 14.049999237060547,
        "bold": True
    },
    {
        "id": "bc23ed2ddf804589917697d36e6ec448",
        "parent": "a071ca4bb57b44de885c7a502a5e1e77",
        "order": 18,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1425,
                    "y1": 2175,
                    "x2": 10507,
                    "y2": 5701,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 1,
            "hash": "fe4ad145c699fd66e9d5f61be7c102fabe7d5cf6"
        },
        "content": [
            "川财证券有限责任公司成立于 1988 年 7 月,前身为经四川省人民政府批准、由四川省财政出资兴办的证券公司,是全国首家由财政国债中介机构整体转制而成的专业证券公司。经过三十余载的变革与成长,现今公司已发展成为由中国华电集团资本控股有限公司、四川省国有资产经营投资管理有限责任公司、四川省水电投资经营集团有限公司等资本和实力雄厚的大型企业共同持股的证券公司。公司一贯秉承诚实守信、专业运作、健康发展的经营理念, 矢志服务客户、服务社会,创造了良好的经济效益和社会效益;目前,公司是中国证券业协会、中国国债协会、上海证券交易所、深圳证券交易所、中国银行间市场交易商协会会员。"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 11.999999046325684,
        "bold": False
    },
    {
        "id": "bcc73edfb8b640a69177e110b18dee7e",
        "parent": "59daa879d1024c2b9b0e8fcf53d70e46",
        "order": 19,
        "dst_type": "text",
        "attributes": {
            "level": 1,
            "position": {
                "bbox": {
                    "x1": 1420,
                    "y1": 6213,
                    "x2": 2260,
                    "y2": 6506,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 1,
            "hash": "b5a0f7b0abd1d82545b7bf96115d565ee386f01c"
        },
        "content": [
            "研究所"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 14.049999237060547,
        "bold": True
    },
    {
        "id": "8daedb4b982340648be389380681fef5",
        "parent": "bcc73edfb8b640a69177e110b18dee7e",
        "order": 20,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1434,
                    "y1": 6639,
                    "x2": 10487,
                    "y2": 7825,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 1,
            "hash": "6ef27452f7b7b5fbbae701b007e3ec78854e48ca"
        },
        "content": [
            "川财证券研究所目前下设北京、上海、深圳、成都四个办公区域。团队成员主要来自国内一流学府。致力于为金融机构、企业集团和政府部门提供专业的研究、咨询和调研服务，以及投资综合解决方案。"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 11.999999046325684,
        "bold": False
    },
    {
        "id": "d5243faa0ae94966b28b88e8dbebe819",
        "parent": "bcc73edfb8b640a69177e110b18dee7e",
        "order": 21,
        "dst_type": "image",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 4737,
                    "y1": 8397,
                    "x2": 7168,
                    "y2": 10783,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 1,
            "hash": "da111184d29a0585e1013ce3b4d699bcac104e4f"
        },
        "content": [
            "http://kna-common.ks3-cn-beijing.ksyuncs.com/insight-ai/doc-parser/a045e23e8bd64f7fa08b5c2936a14b11_2.png?Signature=sHQS38yGc0wJmk7EMnSt9pE4S6c%3D&Expires=3243324800&KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb"
        ],
        "image_pixel": [
            244,
            240
        ],
        "mark": None,
        "font_size": -1,
        "bold": False
    },
    {
        "id": "4b7d5f6c694a4783b2b371127364f8d9",
        "parent": "bcc73edfb8b640a69177e110b18dee7e",
        "order": 22,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 3080,
                    "y1": 15397,
                    "x2": 8836,
                    "y2": 15615,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 1,
            "hash": "88b3ffcd7f5b4ed607b95a691e7baf470a0ebf04"
        },
        "content": [
            "本报告由川财证券有限责任公司编制 谨请参阅本页的重要声明"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 10.449999809265137,
        "bold": False
    },
    {
        "id": "f0e2cdbb47fa4133b76b27a6aca146de",
        "parent": "bcc73edfb8b640a69177e110b18dee7e",
        "order": 23,
        "dst_type": "image",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 5510,
                    "y1": 15693,
                    "x2": 6549,
                    "y2": 15993,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 1,
            "hash": "f2d698cde8f3af66f22daccd40f6161572cf8da0"
        },
        "content": [
            "http://kna-common.ks3-cn-beijing.ksyuncs.com/insight-ai/doc-parser/eb866af27bed4dc48e11b7ce9be919af_3.png?Signature=ETtIRyZfb8edu07aL16bf%2BOeN3w%3D&Expires=3243324800&KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb"
        ],
        "image_pixel": [
            104,
            31
        ],
        "mark": None,
        "font_size": -1,
        "bold": False
    },
    {
        "id": "5ac2e8fdbdcc4b3e86ff7407acaf160a",
        "parent": "bcc73edfb8b640a69177e110b18dee7e",
        "order": 24,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 5909,
                    "y1": 15773,
                    "x2": 6209,
                    "y2": 15939,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 1,
            "hash": "a8792c02824141504af55abd0bbe54ed6f5e38fc"
        },
        "content": [
            "2/3"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 11.049999237060547,
        "bold": True
    },
    {
        "id": "6902aad9b1a94cde91e3505847f65afa",
        "parent": "59daa879d1024c2b9b0e8fcf53d70e46",
        "order": 25,
        "dst_type": "text",
        "attributes": {
            "level": 1,
            "position": {
                "bbox": {
                    "x1": 1432,
                    "y1": 1749,
                    "x2": 2822,
                    "y2": 2042,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 2,
            "hash": "0cbbae94f391d9f6916b3215509bf1e8df48487a"
        },
        "content": [
            "分析师声明"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 14.049999237060547,
        "bold": True
    },
    {
        "id": "0f1a35e2bda64e6f91bae9e546dac7b6",
        "parent": "6902aad9b1a94cde91e3505847f65afa",
        "order": 26,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1419,
                    "y1": 2099,
                    "x2": 10418,
                    "y2": 2767,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 2,
            "hash": "a4828882c1a86bc5f7e3b078973f0465a9b6a908"
        },
        "content": [
            "本人具有中国证券业协会授予的证券投资咨询执业资格并注册为证券分析师，以勤勉尽责的职业态度、专业审慎的研究方法，使用合法合规的信息，独立、客观地出具本报告。本人薪酬的任何部分过去不曾与、现在不与、未来也不会与本报告中的具体推荐意见或观点直接或间接相关。"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.999999046325684,
        "bold": False
    },
    {
        "id": "ff39d0c9cb2e4685802beb0c76358ed5",
        "parent": "59daa879d1024c2b9b0e8fcf53d70e46",
        "order": 27,
        "dst_type": "text",
        "attributes": {
            "level": 1,
            "position": {
                "bbox": {
                    "x1": 1434,
                    "y1": 3268,
                    "x2": 3105,
                    "y2": 3561,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 2,
            "hash": "8568f5a90fcbcf9718eb147c2b5b93b6031a9f36"
        },
        "content": [
            "行业公司评级"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 14.049999237060547,
        "bold": True
    },
    {
        "id": "0189fde83d3d4243bf81e4bf125ef43d",
        "parent": "ff39d0c9cb2e4685802beb0c76358ed5",
        "order": 28,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1423,
                    "y1": 3619,
                    "x2": 10486,
                    "y2": 4046,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 2,
            "hash": "1d9fb0caae56d91d82eaded0ec53f995e93eb5c7"
        },
        "content": [
            "证券投资评级：以研究员预测的报告发布之日起 6 个月内证券的绝对收益为分类标准。 30%以上为买入评级； 15%-30%为增持评级； -15%-15%为中性评级； -15%以下为减持评级。"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.999999046325684,
        "bold": False
    },
    {
        "id": "8b9f14ab0ea040dbbb830604dfd79efb",
        "parent": "ff39d0c9cb2e4685802beb0c76358ed5",
        "order": 29,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1426,
                    "y1": 4219,
                    "x2": 10487,
                    "y2": 4646,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 2,
            "hash": "cfd6018e390b7feaf680561c086f391169bb4b5b"
        },
        "content": [
            "行业投资评级：以研究员预测的报告发布之日起 6 个月内行业相对市场基准指数的收益为分类标准。 30%以上为买入评级； 15%-30%为增持评级； -15%-15%为中性评级； -15%以下为减持评级。"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.999999046325684,
        "bold": False
    },
    {
        "id": "dd7aca4ef2b047708c3672b3fa412c2d",
        "parent": "59daa879d1024c2b9b0e8fcf53d70e46",
        "order": 30,
        "dst_type": "text",
        "attributes": {
            "level": 1,
            "position": {
                "bbox": {
                    "x1": 1436,
                    "y1": 5188,
                    "x2": 2541,
                    "y2": 5481,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 2,
            "hash": "4ce2ae10c17fff52af8809ce9a27e5833ca76965"
        },
        "content": [
            "重要声明"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 14.049999237060547,
        "bold": True
    },
    {
        "id": "007fe141e69d4da7b931c429c58de0d8",
        "parent": "dd7aca4ef2b047708c3672b3fa412c2d",
        "order": 31,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1431,
                    "y1": 5539,
                    "x2": 10418,
                    "y2": 6446,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 2,
            "hash": "6f8fc3adb615d776708e8d934539df6523fe4e2a"
        },
        "content": [
            "本报告由川财证券有限责任公司（已具备中国证监会批复的证券投资咨询业务资格）制作。本报告仅供川财证券有限责任公司（以下简称“本公司 ”）客户使用。本公司不因接收人收到本报告而视其为客户，与本公司无直接业务关系的阅读者不是本公司客户，本公司不承担适当性职责。本报告在未经本公司公开披露或者同意披露前，系本公司机密材料，如非本公司客户接收到本报告，请及时退回并删除，并予以保密。"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.999999046325684,
        "bold": False
    },
    {
        "id": "0f44045a039a45cfa181bb590f969cd8",
        "parent": "dd7aca4ef2b047708c3672b3fa412c2d",
        "order": 32,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1421,
                    "y1": 6619,
                    "x2": 10463,
                    "y2": 8246,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 2,
            "hash": "3bd72f0c5c866dd614ecb785ea00d72682f5ee21"
        },
        "content": [
            "本报告基于本公司认为可靠的、已公开的信息编制，但本公司对该等信息的真实性、准确性及完整性不作任何保证。本报告所载的意见、评估及预测仅为本报告出具日 的观点和判断，该等意见、评估及预测无需通知即可随时更改。在不同时期，本公司可能会发出与本报告所载意见、评估及预测不一致的研究报告。同时，本报告所指的证券或投资标的的价格、价值及投资收入可能会波动。本公司不保证本报告所含信息保持在最新状态。对于本公司其他专业人士（包括但不限于销售人员、交易人员）根据不同假设、研究方法、即时动态信息及市场表现，发表的与本报告不一致的分析评论或交易观点，本公司没有义务向本报告所有接收者进行更新。本公司对本报告所含信息可在不发出通知的情形下做出修改，投资者应当自行关注相应的更新或修改。"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.999999046325684,
        "bold": False
    },
    {
        "id": "ce86bdf97c2f4bbbb7f7ab6491dde091",
        "parent": "dd7aca4ef2b047708c3672b3fa412c2d",
        "order": 33,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1419,
                    "y1": 8419,
                    "x2": 10418,
                    "y2": 10286,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 2,
            "hash": "6b771a78fd76b09d6d3d5c14219b745ac20adc9c"
        },
        "content": [
            "本公司力求报告内容客观、公正，但本报告所载的观点、结论和建议仅供投资者参考之用，并非作为购买或出售证券或其他投资标的的邀请或保证。该等观点、建议并未考虑到获取本报告人员的具体投资目 的、财务状况以及特定需求，在任何时候均不构成对客户私人投资建议。根据本公司《产品或服务风险等级评估管理办法》，上市公司价值相关研究报告风险等级为中低风险，宏观政策分析报告、行业研究分析报告、其他报告风险等级为低风险。本公司特此提示，投资者应当充分考虑自身特定状况，并完整理解和使用本报告内容，不应视本报告为做出投资决策的唯一因素，必要时应就法律、商业、财务、税收等方面咨询专业财务顾问的意见。本公司以往相关研究报告预测与分析的准确，也不预示与担保本报告及本公司今后相关研究报告的表现。对依据或者使用本报告及本公司其他相关研究报告所造成的一切后果，本公司及作者不承担任何法律责任。"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.999999046325684,
        "bold": False
    },
    {
        "id": "981d277ff84f44d5bb35d480186554e6",
        "parent": "dd7aca4ef2b047708c3672b3fa412c2d",
        "order": 34,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1421,
                    "y1": 10459,
                    "x2": 10418,
                    "y2": 11366,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 2,
            "hash": "946c4bb603f6d13d8c77dd60326f5b44283b45f4"
        },
        "content": [
            "本公司及作者在自身所知情的范围内，与本报告所指的证券或投资标的不存在法律禁止的利害关系。投资者应当充分考虑到本公司及作者可能存在影响本报告观点客观性的潜在利益冲突。在法律许可的情况下，本公司及其所属关联机构可能会持有报告中提到的公司所发行的证券头寸并进行交易，也可能为之提供或者争取提供投资银行、财务顾问或者金融产品等相关服务。本公司的投资业务部门可能独立做出与本报告中的意见或建议不一致的投资决策。"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.999999046325684,
        "bold": False
    },
    {
        "id": "541dc21bc7c241b496c6636e6c8c4582",
        "parent": "dd7aca4ef2b047708c3672b3fa412c2d",
        "order": 35,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1423,
                    "y1": 11539,
                    "x2": 10418,
                    "y2": 11966,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 2,
            "hash": "8daa902ce61004187b663d31683158d4d0baca2f"
        },
        "content": [
            "对于本报告可能附带的其它网站地址或超级链接，本公司不对其内容负责，链接内容不构成本报告的任何部分，仅为方便客户查阅所用，浏览这些网站可能产生的费用和风险由使用者自行承担。"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.999999046325684,
        "bold": False
    },
    {
        "id": "8291673e31904cca9990992fdae90b01",
        "parent": "dd7aca4ef2b047708c3672b3fa412c2d",
        "order": 36,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1426,
                    "y1": 12139,
                    "x2": 10487,
                    "y2": 12806,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 2,
            "hash": "f2bb88b6d060c23dce6cf9549ce96ffa029ba39e"
        },
        "content": [
            "本公司关于本报告的提示（包括但不限于本公司工作人员通过电话、短信、邮件、微信、微博、博客、QQ、视频网站、百度官方贴吧、论坛、 BBS）仅为研究观点的简要沟通，投资者对本报告的参考使用须以本报告的完整版本为准。"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.999999046325684,
        "bold": False
    },
    {
        "id": "4c738ef715524c28a514c69b6c45925e",
        "parent": "dd7aca4ef2b047708c3672b3fa412c2d",
        "order": 37,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1421,
                    "y1": 12979,
                    "x2": 10418,
                    "y2": 14126,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 2,
            "hash": "047312cdbf888f0ff961394a5bc8db5e37e9ace1"
        },
        "content": [
            "本报告版权仅为本公司所有。未经本公司书面许可，任何机构或个人不得以翻版、复制、发表、引用或再次分发他人等任何形式侵犯本公司版权。如征得本公司同意进行引用、刊发的，需在允许的范围内使用，并注明出处为“川财证券研究所 ”，且不得对本报告进行任何有悖原意的引用、删节和修改。如未经川财证券授权，私自转载或者转发本报告，所引起的一切后果及法律责任由私自转载或转发者承担。本公司保留追究相关责任的权利。所有本报告中使用的商标、服务标记及标记均为本公司的商标、服务标记及标记。"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.999999046325684,
        "bold": False
    },
    {
        "id": "8e0c4066af4445718f7d5e63e6768cf0",
        "parent": "dd7aca4ef2b047708c3672b3fa412c2d",
        "order": 38,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1418,
                    "y1": 14299,
                    "x2": 10418,
                    "y2": 14966,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 2,
            "hash": "683f1dd82b690a448b2371fdca0169eb126f5e03"
        },
        "content": [
            "本提示在任何情况下均不能取代您的投资判断，不会降低相关产品或服务的固有风险，既不构成本公司及相关从业人员对您投资本金不受损失的任何保证，也不构成本公司及相关从业人员对您投资收益的任何保证，与金融产品或服务相关的投资风险、履约责任以及费用等将由您自行承担。"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.999999046325684,
        "bold": False
    },
    {
        "id": "77930df0a321443a8a4cfd99ce3828df",
        "parent": "dd7aca4ef2b047708c3672b3fa412c2d",
        "order": 39,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 1431,
                    "y1": 15139,
                    "x2": 8920,
                    "y2": 15326,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 2,
            "hash": "083938d69d1830d1f87c0ea9bdfc5dbba51eb8a6"
        },
        "content": [
            "本公司具有中国证监会核准的“证券投资咨询 ” 业务资格，经营许可证编号为： 000000029399"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 8.999999046325684,
        "bold": False
    },
    {
        "id": "d5491294c37d4aef87b87021362e77a1",
        "parent": "dd7aca4ef2b047708c3672b3fa412c2d",
        "order": 40,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 3080,
                    "y1": 15397,
                    "x2": 8836,
                    "y2": 15615,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 2,
            "hash": "88b3ffcd7f5b4ed607b95a691e7baf470a0ebf04"
        },
        "content": [
            "本报告由川财证券有限责任公司编制 谨请参阅本页的重要声明"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 10.449999809265137,
        "bold": False
    },
    {
        "id": "9f4963bb63804d2b829781ccb4eddfec",
        "parent": "dd7aca4ef2b047708c3672b3fa412c2d",
        "order": 41,
        "dst_type": "image",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 5510,
                    "y1": 15693,
                    "x2": 6549,
                    "y2": 15993,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 2,
            "hash": "7ccf57ab58fe8de3cd58fad1e9b94590a34c97b2"
        },
        "content": [
            "http://kna-common.ks3-cn-beijing.ksyuncs.com/insight-ai/doc-parser/3706b1b15c85496a9d452cc612e8afa2_4.png?Signature=9tMtbrbsoBF1PYcK4wdkLoVHy5U%3D&Expires=3243324800&KSSAccessKeyId=AKLTIVEbT9FoQ9OHqj9Ahrtb"
        ],
        "image_pixel": [
            104,
            31
        ],
        "mark": None,
        "font_size": -1,
        "bold": False
    },
    {
        "id": "7e17d8062f5c4828a6ffeb590e200308",
        "parent": "dd7aca4ef2b047708c3672b3fa412c2d",
        "order": 42,
        "dst_type": "text",
        "attributes": {
            "level": 10,
            "position": {
                "bbox": {
                    "x1": 5912,
                    "y1": 15773,
                    "x2": 6209,
                    "y2": 15939,
                    "rotate": None
                },
                "block_coordinate": None
            },
            "page": 2,
            "hash": "62658eb98057620ec31f189e9f03c2d02d34463b"
        },
        "content": [
            "3/3"
        ],
        "image_pixel": None,
        "mark": None,
        "font_size": 11.049999237060547,
        "bold": True
    }
]


class TestPdfParse(TestCase):

    def setUp(self):
        self.parser = PdfParse()
        self.dsts = kdc_pdf_data_3()
        self.contxt = PipelineContext()
    def tearDown(self):
        pass

    def test_dst_generate(self):
        # self.contxt.file_info=FileInfo()
        dst = convert_to_dst_list(self.dsts)
        dst_list = self.parser.dst_reprocess(self.contxt,dst)
        print(print_dst_tree(dst_list))