# coding: utf-8
# <AUTHOR> dengfuhua
# @date    : 2025/4/22
from typing import List, Dict
import logging
import pandas as pd
import io
import hashlib
from bs4 import BeautifulSoup
from modules.entity.dst_entity import FileType, DST, DSTType, DSTAttribute, ExcelCoordinate
from modules.entity.kdc_enttiy import WorkBook, KDC_DOC_FIELD, CellDataType
from modules.flows.dst_builder.parse_template import ParseTemplate
from modules.common import build_root_dst, build_dst_id, build_media_map
from modules.pipeline.context import PipelineContext


def generate_html_table(data, spans) -> str:
    """
    generate html table from data and spans
    :param data: list of list, data of table
    :param spans: list of dict, spans of table
    :return: str, html table
    """
    html_table = "<table border='1'>\n"
    html_table += "<meta charset='utf-8'>\n"
    row_and_col = []
    if not data:
        return ""
    for idx_of_row, row in enumerate(data):
        ls_row = []
        for idx_of_col, cell in enumerate(row):
            cell = cell.replace("\n", "<br>")
            if spans and len(spans) > 0:
                for span in spans:
                    if span.row_spans.from_ == idx_of_row and span.col_spans.from_ == idx_of_col:
                        ls_row.append(
                            {"content": cell, "rowspan": span.row_spans.to - span.row_spans.from_ + 1,
                             "colspan": span.col_spans.to - span.col_spans.from_ + 1})
                        break
                else:
                    ls_row.append({"content": cell, "rowspan": 1, "colspan": 1})
            else:
                ls_row.append({"content": cell, "rowspan": 1, "colspan": 1})
        row_and_col.append(ls_row)
    # 根据row_and_col的colspan和rowspan属性，将每一行中多余的单元格删除
    for idx_of_row, row in enumerate(row_and_col):
        for idx_of_col, cell in enumerate(row):
            colspan = cell["colspan"]
            rowspan = cell["rowspan"]
            if colspan > 1:
                for i in range(1, colspan):
                    row[idx_of_col + i]["content"] = "delete"
            if rowspan > 1:
                for i in range(0, colspan):
                    for j in range(1, rowspan):
                        row_and_col[idx_of_row + j][idx_of_col + i]["content"] = "delete"
    for row in row_and_col:
        html_table_row = "<tr>"
        for cell in row:
            if cell["content"] != "delete" and cell["content"] != "":
                html_table_row += f"<td rowspan='{cell['rowspan']}' colspan='{cell['colspan']}'>{cell['content']}</td>"
        html_table_row += "</tr>\n"
        if html_table_row != "<tr></tr>\n":
            html_table += html_table_row
    html_table += "</table>"
    return html_table


async def extract_content_for_et(context: PipelineContext, kdc_doc_list: List[WorkBook]) -> dict:
    shared_strings = kdc_doc_list[0].shared_strings
    sheets = kdc_doc_list[0].sheets
    # 如果cell_images存在，将其bytes的图片上传到ks3，并将其url替换为ks3的url
    id2url = await build_media_map(context, kdc_doc_list[0].cell_images)

    dict_of_json = {}  # 动态创建dict_of_json
    key = 1
    for sheet in sheets:
        title = ""
        if sheet.used_range:
            # 边界条件: 如果used_range中的col_spans和row_spans非常大，会导致list_of_table的内存占用过大，实际上一般这些都是空白的单元格，所以不需要解析
            # len_of_col = sheet.get("used_range").get("col_spans").get("to") - sheet.get("used_range").get("col_spans").get("from") + 1
            # len_of_row = sheet.get("used_range").get("row_spans").get("to") - sheet.get("used_range").get("row_spans").get("from") + 1
            # list_of_table = [["" for _ in range(len_of_col)] for _ in range(len_of_row)]
            list_of_table = []
            if len(sheet.data) > 0:
                title = sheet.name
                # len_of_row = len([row for row in sheet["data"] if "cells" in row])
                # len_of_row 更新为最后一个存在cells的行的index+1
                len_of_row = 0
                for row in sheet.data:
                    if row.cells:
                        len_of_row = row.index + 1
                ls_len_of_col = []
                for row in sheet.data:
                    if row.spans:
                        ls_len_of_col.append(row.spans.to + 1)
                    else:
                        ls_len_of_col.append(0)
                len_of_col = max(ls_len_of_col)
                # 如果存在合并单元格，则取原有值和合并单元格的col_spans.to+1的最大值
                if sheet.merge_cells:
                    len_of_col = max([len_of_col, max([cell.col_spans.to for cell in sheet.merge_cells]) + 1])
                    len_of_row = max([len_of_row, max([cell.row_spans.to for cell in sheet.merge_cells]) + 1])
                list_of_table = [["" for _ in range(len_of_col)] for _ in range(len_of_row)]
                for idx_of_row, row in enumerate(sheet.data):
                    if row.cells:
                        for cell in row.cells:
                            if cell.type:
                                match cell.type:
                                    case CellDataType.shared:
                                        list_of_table[idx_of_row][cell.index] = "".join(
                                            [item.text for item in shared_strings[int(cell.value)].items])
                                    case _:
                                        if cell.value:
                                            # 如果cell中存在image_id，将其替换为img标签
                                            if cell.image_id:
                                                image_id = cell.image_id
                                                cell_image_url = id2url.get(image_id)
                                                if not cell_image_url:
                                                    logging.warning(
                                                        f"extract_content_for_et: image_id: {image_id} does not exist in id2url.")
                                                list_of_table[idx_of_row][
                                                    cell.index] = f"<img src='{cell_image_url}'/>"
                                            else:
                                                list_of_table[idx_of_row][cell.index] = cell.value
                                        else:
                                            continue
                            else:
                                continue
            merge_cells = sheet.merge_cells
            if merge_cells:
                # 对merge_cells进行排序，排序键值的顺序为：row_spans.from, col_spans.from, col_spans.to, row_spans.to
                merge_cells = sorted(merge_cells, key=lambda x: (
                    x.row_spans.from_, x.col_spans.from_, x.col_spans.to, x.row_spans.to))
            content = generate_html_table(list_of_table, merge_cells)
            dict_of_json[f"1/{str(key)}"] = {"title": title, "content": content, "type": "table", "page_num": 0,
                                             "bbox": [], "meta": {"url": ""}}
            key += 1
    return dict_of_json


def extract_img_positions(html_content, nodes, page_no, root, sheet_name, order):
    soup = BeautifulSoup(html_content, "html.parser")
    table = soup.find("table")

    if not table:
        return

    rows = table.find_all("tr")
    for row_idx, row in enumerate(rows):
        cells = row.find_all(["td", "th"])
        for col_idx, cell in enumerate(cells):
            if cell.find("img"):
                # 计算Excel位置
                col_letter = num_to_col(col_idx + 1)  # 列从1开始
                row_number = row_idx + 1  # 行从1开始
                excel_position = f"{col_letter}{row_number}"
                img_url = [img["src"] for img in cell.find_all("img") if "src" in img.attrs][0]
                content_hash = hashlib.sha1(img_url.encode()).hexdigest()
                position = ExcelCoordinate(sheet=sheet_name, top_left_span=excel_position,
                                           bottom_left_span=excel_position,
                                           top_right_span=excel_position,
                                           bottom_right_span=excel_position)
                attributes = DSTAttribute(level=0, position=position, page=page_no, hash=content_hash)
                sheet_dst = DST(
                    id=build_dst_id(),
                    parent=root.id,
                    order=order,
                    dst_type=DSTType("image"),
                    attributes=attributes,
                    content=[img_url],
                )
                nodes.append(sheet_dst)
                order += 1


def num_to_col(n):
    """将数字转换为Excel列字母"""
    string = ""
    while n > 0:
        n, remainder = divmod(n - 1, 26)
        string = chr(65 + remainder) + string
    return string


def row_process(content, nodes, page_no, root, sheet_name):
    table_df = pd.read_html(io.StringIO(content))[0]
    # 提取表头
    headers = table_df.iloc[0]
    # 从第二行开始，将剩余的行按每10行分成一组，并剔除全是NaN的行
    data_df = table_df.iloc[1:].dropna(how='all')
    dfs = [data_df.iloc[i:i + 10] for i in range(0, len(data_df), 10)]
    # dfs = np.array_split(data_df, np.arange(10, len(data_df), 10))
    # 记录原始表格的位置信息
    row_indices = {i: idx for idx, i in enumerate(table_df.index)}
    col_indices = {col: idx for idx, col in enumerate(table_df.columns)}
    for i, df in enumerate(dfs):
        # 将表头添加到每组数据的前面
        df_with_header = pd.concat([headers.to_frame().T, df], ignore_index=True)

        # 计算每组10行在原始表格中的位置范围
        start_row = row_indices[df.index[0]] + 1  # +1 因为Excel索引从1开始
        end_row = start_row + len(df) - 1
        start_col = col_indices[df.columns[0]] + 1  # +1 因为Excel索引从1开始
        end_col = start_col + len(df.columns) - 1

        # 将列索引转换为Excel列字母
        start_col_letter = num_to_col(start_col)
        end_col_letter = num_to_col(end_col)

        # 将 DataFrame 转换为 HTML
        html_content = df_with_header.to_html(index=False)

        content_hash = hashlib.sha1(html_content.encode()).hexdigest()
        position = ExcelCoordinate(sheet=sheet_name, top_left_span=f"{start_col_letter}{start_row}",
                                   bottom_left_span=f"{start_col_letter}{end_row}",
                                   top_right_span=f"{end_col_letter}{start_row}",
                                   bottom_right_span=f"{end_col_letter}{end_row}")
        attributes = DSTAttribute(level=0, position=position, page=page_no, hash=content_hash)
        sheet_dst = DST(
            id=build_dst_id(),
            parent=root.id,
            order=i,
            dst_type=DSTType("table"),
            attributes=attributes,
            content=[html_content],
        )
        nodes.append(sheet_dst)
    extract_img_positions(content, nodes, page_no, root, sheet_name, len(dfs))


class XlsParse(ParseTemplate):
    def __init__(self):
        super().__init__(FileType.XLS)
        self.kdc_data = None
        self.dst = None

    def _kdc_validate(self) -> List[WorkBook]:
        kdc_doc_list = []
        for idx, item in enumerate(self.kdc_data):
            if item is None:
                logging.error(f"EtParse._kdc_validate error: kdc item is None")
                continue
            if KDC_DOC_FIELD not in item.keys() and item[KDC_DOC_FIELD] is None:
                logging.error(f"EtParse._kdc_validate error: doc is none or doc not in kdc item.keys():{item.keys()}")
                continue
            kdc_doc_list.append(WorkBook.model_validate(item[KDC_DOC_FIELD]))
        return kdc_doc_list

    async def dst_generate(self, context: PipelineContext, kdc_data: List[dict]) -> None |  Dict[int, List[DST]]:
        self.kdc_data = kdc_data
        kdc_doc_list = self._kdc_validate()
        if len(kdc_doc_list) == 0:
            logging.error(f"EtParse.dst_generate error: kdc_doc_list is empty")
            return None
        root = build_root_dst()
        nodes: List[DST] = [root]

        page_no = 0
        result = await extract_content_for_et(context, kdc_doc_list)

        for key, value in result.items():
            content = value["content"]
            sheet_name = value["title"]
            try:
                row_process(content, nodes, page_no, root, sheet_name)
                page_no += 1

            except ValueError as e:
                logging.error(f"Failed to parse sheet {key}: {e}")

        return {0: nodes}

    def dst_reprocess(self, context: PipelineContext, dst: List[DST]):
        pass

    def get_res(self, dst: DST):
        pass
