# coding: utf-8
# <AUTHOR> xuwen<PERSON><PERSON>
# @date    : 2025/4/23


import hashlib
import re
from abc import ABC, abstractmethod
from typing import Dict, List, Any

from modules.common import build_dst_id, table_entity2html
from modules.entity.dst_entity import BBox, DSTAttribute, DSTType, DST, BlockCoordinate, PositionInfo
from modules.entity.kdc_enttiy import Block, BlockType


def replace_space(text:str):
    pattern = re.compile(r'[\u0000-\u001F\u007F\u00A0\u2000-\u200F\u3000]')
    return pattern.sub(' ', text)  # 替换为普通空格


def parse_string(s: str) -> tuple[str, int, int, bool]:
    """
    解析字符串，支持两种格式：
    1. 格式一：'OrrzmFhNdteeQlae_2_13' - 使用下划线分隔
    2. 格式二：'ajNMzhAFl72eF336v531' - 混合字母数字，提取最后两组数字

    Args:
        s (str): 需要解析的字符串

    Returns:
        tuple: (block_id, gcp, length, success)
        - block_id: 字符串部分
        - gcp: 全局字符位置
        - length: 长度
        - success: 是否成功解析
    """
    # 格式一：使用下划线分隔的格式
    if '_' in s:
        parts = s.split('_')
        if len(parts) >= 3:
            try:
                block_id = parts[0]
                gcp = int(parts[1])
                length = int(parts[2])
                return block_id, gcp, length, True
            except ValueError:
                return s, 0, 0, False

    # 格式二：混合字母数字格式，提取数字组
    # 使用正则表达式找到所有连续的数字
    numbers = re.findall(r'\d+', s)
    if len(numbers) >= 2:
        try:
            # 最后一个数字是gcp，倒数第二个数字是length
            gcp = int(numbers[-1])
            length = int(numbers[-2])
            block_id = s  # 整个字符串作为block_id
            return block_id, gcp, length, True
        except ValueError:
            return s, 0, 0, False

    # 如果都不匹配，返回失败
    return s, 0, 0, False


class TreeNode(ABC):
    def __init__(self, block: Block, id2url: Dict[str, List[Any]], id2text: Dict[str, str]):
        self.block = block
        self.id2url = id2url
        self.id2text = id2text

    @abstractmethod
    def process(self, parent_outline_level: int, parent_id: str, index: int, ls: List[DST]) -> str:
        pass


class ComponentNode(TreeNode):
    def process(self, outline_level: int, parent_id: str, index: int, ls: List[DST]) -> str:
        component = self.block.component
        dst_id = build_dst_id()
        content_data = self.id2url.get(component.media_id)
        if not content_data:
            return parent_id
        content = content_data[0]
        image_dimensions = content_data[1]
        page_num = self.block.page_index
        if not page_num:
            page_num = 0
        block_msg = self.block.id
        bbox = self.block.bounding_box
        if not block_msg and not bbox:
            return parent_id
        bid, gcp, length, success = parse_string(block_msg)
        if success:
            block_coordinate = BlockCoordinate(block_id=bid, gcp=gcp, len=length)
        else:
            block_coordinate = None

        if bbox:
            b_box = BBox(
                x1=bbox.x1,
                y1=bbox.y1,
                x2=bbox.x2,
                y2=bbox.y2
            )
        else:
            b_box = None
        position = PositionInfo(block_coordinate=block_coordinate, bbox=b_box)
        content = replace_space(content)

        content_hash = hashlib.sha1(content.encode()).hexdigest()

        attributes = DSTAttribute(level=outline_level, position=position, page=page_num, hash=content_hash)
        try:
            dst_type = DSTType(component.type.value.lower())
        except ValueError:
            dst_type = DSTType.OTHER
        dst = DST(
            id=dst_id,
            parent=parent_id,
            dst_type=dst_type,
            attributes=attributes,
            content=[content],
            order=index,
            image_pixel=image_dimensions
        )
        ls.append(dst)
        return dst_id


class TextboxNode(TreeNode):
    def process(self, outline_level: int, parent_id: str, index: int, ls: List[DST]) -> str:
        dst_id = build_dst_id()
        content = ""
        font_size = -1
        bold = False
        for block in self.block.textbox.blocks or []:
            if block.type == BlockType.para:
                for run in block.para.runs or []:
                    if run.text and (run.id not in self.id2text or run.text in self.id2text[run.id]):
                        content += run.text
                    if run.prop:
                        if run.prop.size:
                            font_size = run.prop.size
                        if run.prop.bold is not None:
                            bold = run.prop.bold

        page_num = self.block.page_index
        if not page_num:
            page_num = 0
        block_msg = self.block.id
        bbox = self.block.bounding_box
        if not block_msg and not bbox:
            return parent_id
        bid, gcp, length, success = parse_string(block_msg)
        if success:
            block_coordinate = BlockCoordinate(block_id=bid, gcp=gcp, len=length)
        else:
            block_coordinate = None

        if bbox:
            b_box = BBox(
                x1=bbox.x1,
                y1=bbox.y1,
                x2=bbox.x2,
                y2=bbox.y2
            )
        else:
            b_box = None
        position = PositionInfo(block_coordinate=block_coordinate, bbox=b_box)
        content = replace_space(content)
        content_hash = hashlib.sha1(content.encode()).hexdigest()
        attributes = DSTAttribute(level=outline_level, position=position, page=page_num, hash=content_hash)
        if self.block.para is not None and self.block.para.prop is not None and font_size is None:
            font_size = self.block.para.prop.def_run_prop.size if self.block.para.prop.def_run_prop else -1
            bold = self.block.para.prop.def_run_prop.bold if self.block.para.prop.def_run_prop else False
        dst = DST(
            id=dst_id,
            parent=parent_id,
            dst_type=DSTType.TEXT,
            attributes=attributes,
            content=[content],
            order=index,
            font_size=font_size,
            bold=bold,
        )
        ls.append(dst)
        return dst_id


class TableNode(TreeNode):

    def get_bounding_box(self) -> BBox:
        """
        Retrieves the bounding box for the block. If the block's bounding box is None,
        it searches through the table rows and cells for the first valid bounding box.

        :return: The first valid bounding box or None if not found.
        """
        bbox = self.block.bounding_box
        if bbox is None and self.block.table and self.block.table.rows:
            for row in self.block.table.rows:
                for cell in row.cells:
                    if cell.bounding_box:
                        return cell.bounding_box
                    if cell.blocks:
                        for block in cell.blocks:
                            if block.bounding_box:
                                return block.bounding_box
        return bbox

    async def process(self, outline_level: int, parent_id: str, index: int, ls: List[DST]) -> str:
        dst_id = build_dst_id()
        content = await table_entity2html(self.block.table, self.id2url, self.id2text)
        page_num = self.block.page_index
        if not page_num:
            page_num = 0
        block_msg = self.block.id
        bbox = self.get_bounding_box()
        if not block_msg and not bbox:
            return parent_id
        bid, gcp, length, success = parse_string(block_msg)
        if success:
            block_coordinate = BlockCoordinate(block_id=bid, gcp=gcp, len=length)
        else:
            block_coordinate = None

        if bbox:
            b_box = BBox(
                x1=bbox.x1,
                y1=bbox.y1,
                x2=bbox.x2,
                y2=bbox.y2
            )
        else:
            b_box = None
        position = PositionInfo(block_coordinate=block_coordinate, bbox=b_box)
        content_hash = hashlib.sha1(content.encode()).hexdigest()

        attributes = DSTAttribute(level=outline_level, position=position, page=page_num, hash=content_hash)
        dst = DST(
            id=dst_id,
            parent=parent_id,
            dst_type=DSTType.TABLE,
            attributes=attributes,
            content=[content],
            order=index,
        )
        ls.append(dst)
        return dst_id


class ParaNode(TreeNode):
    def process(self, outline_level: int, parent_id: str, index: int, ls: List[DST]) -> str:
        dst_id = build_dst_id()
        content = []
        if self.block.para.prop:
            if self.block.para.prop.list_string is not None:
                content.append(self.block.para.prop.list_string)
            # content.append(self.block.para.prop.list_string)
            outline_level = self.block.para.prop.outline_level
        if self.block.para.runs:
            for run in self.block.para.runs or []:
                if run.text and (run.id not in self.id2text or run.text in self.id2text[run.id]):
                    content.append(run.text)
        else:
            return parent_id
        page_num = self.block.page_index
        if not page_num:
            page_num = 0
        block_msg = self.block.id
        bbox = self.block.bounding_box
        if not block_msg and not bbox:
            return parent_id
        bid, gcp, length, success = parse_string(block_msg)
        if success:
            block_coordinate = BlockCoordinate(block_id=bid, gcp=gcp, len=length)
        else:
            block_coordinate = None

        if bbox:
            b_box = BBox(
                x1=bbox.x1,
                y1=bbox.y1,
                x2=bbox.x2,
                y2=bbox.y2
            )
        else:
            b_box = None
        position = PositionInfo(block_coordinate=block_coordinate, bbox=b_box)
        content_str = "".join(content)
        content_str = replace_space(content_str)
        content_hash = hashlib.sha1(content_str.encode()).hexdigest()
        attributes = DSTAttribute(level=outline_level, position=position, page=page_num, hash=content_hash)
        dst = DST(

            id=dst_id,
            parent=parent_id,
            dst_type=DSTType.TEXT,
            attributes=attributes,
            content=content,
            order=index,
        )
        ls.append(dst)
        return dst_id


class HighLightNode(TreeNode):
    def process(self, outline_level: int, parent_id: str, index: int, ls: List[DST]) -> str:
        dst_id = build_dst_id()
        content = []
        for block in self.block.highlight.blocks or []:
            if block.type == BlockType.para:
                for run in block.para.runs or []:
                    if run.text and (run.id not in self.id2text or run.text in self.id2text[run.id]):
                        content.append(run.text)
        page_num = self.block.page_index
        if not page_num:
            page_num = 0
        block_msg = self.block.id
        bbox = self.block.bounding_box
        if not block_msg and not bbox:
            return parent_id
        bid, gcp, length, success = parse_string(block_msg)
        if success:
            block_coordinate = BlockCoordinate(block_id=bid, gcp=gcp, len=length)
        else:
            block_coordinate = None

        if bbox:
            b_box = BBox(
                x1=bbox.x1,
                y1=bbox.y1,
                x2=bbox.x2,
                y2=bbox.y2
            )
        else:
            b_box = None
        position = PositionInfo(block_coordinate=block_coordinate, bbox=b_box)
        content_str = "".join(content)
        content_str = replace_space(content_str)
        content_hash = hashlib.sha1(content_str.encode()).hexdigest()
        attributes = DSTAttribute(level=outline_level, position=position, page=page_num, hash=content_hash)
        dst = DST(
            id=dst_id,
            parent=parent_id,
            dst_type=DSTType.TEXT,
            attributes=attributes,
            content=content,
            order=index,
        )
        ls.append(dst)
        return dst_id
