import copy
import logging
import time
from typing import List, Dict, Optional

from bs4 import <PERSON>Sou<PERSON>
from pydantic import BaseModel

from commons.prompt.checkbox_prompt import preprocess_llm_v1
from commons.thread.multicoroutine import MultiCoroutine
from conf import CheckboxConf
from modules.entity.checkbox_entity import CheckBoxCorrect, replace_frame, FRAME_BIG

from modules.pipeline.context import Pipeline<PERSON>ontext

def preprocess_hard_selected_marks(text):
    selected = ["☒", "■", "図"]

    for s in selected:
        text = text.replace(s, "☑")

    return text

class Replacement(BaseModel):
    wrong: str
    refined: str
    num: int
    page: Optional[int] = None  # Make `page` optional


async def process_text_chunks(context: PipelineContext, text_chunks, max_tasks=10):
    context.business_log.info(f"Processing {len(text_chunks)} text chunks for checkbox correction.")
    replacements_by_page = {}
    pool = MultiCoroutine()
    for batch_index, chunk in enumerate(text_chunks):
        pool.add_task(f"text_chunk_task_{batch_index}", correct_checkbox_text_v1(chunk))

    pool_results = await pool.run_limit(max_tasks)

    for key, result in pool_results.items():
        if isinstance(result, Exception):
            context.business_log.error(f"Task {key} failed with error: {result}")
        else:
            for item in result:
                page = item.page
                if page not in replacements_by_page:
                    replacements_by_page[page] = []
                replacements_by_page[page].append(item)
    print(replacements_by_page)
    return replacements_by_page


async def correct_checkbox_marks(context: PipelineContext, text_chunks: [CheckBoxCorrect]) -> Dict[
    int, list[Replacement]]:
    replacements = await process_text_chunks(context, text_chunks)
    return replacements


async def correct_checkbox_text_v1(checkbox: CheckBoxCorrect) -> List[Replacement]:
    _t0 = time.perf_counter()
    output = await start_to_repair(checkbox.content, checkbox.images[0].url)
    for term in output:
        term.page = checkbox.page
    print(f"Checkbox text correction completed with {len(output)} replacements page is {checkbox.page}. cost {time.perf_counter() - _t0:.2f} seconds")
    return output


def parse_html(html_string):
    # 格式化输入数据。
    soup = BeautifulSoup(html_string, 'html.parser')
    formatted_html = soup.prettify()
    return formatted_html


def preprocess(text):
    # 数据预处理，将所有的"□"转为"☐"
    text = replace_frame(text)

    # 进行html结构化处理，使用beautifulsoup进行格式化
    text = parse_html(text)

    # 这里拿到了每一行的数据， 去掉了空白行。
    # 这里后续是否要将空白行加入，可以由dst自行设计代码
    split_text = [l for i, l in enumerate(text.split("\n")) if l.strip() != ""]

    return split_text


def get_wrong_text_list():
    # return ["冈", "凶", "区", "图", "因", "囧", "口", "D", "O"]
    return CheckboxConf.checkbox_filter_key


async def get_response(text, image_path):
    output = await preprocess_llm_v1(text, image_path)
    return output


def repair_wrong_text_v1(split_text_w_line, wrong_blocks, replace_text) -> (list[Replacement], list[str]):
    wrong_text_list = get_wrong_text_list()
    result = []
    for wrong_block in wrong_blocks:
        line, wrong, refined = wrong_block['line'], wrong_block['wrong'], wrong_block['refined']
        wrong_mark, wrong_text = wrong[0], wrong[1:].strip()
        match_index = 0
        for index, wrong_context_line in enumerate(split_text_w_line):
            if wrong_mark in wrong_text_list:
                # 找到错误的标识, 在判断文字是否找到
                # print(f"找到错误的标识: {w}")
                # 先判断全文是否能直接找到，能找到就直接替换
                if wrong in wrong_context_line:
                    match_index = match_index + 1
                    if index == line:
                        result.append(Replacement(
                            wrong=wrong,
                            refined=refined,
                            num=match_index,
                        ))
                        replace_text[line] = wrong_context_line.replace(wrong, refined)
                        break
                elif wrong_text in wrong_context_line:
                    # 先定位到index，然后往前匹配
                    wrong_start_index = wrong_context_line.index(wrong_text)
                    for i in range(wrong_start_index, -1, -1):
                        if wrong_context_line[i] in wrong_text_list:
                            true_wrong_text = wrong_context_line[i: wrong_start_index + len(wrong_text)]
                            match_index = match_index + 1
                            if index == line:
                                result.append(Replacement(
                                    wrong=true_wrong_text,
                                    refined=refined,
                                    num=match_index,
                                ))
                                replace_text[line] = wrong_context_line.replace(true_wrong_text, refined)

                            break

                else:
                    print("【未匹配】没有找到合适的内容进行替换")

    return result, replace_text


def repair_wrong_left_v1(split_text_w_line, wrong_blocks, replace_text):
    # 第二种错误，替换逻辑其实和第一种错误一样。
    wrong_text_list = get_wrong_text_list()
    result = []
    for wrong_block in wrong_blocks:
        line, wrong, refined = wrong_block['line'], wrong_block['wrong'], wrong_block['refined']
        if wrong.startswith(FRAME_BIG):
            wrong_index = 0
            # 表示这个是漏勾的错误，那么就开始替换
            line, wrong, refined = wrong_block['line'], wrong_block['wrong'], wrong_block['refined']
            wrong_mark, wrong_text = wrong[0], wrong[1:].strip()
            for index, wrong_context_line in enumerate(split_text_w_line):
                if wrong in wrong_context_line:
                    wrong_index = wrong_index + 1
                    if index == line:
                        result.append(Replacement(
                            wrong=wrong,
                            refined=refined,
                            num=wrong_index,
                        ))
                        replace_text[line] = wrong_context_line.replace(wrong, refined)

                        break
                    # split_text_w_line[line] = wrong_context_line.replace(wrong, refined)
                elif wrong_text in wrong_context_line:
                    # 先定位到index，然后往前匹配
                    wrong_start_index = wrong_context_line.index(wrong_text)
                    for i in range(wrong_start_index, -1, -1):
                        if wrong_context_line[i] in wrong_text_list:
                            wrong_index = wrong_index + 1
                            if index == line:
                                result.append(Replacement(
                                    wrong=wrong,
                                    refined=refined,
                                    num=wrong_index,
                                ))
                                replace_text[line] = wrong_context_line.replace(wrong, refined)

                                break
    return result, replace_text


def replace_with_llm_response(split_text_w_line, replace_text, wrong_blocks) -> (list[Replacement], list[str]):
    if len(wrong_blocks) == 0:
        return [], replace_text
    # 目前模型纠错是两种，第1种是勾选变成错字的问题，第2种是勾选漏勾问题
    result, replace_text = repair_wrong_text_v1(split_text_w_line, wrong_blocks, replace_text)
    logging.debug(f"repair_wrong_text_v1 result: {result}")
    checkboxs, replace_text = repair_wrong_left_v1(split_text_w_line, wrong_blocks, replace_text)
    logging.debug(f"repair_wrong_left_v1 result:{checkboxs}")
    result.extend(checkboxs)

    return result, replace_text


async def repair(split_text, image_path) -> (list[Replacement]):
    try:
        max_retry_time = 2
        threshold = 5
        final_blocks = []
        replace_text = copy.deepcopy(split_text)
        # 这里如果wrong_blocks的个数比较多的时候，那么注意力机制会下降，有可能会有遗漏的情况出现，那么这个时候第二次尝试是有必要的

        for _ in range(max_retry_time):
            # 合并为输入数据
            split_text_w_line = [f"line {i}: {l.strip()}" for i, l in enumerate(replace_text)]
            input_text = "\n".join(split_text_w_line)
            wrong_blocks = await get_response(input_text, image_path)
            result, replace_text = replace_with_llm_response(split_text, replace_text, wrong_blocks)
            if len(wrong_blocks) < threshold:
                # 代表只执行了一次
                if len(final_blocks) == 0:
                    return result
                else:
                    # 将最终全部的wrong block进行一次计算
                    replace_text = copy.deepcopy(split_text)
                    final_blocks.extend(wrong_blocks)
                    result, replace_text = replace_with_llm_response(split_text, replace_text, final_blocks)
                    return result
            else:
                print(f"retrying, wrong_blocks: {len(wrong_blocks)}")
                final_blocks.extend(wrong_blocks)
        # 如果超过了最大重试次数
        replace_text = copy.deepcopy(split_text)
        result, replace_text = replace_with_llm_response(split_text, replace_text, final_blocks)
        return result

    except:
        print("Error: Repair failed")
        return []


async def start_to_repair(page_content, image_url) -> (list[Replacement]):
    split_text = preprocess(page_content)

    # start_time = time.time()
    # for _ in range(max_retry_time):
    #  由于注意力会有下降，需要将多次查找的wrongblocks进行统一替换
    replace_blocks = await repair(split_text, image_url)

    return replace_blocks
