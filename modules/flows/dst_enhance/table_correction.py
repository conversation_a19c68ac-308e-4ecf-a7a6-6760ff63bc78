import copy
import re
from typing import List

import fitz

from commons.llm_gateway.models.public_model_gateway import default_max_time_out
from commons.prompt.ocrflux_prompt import preprocess_ocrflux,prompt
from commons.thread.multicoroutine import MultiCoroutine
from conf import MultipleParse
from modules.common import build_dst_id
from modules.entity.dst_entity import DST, DSTType
from modules.entity.parse_entity import Image
from modules.flows.crop.crop import load_pdf_bytes, get_table_crop_v1
from modules.layout.typesetting import typesetting_top_bottom
from modules.pipeline.context import PipelineContext

from modules.rpc.ocr_model import OCRModelClient

table_pattern = r"(?s)(<table.*?>.*?</table>)"


async def table_correct(context: PipelineContext, dsts: List[DST], max_tasks=10) -> (List[DST], List[Image]):
    """
    对提供的上下文和 DSTs（文档结构树）进行表格校正。

    该函数处理 PDF 文件以识别和校正表格结构。它提取表格图像，对其执行 OCR（光学字符识别），
    并使用校正后的表格内容或文本更新 DSTs。函数还处理特定布局的调整，并管理异步任务以进行处理。

    参数:
        context (PipelineContext): 管道上下文，包含文件信息、布局和日志工具。
        dsts (List[DST]): 表示需要校正的文档结构的 DST 对象列表。
        max_tasks (int, 可选): 并发运行的最大异步任务数。默认为 10。

    返回值:
        Tuple[List[DST], List[Image]]:
            - 更新后的 DST 对象列表，包含校正后的表格或文本内容。
            - 表示提取的表格图像的 Image 对象列表。
    """
    # Placeholder for table correction logic
    # In a real implementation, this would involve correcting the table structure
    # based on the context provided.
    need_extend_width = context.layout == typesetting_top_bottom

    if context.file_info.file_type != "pdf":
        return dsts, []
    # 如果无线增强功能未启用，跳过表格校正
    if not MultipleParse.wireless_enhance:
        context.business_log.info("Wireless enhancement is disabled, skipping table correction.")
        return dsts, []
    file_bytes = load_pdf_bytes(context.kdc_input.file_url_or_bytes)
    pdf_document = fitz.open(stream=file_bytes, filetype="pdf")
    # 获取表格裁剪结果
    table_pic_res = get_table_crop_v1(pdf_document, dsts,
                                      context.file_info, need_extend_width)
    if len(table_pic_res) == 0:
        context.business_log.info("No table images found for correction.")
        return dsts, []

    pool = MultiCoroutine()
    for index, table_pic in enumerate(table_pic_res):
        pool.add_task(f"text_chunk_task_{index}", table_correct_sync(context, table_pic))

    pool_results = await pool.run_limit(max_tasks)
    replacements = {}
    for key, result in pool_results.items():
        if not result or isinstance(result, Exception):
            context.business_log.error(f"Task {key} failed with error: {result}")
        else:
            replacements.update(result)
    context.business_log.info("Table correction completed.")
    # 处理校正结果
    result_bboxes = {}
    for dst in dsts:
        if dst.id in replacements:
            bbox = dst.attributes.position.bbox
            if dst.attributes.page not in result_bboxes:
                result_bboxes[dst.attributes.page] = []
            result_bboxes[dst.attributes.page].append(bbox)

    result = []
    replace_parent = {}
    for dst in dsts:
        if dst.dst_type == DSTType.TEXT:
            # Check if the bbox overlaps with any bbox in result_bboxes
            bbox = dst.attributes.position.bbox
            if dst.attributes.page in result_bboxes and any(
                    bbox.y1 >= res_bbox.y1 and bbox.y2 <= res_bbox.y2 for res_bbox in
                    result_bboxes[dst.attributes.page]):
                replace_parent[dst.id] = dst.parent
                continue  # Skip adding to result if the condition is met

        if dst.id in replacements:
            table_and_text = replacements[dst.id]
            if table_and_text.get("table"):
                if need_extend_width:
                    dst.attributes.position.bbox.x1 = 0
                for term in table_and_text.get("raw"):
                    if re.match(table_pattern, term):
                        dst.content = table_and_text["table"]
                        result.append(dst)
                    else:
                        # If the term is not a table, treat it as text
                        if term.strip() != "":
                            text_dst = copy.deepcopy(dst)
                            text_dst.content = term.strip()
                            text_dst.id = build_dst_id()
                            text_dst.dst_type = DSTType.TEXT
                            result.append(text_dst)
            else:
                result.append(dst)
                ##处理位置信息
            context.business_log.debug(f"Updated DST {dst.id} with corrected content.")
        else:
            result.append(dst)
    for dst in result:
        if dst.parent in replace_parent:
            dst.parent = replace_parent[dst.id]
            context.business_log.debug(f"Updated parent for DST {dst.id} to {dst.parent}")
    return result, table_pic_res


def extract_table_and_text(input_str: str) -> dict:
    # Regular expression to match <table>...</table> and split text around it

    # Split the input string based on the table pattern
    parts = re.split(table_pattern, input_str)

    # Separate tables and text
    tables = [part for part in parts if re.match(table_pattern, part) and part.strip()]
    text = [part for part in parts if not re.match(table_pattern, part) and part.strip()]

    return {"table": tables, "text": text, "raw": parts}


async def table_correct_sync(context: PipelineContext, table_corp_image: Image) -> dict:
    """
    Synchronous wrapper for the table correction function.
    This is useful for environments that do not support async operations.
    """
    try:
        table_cls = await OCRModelClient().request_table_cls(table_corp_image.url)
        context.business_log.debug(f"table cls: {table_cls}")
        if table_cls == "wireless":
            output_data,_ = await preprocess_ocrflux(table_corp_image.url, prompt,default_max_time_out)
            context.business_log.debug(f"table ocr: {output_data}")

            ## 处理ocrflux的输出。获取里面的表格
            table_and_text = extract_table_and_text(output_data)
            if not context.file_info.is_scan and table_and_text.get("text") is not None:
                context.business_log.error(f"Table OCR failed for {table_corp_image.url}. No table found.")
                return {}
            if table_and_text.get("table") is None or len(table_and_text.get("table")) == 0:
                context.business_log.error(f"Table OCR failed for {table_corp_image.url}. No table found.")
                return {}
            return {
                table_corp_image.dst_ids[0]: table_and_text
            }
    except Exception as e:
        # 可能会存在一些解析json的问题，直接返回文本。
        context.business_log.error(f"Error processing ocr response, returning raw text {e}.")
        return {}
