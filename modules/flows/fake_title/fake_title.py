# coding: utf-8
# <AUTHOR> linqi
# @date    : 2025/5/7 16:24
from commons.trace.tracer import async_trace_span
from modules.pipeline.base import PipelineHandler
from modules.pipeline.context import PipelineContext
from modules.entity.dst_entity import print_dst_indent_tree
from modules.rpc.ocr_model import OCRModelClient
from services.fake_title import fake_title_services
from modules.entity.parse_entity import ParseRes
from modules.flows.callback import callback_parse_background

class FakeTitleNode(PipelineHandler):
    def __init__(self, name: str):
        super().__init__(name)
        self.name = name

    @async_trace_span
    async def process(self, context: PipelineContext) -> PipelineContext:
        try:
            # 从 context 中取出 dst_list
            dst_list = context.dst

            # 调用 print_dst_tree 函数
            plain_content = print_dst_indent_tree(dst_list)

            fake_title = await fake_title_services(plain_content)
            fake_title_embedding = None
            if context.embed_enabled:
                fake_title_embedding = await OCRModelClient().request_text_embedding(fake_title)
            context.handler_results[self.name] = {
                "fake_title": fake_title,
                "fake_title_embedding": fake_title_embedding
            }
            res = ParseRes(fake_title=fake_title, fake_title_embedding=fake_title_embedding, page_size=context.file_info.page_size,
                           word_count=context.file_info.word_count,width=context.file_info.width, height=context.file_info.height,
                           is_scan=context.file_info.is_scan, image=context.image, rotate_page=context.file_info.rotate_page,parse_version=context.parse_version)
            await callback_parse_background(res, self.name, context.token, context.need_callback,
                                             context.return_ks3_url, context.callback_url)
            return context

        except Exception as e:
            import logging
            logging.error(f"Error in FakeTitleNode.process: {e}")