from typing import List, Dict

from modules.entity.dst_entity import DST, DSTType
typesetting_left_right="left-right"
typesetting_top_bottom="top-bottom"

def determine_layout_with_middle_line(page_dst_map: Dict[int, List[DST]]):
    """
    Determine whether the PDF content is left-right or top-bottom layout based on bbox density, distribution, and middle line.

    :param page_dst_map: List of DST objects containing bbox information.
    :return: "left-right" if the layout is horizontal, "top-bottom" if the layout is vertical.
    """
    horizontal_density = 0
    vertical_density = 0

    for page, dsts in page_dst_map.items():
        # 过滤出有效的bbox
        valid_dsts = [dst for dst in dsts if dst.attributes and
                      dst.attributes.position and
                      dst.attributes.position.bbox]

        if not valid_dsts:
            continue

        # Calculate max_x2 and min_x1 for the page
        max_x2 = max(dst.attributes.position.bbox.x2 for dst in valid_dsts)
        min_x1 = min(dst.attributes.position.bbox.x1 for dst in valid_dsts)


        for dst in valid_dsts:
            # if dst.dst_type == DSTType.TABLE:
            #     continue
            width = dst.attributes.position.bbox.x2 - dst.attributes.position.bbox.x1
            height = dst.attributes.position.bbox.y2 - dst.attributes.position.bbox.y1
            area = width * height

            # Calculate overall horizontal and vertical density
            if  width > 0.5 * (max_x2 - min_x1):
                horizontal_density += area
            else:
                vertical_density += area

    # Determine layout based on density
    if horizontal_density < vertical_density:
        return typesetting_left_right
    else:
        return typesetting_top_bottom


def reorder_dst_by_page(root_dst:DST,page_dst_map: Dict[int, List[DST]]):
    """
    Reorder DST objects on each page considering mixed layouts (left-right and top-bottom).

    :param page_dst_map: Dict of DST objects.
    :return: Reordered list of DST objects.
    """

    reordered_dst_list = [root_dst]

    # Process each page
    for page, dsts in page_dst_map.items():
        # Calculate max_x2 and min_x1 for the page
        max_x2 = max(dst.attributes.position.bbox.x2 for dst in dsts)
        min_x1 = min(dst.attributes.position.bbox.x1 for dst in dsts)
        threshold = (max_x2 - min_x1) / 2 + min_x1

        left_side = []
        right_side = []
        # Split into left, right, and top-bottom groups
        for dst in dsts:
            bbox = dst.attributes.position.bbox
            # if dst.dst_type in {"table", "image"}:
            #     top_bottom.append(dst)
            if bbox.x1 < threshold and bbox.x2 > threshold:
                # Cross-domain content goes to left_side
                left_side.append(dst)
            elif bbox.x2 <= threshold:
                left_side.append(dst)
            else:
                right_side.append(dst)

        reordered_dst_list.extend(left_side + right_side)

    return reordered_dst_list


def typesetting_correct(root_dst: DST,page_dst_map: Dict[int, List[DST]]):
    """
    Correct the typesetting of DST objects based on their layout.

    :param page_dst_map: Dict of DST objects.
    :return: Corrected list of DST objects.
    """
    layout = determine_layout_with_middle_line(page_dst_map)
    print(f"Detected layout: {layout}")
    if layout == typesetting_left_right:
        return reorder_dst_by_page(root_dst,page_dst_map),layout
    return None,layout
