# coding: utf-8
# <AUTHOR> wangziyang1
# @date    : 2025/4/2 14:55


from commons.db.mysqldao import MysqlDao
import unittest
from modules.parse_record.parse_record_dao import ParseRecordDao
from conf import ConfDB


class ParseRecordTest(unittest.TestCase):
    def setUp(self):
        ConfDB.load()
        MysqlDao().init(
            host=ConfDB.host,
            port=ConfDB.port,
            user=ConfDB.user,
            pwd=ConfDB.pwd,
            db=ConfDB.db_name,
            pools=ConfDB.pools,
            env=ConfDB.env,
            is_debug=True
        )
        self.db = MysqlDao().sess()

    def tearDown(self):
        MysqlDao().close()

    def test_add(self):
        res = ParseRecordDao.add_record(self.db, "token5", "{\"a\":\"3\"}")
        print(res)

    def test_get(self):
        res = ParseRecordDao.get_record(self.db, "token3")
        print(res.token)
        print(res.record_json)

    def test_update(self):
        res = ParseRecordDao.get_record(self.db, "token3")
        print(res.heartbeat)

        ParseRecordDao().record_heartbeat(self.db, "token3")

        res = ParseRecordDao.get_record(self.db, "token3")
        print(res.heartbeat)

    def test_delete(self):
        ParseRecordDao.del_record(self.db, "token3")

    def test_scan(self):
        res = ParseRecordDao.scan_records(self.db, 0, 20)
        print(res)
        for r in res:
            print(r.key_id)
            print(r.token)
