from unittest import TestCase

from modules.entity.dst_entity import DST, breadth_first_sort, DSTAttribute, BBox, print_dst_indent_tree, \
    print_dst_tree, sort_dst_list


class Test(TestCase):

    def setUp(self):
        self.dst_list = [
            DST(id="1", parent="-1", order=0, dst_type="root", attributes=DSTAttribute(
            level=0,
            position=BBox(x1=1,
                          y1=2,
                          x2=3,
                          y2=4),
            page=0,
            hash="roothashhashhashhashhashhashhashhashhashhash",

        ), content=["Root"]),
            DST(id="5", parent="2", order=1, dst_type="text", attributes=DSTAttribute(
            level=0,
            position=BBox(x1=1,
                          y1=2,
                          x2=3,
                          y2=4),
            page=0,
            hash="roothashhashhashhashhashhashhashhashhashhash",

        ), content=["Grandchild 2"]),
            DST(id="2", parent="1", order=0, dst_type="text", attributes=DSTAttribute(
            level=0,
            position=BBox(x1=1,
                          y1=2,
                          x2=3,
                          y2=4),
            page=0,
            hash="roothashhashhashhashhashhashhashhashhashhash",

        ), content=["Child 1"]),
            DST(id="3", parent="1", order=1, dst_type="text", attributes=DSTAttribute(
            level=0,
            position=BBox(x1=1,
                          y1=2,
                          x2=3,
                          y2=4),
            page=0,
            hash="roothashhashhashhashhashhashhashhashhashhash",

        ), content=["Child 2"]),
            DST(id="4", parent="2", order=0, dst_type="text", attributes=DSTAttribute(
            level=0,
            position=BBox(x1=1,
                          y1=2,
                          x2=3,
                          y2=4),
            page=0,
            hash="roothashhashhashhashhashhashhashhashhashhash",

        ), content=["Grandchild 1"]),
        ]

    def test_breadth_first_sort(self):
        result = sort_dst_list(self.dst_list)
        expected_order = ['1', '2', '4', '5', '3']  # Expected order of IDs in breadth-first traversal
        print(print_dst_tree(result))
        self.assertEqual([node.id for node in result], expected_order)
