# coding: utf-8
# <AUTHOR> linqi
# @date    : 2025/4/25 11:18
import logging

from fastapi.routing import APIRouter
from fastapi import Request
from typing import Callable, Optional
from pydantic import BaseModel

from commons.tools.utils import error_trace
from commons.auth.auth_route import AuthRoute
from routers.httpcode import HTTPCODE
from services.datamodel import RespBaseModel, ReqGeneralParse, ReqGeneralParseRes, RespGeneralParseRes, ReqMergeTable, \
    RespMergeTableRes, RespVersionRes, ReqMergeTableV2
from services.parse import general_parse_res_service
from services.parse import general_parse_service
from services.table_merge import merge_table, merge_table_v2
from services.version import get_version

router = APIRouter(route_class=AuthRoute)


async def safe_service(service: Callable, *args, **kwargs):
    try:
        return await service(*args, **kwargs)
    except:
        error_trace()
        return RespBaseModel(code=HTTPCODE.ERROR, message="Service Error")


from services.parse import BackgroundTasks


@router.post("/api/v1/aidocs_dst/parse_pipeline", response_model=RespGeneralParseRes)
async def general_parse(r: Request, background_tasks: BackgroundTasks):
    return await safe_service(general_parse_service, r, background_tasks)


@router.post("/api/v1/aidocs_dst/merge_table", response_model=RespMergeTableRes)
async def general_parse(r: ReqMergeTable):
    return await safe_service(merge_table, r)

@router.post("/api/v1/aidocs_dst/merge_table_v2", response_model=RespMergeTableRes)
async def general_parse(r: ReqMergeTableV2):
    return await safe_service(merge_table_v2, r)


@router.get("/api/v1/aidocs_dst/parse_version", response_model=RespVersionRes)
async def general_parse():
    return await safe_service(get_version)


@router.post("/api/v1/aidocs_dst/general_parse_res", response_model=RespGeneralParseRes)
async def general_parse_res(r: ReqGeneralParseRes):
    return await safe_service(general_parse_res_service, r)


from services.kafka_service import get_kafka_lag, get_kafka_throughput, KafkaMsg, RespKafkaMsg

@router.get("/api/v1/aidocs_dst/kafka_lag", response_model=RespKafkaMsg)
async def kafka_lag():
    return await safe_service(get_kafka_lag)

@router.get("/api/v1/aidocs_dst/kafka_throughput", response_model=RespKafkaMsg)
async def kafka_throughput():
    return await safe_service(get_kafka_throughput)
