"""
modules.cross_table.cross_page_merge 模块的单元测试
"""
import pytest
from unittest.mock import patch, MagicMock

from modules.cross_table.cross_page_merge import (
    classify_and_mark_table_indices, process_and_merge_chunks,
    merge_chunks_with_base, merge_chunks_with_base_v2, cross_table_merge
)
from modules.entity.chunk_entity import Chunk, LabelType
from modules.entity.dst_entity import DSTType, DST, DSTAttribute, BBox, PositionInfo


class TestClassifyAndMarkTableIndices:
    """classify_and_mark_table_indices 函数的测试用例"""

    def create_dst_for_chunk(self, chunk_id, content, page_num, label_type=LabelType.TABLE):
        """创建用于测试的DST对象"""
        actual_page = page_num[0] if isinstance(page_num, list) else page_num
        if actual_page < 0:
            actual_page = 0
        
        return DST(
            id=f"dst_{chunk_id}",
            parent="root",
            order=0,
            dst_type=DSTType.TABLE if label_type == LabelType.TABLE else DSTType.TEXT,
            attributes=DSTAttribute(
                level=0,
                position=PositionInfo(),
                page=actual_page,
                hash="a" * 32
            ),
            content=[content]
        )

    def create_test_chunk(self, chunk_id, content, page_num, label_type=LabelType.TABLE):
        """创建具有必需字段的测试块的辅助方法"""
        # 处理负数页码，将其转换为有效的页码
        actual_page = page_num[0] if isinstance(page_num, list) else page_num
        if actual_page < 0:
            actual_page = 0  # 将负数页码转换为0
        
        # 创建一个测试用的DST对象
        test_dst = DST(
            id=f"dst_{chunk_id}",
            parent="root",
            order=0,
            dst_type=DSTType.TABLE if label_type == LabelType.TABLE else DSTType.TEXT,
            attributes=DSTAttribute(
                level=0,
                position=PositionInfo(),
                page=actual_page,
                hash="a" * 32  # 生成32个字符的测试hash
            ),
            content=[content]
        )
        
        return Chunk(
            chunk_id=chunk_id,
            page_size=100,
            content=content,
            label=label_type,
            page_num=page_num,
            block=[f"block_{chunk_id}"],
            dsts=[test_dst]
        )

    def test_classify_empty_chunks(self):
        """测试 classify_and_mark_table_indices 处理空块列表的情况"""
        result = classify_and_mark_table_indices([])
        
        assert result == {}

    def test_classify_single_chunk_single_page(self):
        """测试 classify_and_mark_table_indices 处理单页面单块的情况"""
        chunk = self.create_test_chunk("chunk_1", "<table><tr><td>Test</td></tr></table>", [1])

        result = classify_and_mark_table_indices([chunk])

        assert 1 in result
        assert isinstance(result[1], dict)
        assert "first_table_index" in result[1]
        assert "first_chunk_id" in result[1]
        assert "from_last_table_index" in result[1]
        assert "last_chunk_id" in result[1]

    def test_classify_multiple_chunks_same_page(self):
        """测试 classify_and_mark_table_indices 处理同一页面多个块的情况"""
        chunk1 = self.create_test_chunk("chunk_1", "<table><tr><td>Table 1</td></tr></table>", [1])
        chunk2 = self.create_test_chunk("chunk_2", "<table><tr><td>Table 2</td></tr></table>", [1])

        result = classify_and_mark_table_indices([chunk1, chunk2])

        assert 1 in result
        assert isinstance(result[1], dict)
        assert "first_table_index" in result[1]
        assert "first_chunk_id" in result[1]
        assert "from_last_table_index" in result[1]
        assert "last_chunk_id" in result[1]

    def test_classify_chunks_different_pages(self):
        """测试 classify_and_mark_table_indices 处理不同页面块的情况"""
        chunk1 = self.create_test_chunk("chunk_1", "<table><tr><td>Page 1 Table</td></tr></table>", [1])
        chunk2 = self.create_test_chunk("chunk_2", "<table><tr><td>Page 2 Table</td></tr></table>", [2])

        result = classify_and_mark_table_indices([chunk1, chunk2])

        assert 1 in result
        assert 2 in result
        assert isinstance(result[1], dict)
        assert isinstance(result[2], dict)
        assert "first_table_index" in result[1]
        assert "first_table_index" in result[2]

    def test_classify_chunks_multiple_pages_per_chunk(self):
        """测试 classify_and_mark_table_indices 处理跨多页面块的情况"""
        chunk = self.create_test_chunk("chunk_1", "<table><tr><td>Multi-page Table</td></tr></table>", [1, 2, 3])

        result = classify_and_mark_table_indices([chunk])

        # 应该按第一个页码进行分类
        assert 1 in result
        assert isinstance(result[1], dict)
        assert "first_table_index" in result[1]

    def test_classify_mixed_label_types(self):
        """测试 classify_and_mark_table_indices 处理混合标签类型的情况"""
        table_chunk = self.create_test_chunk("table_chunk", "<table><tr><td>Table</td></tr></table>", [1], LabelType.TABLE)
        text_chunk = self.create_test_chunk("text_chunk", "Regular text content", [1], LabelType.TEXT)

        result = classify_and_mark_table_indices([table_chunk, text_chunk])

        assert 1 in result
        assert isinstance(result[1], dict)
        assert "first_table_index" in result[1]

    def test_classify_chunks_zero_page(self):
        """测试 classify_and_mark_table_indices 处理零页码的情况"""
        chunk = self.create_test_chunk("chunk_0", "<table><tr><td>Zero Page</td></tr></table>", [0])

        result = classify_and_mark_table_indices([chunk])

        assert 0 in result
        assert isinstance(result[0], dict)
        assert "first_table_index" in result[0]

    def test_classify_chunks_negative_page(self):
        """测试 classify_and_mark_table_indices 处理负页码的情况"""
        chunk = self.create_test_chunk("chunk_neg", "<table><tr><td>Negative Page</td></tr></table>", [-1])

        result = classify_and_mark_table_indices([chunk])

        assert -1 in result
        assert isinstance(result[-1], dict)
        assert "first_table_index" in result[-1]

    def test_classify_chunks_with_dst_data(self):
        """测试 classify_and_mark_table_indices 处理包含 DST 数据的块的情况"""
        # 创建包含所有必需字段的 DST 对象
        bbox = BBox(x1=10, y1=20, x2=100, y2=200)
        pos = PositionInfo(bbox=bbox)
        attr = DSTAttribute(level=0, position=pos, page=0, hash="a" * 32)

        dst1 = DST(
            id="dst1", parent="-1", order=0, dst_type=DSTType.TABLE,
            attributes=attr, content=["<table><tr><td>Table 1</td></tr></table>"], mark=None
        )
        dst2 = DST(
            id="dst2", parent="-1", order=1, dst_type=DSTType.TEXT,
            attributes=attr, content=["Some text"], mark="header"  # 已标记的 DST
        )
        dst3 = DST(
            id="dst3", parent="-1", order=2, dst_type=DSTType.TABLE,
            attributes=attr, content=["<table><tr><td>Table 2</td></tr></table>"], mark=None
        )

        chunk = self.create_test_chunk("chunk_1", "<table><tr><td>Test</td></tr></table>", [1])
        chunk.dsts = [dst1, dst2, dst3]

        result = classify_and_mark_table_indices([chunk])

        assert 1 in result
        assert isinstance(result[1], dict)
        assert result[1]["first_table_index"] == 0  # 第一个表格 DST
        assert result[1]["last_chunk_id"] == "chunk_1"
        assert result[1]["from_last_table_index"] == 1  # 应该考虑已标记的 DST

    def test_classify_and_mark_table_indices_real_log_data(self):
        """测试 classify_and_mark_table_indices 基于真实日志数据的情况 - 完整14个chunks"""
        # 基于日志数据构造完整的14个chunk对象
        chunks = [
            # 1. TEXT chunk on page 0
            self.create_test_chunk("04367f973d7743788083e3a49a1cfa12", 
                '严重不良事件(SAE)报告表 \\n\\n  新药临床批准文号： 报告时问：2024.05.24 报告类型：  □首次 ☑随访 ☑总结', 
                [0], LabelType.TEXT),
            
            # 2. TABLE chunk on page 0 
            self.create_test_chunk("cfe5fc6321be4ecf83d5d9dbcb50e009", 
                '<table><tr><td rowspan="1" colspan="15">临床项目及报告单位信息</td></tr></table>', 
                [0], LabelType.TABLE),
            
            # 3. TABLE chunk on page 1
            self.create_test_chunk("b91ea66fcee04f7bb3a5a58605b6e405", 
                '<table><tr><td rowspan="1" colspan="1">相关Ag21-1升 高</td></tr></table>', 
                [1], LabelType.TABLE),
            
            # 4. TEXT chunk on page 1
            self.create_test_chunk("b2c7948bcb9b48bd971c40540dd1f769", 
                '1-', 
                [1], LabelType.TEXT),
            
            # 5. TABLE chunk on page 2
            self.create_test_chunk("8d6c8fbabf12413da30e2988d3736b72", 
                '<table><tr><td rowspan="1" colspan="1">游离甲状膜素</td></tr></table>', 
                [2], LabelType.TABLE),
            
            # 6. IMAGE chunk on page 2
            self.create_test_chunk("215ff33295e24a0ca79dbb6138dc9bed", 
                '', 
                [2], LabelType.IMAGE),
            
            # 7. TABLE chunk on page 3
            self.create_test_chunk("47ee491c3dd64e00ae2e1cd1c44044be", 
                '<table><tr><td rowspan="10" colspan="1"></td></tr></table>', 
                [3], LabelType.TABLE),
            
            # 8. TABLE chunk on page 3
            self.create_test_chunk("c82270e5edeb4412914012215fe0aa3d", 
                '<table><tr><td rowspan="1" colspan="5">既往用药信息</td></tr></table>', 
                [3], LabelType.TABLE),
            
            # 9. TABLE chunk on page 3
            self.create_test_chunk("5ff2cbe57eda4fa2b8ae5421aa0c472b", 
                '<table><tr><td rowspan="1" colspan="5">合并用药信息</td></tr></table>', 
                [3], LabelType.TABLE),
            
            # 10. TABLE chunk on page 4
            self.create_test_chunk("c98914c3506c4322a13386cd98cd35a2", 
                '<table><tr><td rowspan="1" colspan="5">严重不良事件信息</td></tr></table>', 
                [4], LabelType.TABLE),
            
            # 11. TABLE chunk on page 4
            self.create_test_chunk("c3b9b02d9927468cb729ad4e2ccf3317", 
                '<table><tr><td rowspan="1" colspan="1">SAE发生及处理的详细情况</td></tr></table>', 
                [4], LabelType.TABLE),
            
            # 12. IMAGE chunk on page 5
            self.create_test_chunk("435563ba0f924c8b9bf174a583b7af03", 
                '', 
                [5], LabelType.IMAGE),
            
            # 13. TABLE chunk on page 5
            self.create_test_chunk("3f23cdfd0d4b4601b4490d2cdee57b77", 
                '<table><tr><td rowspan="1" colspan="1">[对可疑产品采取的措施] 不适用</td></tr></table>', 
                [5], LabelType.TABLE),
            
            # 14. TEXT chunk on page 5
            self.create_test_chunk("1aabdf0f7cfc4acf97fe3e44621489a3", 
                ' 20ip.5.2报告者签名： 日期：', 
                [5], LabelType.TEXT),
        ]

        # 调用真实函数
        result = classify_and_mark_table_indices(chunks)

        # 验证结果结构
        assert isinstance(result, dict)
        print(f"classify_and_mark_table_indices result: {result}")
        
        # 验证各页面的结果
        assert 0 in result  # 页面0应该存在
        assert 1 in result  # 页面1应该存在  
        assert 2 in result  # 页面2应该存在
        assert 3 in result  # 页面3应该存在
        assert 4 in result  # 页面4应该存在
        assert 5 in result  # 页面5应该存在
        
        # 验证页面0的结果（包含TEXT + TABLE）
        page_0_result = result[0]
        assert "first_table_index" in page_0_result
        assert "first_chunk_id" in page_0_result
        assert "from_last_table_index" in page_0_result
        assert "last_chunk_id" in page_0_result
        assert page_0_result["first_chunk_id"] == "cfe5fc6321be4ecf83d5d9dbcb50e009"  # 第一个TABLE chunk
        
        # 验证页面3的结果（包含3个TABLE chunks）
        page_3_result = result[3]
        assert page_3_result["first_chunk_id"] == "47ee491c3dd64e00ae2e1cd1c44044be"  # 第一个TABLE chunk
        assert page_3_result["last_chunk_id"] == "5ff2cbe57eda4fa2b8ae5421aa0c472b"   # 最后一个TABLE chunk


class TestProcessAndMergeChunks:
    """process_and_merge_chunks 函数的测试用例"""

    def create_test_chunk(self, chunk_id, content, page_num, label_type=LabelType.TABLE):
        """创建具有必需字段的测试块的辅助方法"""
        return Chunk(
            chunk_id=chunk_id,
            page_size=100,
            content=content,
            label=label_type,
            page_num=page_num,
            block=[f"block_{chunk_id}"]
        )

    def test_process_and_merge_empty_chunks(self):
        """测试 process_and_merge_chunks 处理空块和空页面块的情况"""
        result = process_and_merge_chunks([], {})

        assert result == []

    def test_process_and_merge_empty_page_chunks(self):
        """测试 process_and_merge_chunks 处理有块但页面块为空的情况"""
        chunk = self.create_test_chunk("chunk_1", "<table><tr><td>Test</td></tr></table>", [1])

        result = process_and_merge_chunks([chunk], {})

        assert result == []

    @patch('modules.cross_table.cross_page_merge.cross_page_merge_by_html')
    def test_process_and_merge_single_page(self, mock_merge):
        """测试 process_and_merge_chunks 处理单页面的情况"""
        chunk = self.create_test_chunk("chunk_1", "<table><tr><td>Test</td></tr></table>", [1])
        table_indices = {1: {
            "first_table_index": 0,
            "first_chunk_id": "chunk_1",
            "from_last_table_index": 0,
            "last_chunk_id": "chunk_1",
        }}

        mock_merge.return_value = (True, ["<table><tr><td>Merged</td></tr></table>"])

        result = process_and_merge_chunks([chunk], table_indices)

        assert len(result) == 0  # 单页面不会有合并
        # mock_merge.assert_not_called()  # 单页面不会触发合并

    @patch('modules.cross_table.cross_page_merge.cross_page_merge_by_html')
    def test_process_and_merge_multiple_pages(self, mock_merge):
        """测试 process_and_merge_chunks 处理多页面的情况"""
        chunk1 = self.create_test_chunk("chunk_1", "<table><tr><td>Page 1</td></tr></table>", [1])
        chunk2 = self.create_test_chunk("chunk_2", "<table><tr><td>Page 2</td></tr></table>", [2])
        table_indices = {
            1: {
                "first_table_index": 0,
                "first_chunk_id": "chunk_1",
                "from_last_table_index": 0,
                "last_chunk_id": "chunk_1",
            },
            2: {
                "first_table_index": 0,
                "first_chunk_id": "chunk_2",
                "from_last_table_index": 0,
                "last_chunk_id": "chunk_2",
            }
        }

        mock_merge.return_value = (True, [
            "<table><tr><td>Merged 1</td></tr></table>",
            "<table><tr><td>Merged 2</td></tr></table>"
        ])

        result = process_and_merge_chunks([chunk1, chunk2], table_indices)

        assert len(result) == 1  # 两页面之间的一个合并结果
        assert "chunk_ids" in result[0]
        assert "content" in result[0]
        assert result[0]["chunk_ids"] == ["chunk_1", "chunk_2"]
        mock_merge.assert_called_once()

    @patch('modules.cross_table.cross_page_merge.cross_page_merge_by_html')
    def test_process_and_merge_no_merge_condition(self, mock_merge):
        """测试 process_and_merge_chunks 不满足合并条件时的情况"""
        chunk1 = self.create_test_chunk("chunk_1", "<table><tr><td>Page 1</td></tr></table>", [1])
        chunk2 = self.create_test_chunk("chunk_2", "<table><tr><td>Page 2</td></tr></table>", [2])
        table_indices = {
            1: {
                "first_table_index": 0,
                "first_chunk_id": "chunk_1",
                "from_last_table_index": 10,  # 大值以防止合并
                "last_chunk_id": "chunk_1",
            },
            2: {
                "first_table_index": 0,
                "first_chunk_id": "chunk_2",
                "from_last_table_index": 0,
                "last_chunk_id": "chunk_2",
            }
        }

        result = process_and_merge_chunks([chunk1, chunk2], table_indices)

        assert len(result) == 0  # 不应该发生合并
        mock_merge.assert_not_called()

    @patch('modules.cross_table.cross_page_merge.cross_page_merge_by_html')
    def test_process_and_merge_consecutive_merges(self, mock_merge):
        """测试 process_and_merge_chunks 处理连续合并的情况"""
        chunk1 = self.create_test_chunk("chunk_1", "<table><tr><td>Page 1</td></tr></table>", [1])
        chunk2 = self.create_test_chunk("chunk_2", "<table><tr><td>Page 2</td></tr></table>", [2])
        chunk3 = self.create_test_chunk("chunk_3", "<table><tr><td>Page 3</td></tr></table>", [3])
        table_indices = {
            1: {
                "first_table_index": 0,
                "first_chunk_id": "chunk_1",
                "from_last_table_index": 0,
                "last_chunk_id": "chunk_1",
            },
            2: {
                "first_table_index": 0,
                "first_chunk_id": "chunk_2",
                "from_last_table_index": 0,
                "last_chunk_id": "chunk_2",
            },
            3: {
                "first_table_index": 0,
                "first_chunk_id": "chunk_3",
                "from_last_table_index": 0,
                "last_chunk_id": "chunk_3",
            }
        }

        # 第一次调用返回 True（合并），第二次调用返回 True（连续合并）
        mock_merge.side_effect = [(True, ["merged_content"]), (True, ["merged_content_2"])]

        result = process_and_merge_chunks([chunk1, chunk2, chunk3], table_indices)

        assert len(result) == 1  # 一个合并结果
        assert len(result[0]["chunk_ids"]) == 3  # 所有三个块都被合并
        assert mock_merge.call_count == 2

    def test_process_and_merge_chunks_real_log_data(self):
        """测试 process_and_merge_chunks 基于真实日志数据的情况 - 完整14个chunks"""
        # 基于日志数据构造完整的14个chunk对象
        chunks = [
            # 1. TEXT chunk on page 0
            self.create_test_chunk("04367f973d7743788083e3a49a1cfa12", 
                '严重不良事件(SAE)报告表 \\n\\n  新药临床批准文号： 报告时问：2024.05.24 报告类型：  □首次 ☑随访 ☑总结', 
                [0], LabelType.TEXT),
            
            # 2. TABLE chunk on page 0 
            self.create_test_chunk("cfe5fc6321be4ecf83d5d9dbcb50e009", 
                '<table><tr><td rowspan="1" colspan="15">临床项目及报告单位信息</td></tr><tr><td rowspan="1" colspan="2">医疗机构及专业名称</td><td rowspan="1" colspan="7">上海市第六人民医院呼吸内科</td><td rowspan="1" colspan="3">电话</td><td rowspan="1" colspan="3"></td></tr></table>', 
                [0], LabelType.TABLE),
            
            # 3. TABLE chunk on page 1
            self.create_test_chunk("b91ea66fcee04f7bb3a5a58605b6e405", 
                '<table><tr><td rowspan="1" colspan="1">相关Ag21-1升 高</td><td rowspan="1" colspan="2"></td><td rowspan="1" colspan="2"></td><td rowspan="1" colspan="2"></td><td rowspan="1" colspan="3"></td></tr></table>', 
                [1], LabelType.TABLE),
            
            # 4. TEXT chunk on page 1
            self.create_test_chunk("b2c7948bcb9b48bd971c40540dd1f769", 
                '1-', 
                [1], LabelType.TEXT),
            
            # 5. TABLE chunk on page 2
            self.create_test_chunk("8d6c8fbabf12413da30e2988d3736b72", 
                '<table><tr><td rowspan="1" colspan="1">游离甲状膜素</td><td rowspan="1" colspan="1">2024.5.19</td><td rowspan="1" colspan="1">23.19</td><td rowspan="1" colspan="1">mg/ml</td><td rowspan="1" colspan="1">10</td><td rowspan="1" colspan="1">22</td><td rowspan="1" colspan="1"></td></tr></table>', 
                [2], LabelType.TABLE),
            
            # 6. IMAGE chunk on page 2
            self.create_test_chunk("215ff33295e24a0ca79dbb6138dc9bed", 
                '', 
                [2], LabelType.IMAGE),
            
            # 7. TABLE chunk on page 3
            self.create_test_chunk("47ee491c3dd64e00ae2e1cd1c44044be", 
                '<table><tr><td rowspan="10" colspan="1"></td><td rowspan="1" colspan="4">试验用药信息 □未使用试验药品 图已使用试验药品</td></tr></table>', 
                [3], LabelType.TABLE),
            
            # 8. TABLE chunk on page 3
            self.create_test_chunk("c82270e5edeb4412914012215fe0aa3d", 
                '<table><tr><td rowspan="1" colspan="5">既往用药信息(既往用药收集既往使用且在严重不良事件发生前已停用的药物)</td></tr></table>', 
                [3], LabelType.TABLE),
            
            # 9. TABLE chunk on page 3
            self.create_test_chunk("5ff2cbe57eda4fa2b8ae5421aa0c472b", 
                '<table><tr><td rowspan="1" colspan="5">合并用药信息(合并用药收集严重不良事件发生前开始使用且发生时正在使用的药品)</td></tr></table>', 
                [3], LabelType.TABLE),
            
            # 10. TABLE chunk on page 4
            self.create_test_chunk("c98914c3506c4322a13386cd98cd35a2", 
                '<table><tr><td rowspan="1" colspan="5">严重不良事件信息(如同时发生多个严重不良事件，请复制此表格添加)</td></tr></table>', 
                [4], LabelType.TABLE),
            
            # 11. TABLE chunk on page 4
            self.create_test_chunk("c3b9b02d9927468cb729ad4e2ccf3317", 
                '<table><tr><td rowspan="1" colspan="1">SAE发生及处理的详细情况(如同时发生多个严重不良事件，可统一记录在此处)</td></tr></table>', 
                [4], LabelType.TABLE),
            
            # 12. IMAGE chunk on page 5
            self.create_test_chunk("435563ba0f924c8b9bf174a583b7af03", 
                '', 
                [5], LabelType.IMAGE),
            
            # 13. TABLE chunk on page 5
            self.create_test_chunk("3f23cdfd0d4b4601b4490d2cdee57b77", 
                '<table><tr><td rowspan="1" colspan="1">[对可疑产品采取的措施] 不适用 [不良事件转归 至此报告时，患者有腹水，人清醒，仍住院，转归持续。</td></tr></table>', 
                [5], LabelType.TABLE),
            
            # 14. TEXT chunk on page 5
            self.create_test_chunk("1aabdf0f7cfc4acf97fe3e44621489a3", 
                ' 20ip.5.2报告者签名： 日期：', 
                [5], LabelType.TEXT),
        ]

        # 构造table_indices，模拟真实的分页情况
        table_indices = {
            0: {
                "first_table_index": 1,  # 第一个TABLE在index 1 (cfe5fc6321be4ecf83d5d9dbcb50e009)
                "first_chunk_id": "cfe5fc6321be4ecf83d5d9dbcb50e009",
                "from_last_table_index": 0,
                "last_chunk_id": "cfe5fc6321be4ecf83d5d9dbcb50e009",
            },
            1: {
                "first_table_index": 2,  # 第一个TABLE在index 2 (b91ea66fcee04f7bb3a5a58605b6e405)
                "first_chunk_id": "b91ea66fcee04f7bb3a5a58605b6e405",
                "from_last_table_index": 0,
                "last_chunk_id": "b91ea66fcee04f7bb3a5a58605b6e405",
            },
            2: {
                "first_table_index": 4,  # 第一个TABLE在index 4 (8d6c8fbabf12413da30e2988d3736b72)
                "first_chunk_id": "8d6c8fbabf12413da30e2988d3736b72",
                "from_last_table_index": 0,
                "last_chunk_id": "8d6c8fbabf12413da30e2988d3736b72",
            },
            3: {
                "first_table_index": 6,  # 第一个TABLE在index 6
                "first_chunk_id": "47ee491c3dd64e00ae2e1cd1c44044be",
                "from_last_table_index": 0,
                "last_chunk_id": "5ff2cbe57eda4fa2b8ae5421aa0c472b",
            },
            4: {
                "first_table_index": 9,  # 第一个TABLE在index 9
                "first_chunk_id": "c98914c3506c4322a13386cd98cd35a2",
                "from_last_table_index": 0,
                "last_chunk_id": "c3b9b02d9927468cb729ad4e2ccf3317",
            },
            5: {
                "first_table_index": 12,  # 第一个TABLE在index 12
                "first_chunk_id": "3f23cdfd0d4b4601b4490d2cdee57b77",
                "from_last_table_index": 0,
                "last_chunk_id": "3f23cdfd0d4b4601b4490d2cdee57b77",
            }
        }
        
        # 调用真实的函数
        result = process_and_merge_chunks(chunks, table_indices)
        
        # 验证结果
        assert isinstance(result, list)
        print(f"process_and_merge_chunks result: {result}")  # 调试输出

    def test_process_and_merge_chunks_real_log_data_with_expected_results(self):
        """测试 process_and_merge_chunks 基于真实日志数据并验证预期结果"""
        # 构造完整的14个chunk对象，重点关注可合并的chunks
        chunks = [
            # 跨页表格场景 - 模拟临床项目信息表格被分成多页
            self.create_test_chunk("cfe5fc6321be4ecf83d5d9dbcb50e009", 
                '<table><tr><td rowspan="1" colspan="15">临床项目及报告单位信息</td></tr><tr><td rowspan="1" colspan="2">医疗机构及专业名称</td><td rowspan="1" colspan="7">上海市第六人民医院呼吸内科</td></tr></table>', 
                [0], LabelType.TABLE),
            
            self.create_test_chunk("b91ea66fcee04f7bb3a5a58605b6e405", 
                '<table><tr><td rowspan="1" colspan="1">相关Ag21-1升 高</td><td rowspan="1" colspan="2"></td></tr><tr><td rowspan="1" colspan="1">肝功能不全</td><td rowspan="1" colspan="2">2022-01-12</td></tr></table>', 
                [1], LabelType.TABLE),
            
            self.create_test_chunk("8d6c8fbabf12413da30e2988d3736b72", 
                '<table><tr><td rowspan="1" colspan="1">游离甲状膜素</td><td rowspan="1" colspan="1">2024.5.19</td><td rowspan="1" colspan="1">23.19</td><td rowspan="1" colspan="1">mg/ml</td></tr></table>', 
                [2], LabelType.TABLE),
            
            # 另一组跨页表格 - SAE相关信息
            self.create_test_chunk("c3b9b02d9927468cb729ad4e2ccf3317", 
                '<table><tr><td rowspan="1" colspan="1">SAE发生及处理的详细情况(如同时发生多个严重不良事件，可统一记录在此处)</td></tr></table>', 
                [4], LabelType.TABLE),
            
            self.create_test_chunk("3f23cdfd0d4b4601b4490d2cdee57b77", 
                '<table><tr><td rowspan="1" colspan="1">[对可疑产品采取的措施] 不适用</td></tr><tr><td rowspan="1" colspan="1">[研究者相关性评价] 此次SAE与研究药物人脐带间充质干细胞注射液可能无关</td></tr></table>', 
                [5], LabelType.TABLE),
        ]

        # 构造table_indices - 模拟classify_and_mark_table_indices的输出
        table_indices = {
            0: {
                "first_table_index": 0,
                "first_chunk_id": "cfe5fc6321be4ecf83d5d9dbcb50e009",
                "from_last_table_index": 0,
                "last_chunk_id": "cfe5fc6321be4ecf83d5d9dbcb50e009",
            },
            1: {
                "first_table_index": 1,
                "first_chunk_id": "b91ea66fcee04f7bb3a5a58605b6e405",
                "from_last_table_index": 0,
                "last_chunk_id": "b91ea66fcee04f7bb3a5a58605b6e405",
            },
            2: {
                "first_table_index": 2,
                "first_chunk_id": "8d6c8fbabf12413da30e2988d3736b72",
                "from_last_table_index": 0,
                "last_chunk_id": "8d6c8fbabf12413da30e2988d3736b72",
            },
            4: {
                "first_table_index": 3,
                "first_chunk_id": "c3b9b02d9927468cb729ad4e2ccf3317",
                "from_last_table_index": 0,
                "last_chunk_id": "c3b9b02d9927468cb729ad4e2ccf3317",
            },
            5: {
                "first_table_index": 4,
                "first_chunk_id": "3f23cdfd0d4b4601b4490d2cdee57b77",
                "from_last_table_index": 0,
                "last_chunk_id": "3f23cdfd0d4b4601b4490d2cdee57b77",
            }
        }
        
        # 调用真实函数
        result = process_and_merge_chunks(chunks, table_indices)
        
        # 验证结果
        assert isinstance(result, list)
        print(f"process_and_merge_chunks detailed result: {result}")
        
        # 如果有合并结果，验证结构
        for merged_result in result:
            assert "content" in merged_result
            assert "chunk_ids" in merged_result
            assert isinstance(merged_result["content"], list)
            assert isinstance(merged_result["chunk_ids"], list)
            assert len(merged_result["content"]) == len(merged_result["chunk_ids"])
            
        # 验证chunk_ids都是有效的
        all_chunk_ids = [chunk.chunk_id for chunk in chunks]
        for merged_result in result:
            for chunk_id in merged_result["chunk_ids"]:
                assert chunk_id in all_chunk_ids


class TestMergeChunksWithBase:
    """merge_chunks_with_base 函数的测试用例"""

    def add_dsts_to_chunk(self, chunk):
        """为Chunk对象添加dsts字段"""
        if chunk.dsts is None:
            chunk.dsts = [DST(
                id=f"dst_{chunk.chunk_id}",
                parent="root",
                order=0,
                dst_type=DSTType.TABLE if chunk.label == LabelType.TABLE else DSTType.TEXT,
                attributes=DSTAttribute(
                    level=0,
                    position=PositionInfo(),
                    page=chunk.page_num[0] if chunk.page_num else 1,
                    hash="a" * 32
                ),
                content=[chunk.content]
            )]
        return chunk

    def test_merge_chunks_with_base_empty(self):
        """测试 merge_chunks_with_base 处理空输入的情况"""
        result = merge_chunks_with_base([], [])
        
        assert result == []

    def test_merge_chunks_with_base_no_merged_results(self):
        """测试 merge_chunks_with_base 没有合并结果的情况"""
        chunk = Chunk(
            chunk_id="chunk_1",
            page_size=100,
            content="<table><tr><td>Original</td></tr></table>",
            label=LabelType.TABLE,
            page_num=[1],
            block=["block_1"]
        )
        
        result = merge_chunks_with_base([chunk], [])
        
        assert len(result) == 1
        assert result[0] == chunk

    def test_merge_chunks_with_base_with_merged_results(self):
        """测试 merge_chunks_with_base 有合并结果的情况"""
        chunk1 = Chunk(
            chunk_id="chunk_1",
            page_size=100,
            content="<table><tr><td>Original 1</td></tr></table>",
            label=LabelType.TABLE,
            page_num=[1],
            block=["block_1"]
        )
        chunk2 = Chunk(
            chunk_id="chunk_2",
            page_size=100,
            content="<table><tr><td>Original 2</td></tr></table>",
            label=LabelType.TABLE,
            page_num=[2],
            block=["block_2"]
        )
        
        # 为chunks添加dsts字段
        chunk1 = self.add_dsts_to_chunk(chunk1)
        chunk2 = self.add_dsts_to_chunk(chunk2)
        
        merged_results = [{
            "content": ["<table><tr><td>Original 1</td></tr></table>", "<table><tr><td>Original 2</td></tr></table>"],
            "chunk_ids": ["chunk_1", "chunk_2"]
        }]

        result = merge_chunks_with_base([chunk1, chunk2], merged_results)

        assert len(result) == 1  # chunk2 被合并到 chunk1，所以只剩下 chunk1
        assert result[0].chunk_id == "chunk_1"
        # 检查内容是否已合并
        assert "<table>" in result[0].content

    def test_merge_chunks_with_base_multiple_merged_results(self):
        """测试 merge_chunks_with_base 处理多个合并结果的情况"""
        chunk = Chunk(
            chunk_id="chunk_1",
            page_size=100,
            content="<table><tr><td>Original</td></tr></table>",
            label=LabelType.TABLE,
            page_num=[1],
            block=["block_1"]
        )
        
        merged_results = []  # 此测试的空合并结果

        result = merge_chunks_with_base([chunk], merged_results)

        assert len(result) == 1  # 只剩下原始块
        assert result[0] == chunk

    def test_merge_chunks_with_base_real_log_data(self):
        """测试 merge_chunks_with_base 基于真实日志数据的情况"""
        # 基于日志构造原始chunks - 这里只构造关键的几个chunks用于测试
        chunk1 = Chunk(
            chunk_id="cfe5fc6321be4ecf83d5d9dbcb50e009",
            page_size=6,
            content='<table><tr><td rowspan="1" colspan="15">临床项目及报告单位信息</td></tr><tr><td rowspan="1" colspan="2">医疗机构及专业名称</td><td rowspan="1" colspan="7">上海市第六人民医院呼吸内科</td><td rowspan="1" colspan="3">电话</td><td rowspan="1" colspan="3"></td></tr></table>',
            label=LabelType.TABLE,
            page_num=[0],
            block=["486708b29b774f0ea43e9e158458312a"]
        )
        
        chunk2 = Chunk(
            chunk_id="b91ea66fcee04f7bb3a5a58605b6e405",
            page_size=6,
            content='<table><tr><td rowspan="1" colspan="1">相关Ag21-1升 高</td><td rowspan="1" colspan="2"></td><td rowspan="1" colspan="2"></td><td rowspan="1" colspan="2"></td><td rowspan="1" colspan="3"></td></tr></table>',
            label=LabelType.TABLE,
            page_num=[1],
            block=["d33c7c3164eb41a095d4b47d2f17b408"]
        )
        
        chunk3 = Chunk(
            chunk_id="8d6c8fbabf12413da30e2988d3736b72",
            page_size=6,
            content='<table><tr><td rowspan="1" colspan="1">游离甲状膜素</td><td rowspan="1" colspan="1">2024.5.19</td><td rowspan="1" colspan="1">23.19</td><td rowspan="1" colspan="1">mg/ml</td><td rowspan="1" colspan="1">10</td><td rowspan="1" colspan="1">22</td><td rowspan="1" colspan="1"></td></tr></table>',
            label=LabelType.TABLE,
            page_num=[2],
            block=["4de848e55bb04d809389dba2a9cb63c7"]
        )
        
        chunk4 = Chunk(
            chunk_id="c3b9b02d9927468cb729ad4e2ccf3317",
            page_size=6,
            content='<table><tr><td rowspan="1" colspan="1">SAE发生及处理的详细情况(如同时发生多个严重不良事件，可统一记录在此处)</td></tr></table>',
            label=LabelType.TABLE,
            page_num=[4],
            block=["6e3c113f03114e65bc32115aed32f19a"]
        )
        
        chunk5 = Chunk(
            chunk_id="3f23cdfd0d4b4601b4490d2cdee57b77",
            page_size=6,
            content='<table><tr><td rowspan="1" colspan="1">[对可疑产品采取的措施] 不适用 [不良事件转归 至此报告时，患者有腹水，人清醒，仍住院，转归持续。</td></tr></table>',
            label=LabelType.TABLE,
            page_num=[5],
            block=["5d3dd0de28804797bbbab11581773646"]
        )
        
        # 为chunks添加dsts字段
        for chunk in [chunk1, chunk2, chunk3, chunk4, chunk5]:
            chunk = self.add_dsts_to_chunk(chunk)
        
        chunks = [chunk1, chunk2, chunk3, chunk4, chunk5]
        
        # 基于真实日志构造merged_results
        merged_results = [
            {
                'content': [
                    '<table><tr><td rowspan="1" colspan="15">临床项目及报告单位信息</td></tr><tr><td rowspan="1" colspan="2">医疗机构及专业名称</td><td rowspan="1" colspan="7">上海市第六人民医院呼吸内科</td><td rowspan="1" colspan="3">电话</td><td rowspan="1" colspan="3"></td></tr></table>',
                    '<table><tr><td rowspan="1" colspan="1">相关Ag21-1升 高</td><td rowspan="1" colspan="2"></td><td rowspan="1" colspan="2"></td><td rowspan="1" colspan="2"></td><td rowspan="1" colspan="3"></td></tr></table>',
                    '<table><tr><td rowspan="1" colspan="1">游离甲状膜素</td><td rowspan="1" colspan="1">2024.5.19</td><td rowspan="1" colspan="1">23.19</td><td rowspan="1" colspan="1">mg/ml</td><td rowspan="1" colspan="1">10</td><td rowspan="1" colspan="1">22</td><td rowspan="1" colspan="1"></td></tr></table>'
                ],
                'chunk_ids': ['cfe5fc6321be4ecf83d5d9dbcb50e009', 'b91ea66fcee04f7bb3a5a58605b6e405', '8d6c8fbabf12413da30e2988d3736b72']
            },
            {
                'content': [
                    '<table><tr><td rowspan="1" colspan="1">SAE发生及处理的详细情况(如同时发生多个严重不良事件，可统一记录在此处)</td></tr></table>',
                    '<table><tr><td rowspan="1" colspan="1">[对可疑产品采取的措施] 不适用 [不良事件转归 至此报告时，患者有腹水，人清醒，仍住院，转归持续。</td></tr></table>'
                ],
                'chunk_ids': ['c3b9b02d9927468cb729ad4e2ccf3317', '3f23cdfd0d4b4601b4490d2cdee57b77']
            }
        ]
        
        # 调用真实函数
        result = merge_chunks_with_base(chunks, merged_results)
        
        # 验证结果
        assert isinstance(result, list)
        assert len(result) > 0  # 应该有结果返回
        
        # 验证合并后的chunks数量应该少于原始数量（因为有些被合并了）
        # 原有5个chunks，第一个合并结果合并了3个chunk，第二个合并结果合并了2个chunk
        # 所以最终应该有2个chunk（每个合并结果对应一个）
        expected_chunk_count = len(chunks) - len(merged_results[0]['chunk_ids']) - len(merged_results[1]['chunk_ids']) + len(merged_results)
        assert len(result) == expected_chunk_count


class TestCrossTableMerge:
    """cross_table_merge 函数的测试用例"""

    def add_dsts_to_chunk(self, chunk):
        """为Chunk对象添加dsts字段"""
        if chunk.dsts is None:
            chunk.dsts = [DST(
                id=f"dst_{chunk.chunk_id}",
                parent="root",
                order=0,
                dst_type=DSTType.TABLE if chunk.label == LabelType.TABLE else DSTType.TEXT,
                attributes=DSTAttribute(
                    level=0,
                    position=PositionInfo(),
                    page=chunk.page_num[0] if chunk.page_num else 1,
                    hash="a" * 32
                ),
                content=[chunk.content]
            )]
        return chunk

    def test_cross_table_merge_empty(self):
        """测试 cross_table_merge 处理空块的情况"""
        result = cross_table_merge([])
        
        assert result == []

    @patch('modules.cross_table.cross_page_merge.process_and_merge_chunks')
    @patch('modules.cross_table.cross_page_merge.merge_chunks_with_base')
    @patch('modules.cross_table.cross_page_merge.classify_and_mark_table_indices')
    def test_cross_table_merge_integration(self, mock_classify, mock_merge_base, mock_process):
        """测试 cross_table_merge 集成功能"""
        chunk = Chunk(
            chunk_id="chunk_1",
            page_size=100,
            content="<table><tr><td>Test</td></tr></table>",
            label=LabelType.TABLE,
            page_num=[1],
            block=["block_1"]
        )
        
        mock_classify.return_value = {1: [chunk]}
        mock_process.return_value = ["<table><tr><td>Merged</td></tr></table>"]
        mock_merge_base.return_value = [chunk]
        
        result = cross_table_merge([chunk])
        
        assert result == [chunk]
        mock_classify.assert_called_once_with([chunk])
        mock_process.assert_called_once_with([chunk], {1: [chunk]})
        mock_merge_base.assert_called_once_with([chunk], ["<table><tr><td>Merged</td></tr></table>"])

    @patch('modules.cross_table.cross_page_merge.process_and_merge_chunks')
    @patch('modules.cross_table.cross_page_merge.merge_chunks_with_base')
    @patch('modules.cross_table.cross_page_merge.classify_and_mark_table_indices')
    def test_cross_table_merge_multiple_chunks(self, mock_classify, mock_merge_base, mock_process):
        """测试 cross_table_merge 处理多个块的情况"""
        chunk1 = Chunk(
            chunk_id="chunk_1",
            page_size=100,
            content="<table><tr><td>Table 1</td></tr></table>",
            label=LabelType.TABLE,
            page_num=[1],
            block=["block_1"]
        )
        chunk2 = Chunk(
            chunk_id="chunk_2",
            page_size=100,
            content="<table><tr><td>Table 2</td></tr></table>",
            label=LabelType.TABLE,
            page_num=[2],
            block=["block_2"]
        )
        chunks = [chunk1, chunk2]
        
        mock_classify.return_value = {1: [chunk1], 2: [chunk2]}
        mock_process.return_value = [
            "<table><tr><td>Merged 1</td></tr></table>",
            "<table><tr><td>Merged 2</td></tr></table>"
        ]
        mock_merge_base.return_value = chunks
        
        result = cross_table_merge(chunks)
        
        assert result == chunks
        mock_classify.assert_called_once_with(chunks)
        mock_process.assert_called_once_with(chunks, {1: [chunk1], 2: [chunk2]})
        mock_merge_base.assert_called_once()

    def test_cross_table_merge_real_scenario(self):
        """测试 cross_table_merge 更真实的场景"""
        # 创建可能代表跨页面分割表格的块
        chunk1 = Chunk(
            chunk_id="table_part_1",
            page_size=100,
            content="<table><tr><td>Header 1</td><td>Header 2</td></tr><tr><td>Row 1 Col 1</td><td>Row 1 Col 2</td></tr></table>",
            label=LabelType.TABLE,
            page_num=[1],
            block=["block_1"]
        )
        chunk2 = Chunk(
            chunk_id="table_part_2",
            page_size=100,
            content="<table><tr><td>Row 2 Col 1</td><td>Row 2 Col 2</td></tr><tr><td>Row 3 Col 1</td><td>Row 3 Col 2</td></tr></table>",
            label=LabelType.TABLE,
            page_num=[2],
            block=["block_2"]
        )
        
        # 为chunks添加dsts字段
        chunk1 = self.add_dsts_to_chunk(chunk1)
        chunk2 = self.add_dsts_to_chunk(chunk2)
        
        chunks = [chunk1, chunk2]
        
        # 这应该与实际实现一起工作
        result = cross_table_merge(chunks)
        
        # 结果应该包含合并后的块
        assert isinstance(result, list)
        assert len(result) > 0  # 应该有结果
        # 合并可能减少chunk数量，所以不应该期望至少有原始数量的chunks


class TestMergeChunksWithBaseV2:
    """merge_chunks_with_base_v2 函数的测试用例"""

    def add_dsts_to_chunk(self, chunk):
        """为Chunk对象添加dsts字段"""
        if chunk.dsts is None:
            chunk.dsts = [DST(
                id=f"dst_{chunk.chunk_id}",
                parent="root",
                order=0,
                dst_type=DSTType.TABLE if chunk.label == LabelType.TABLE else DSTType.TEXT,
                attributes=DSTAttribute(
                    level=0,
                    position=PositionInfo(),
                    page=chunk.page_num[0] if chunk.page_num else 1,
                    hash="a" * 32
                ),
                content=[chunk.content]
            )]
        return chunk

    def test_merge_chunks_with_base_v2_empty(self):
        """测试 merge_chunks_with_base_v2 处理空输入的情况"""
        result = merge_chunks_with_base_v2([], [])
        
        assert result == []

    def test_merge_chunks_with_base_v2_no_merged_results(self):
        """测试 merge_chunks_with_base_v2 没有合并结果的情况"""
        chunk = Chunk(
            chunk_id="chunk_1",
            page_size=100,
            content="<table><tr><td>Original</td></tr></table>",
            label=LabelType.TABLE,
            page_num=[1],
            block=["block_1"]
        )
        chunk = self.add_dsts_to_chunk(chunk)
        
        result = merge_chunks_with_base_v2([chunk], [])
        
        assert len(result) == 1
        assert result[0] == chunk

    def test_merge_chunks_with_base_v2_with_merged_results(self):
        """测试 merge_chunks_with_base_v2 有合并结果的情况"""
        # 创建基础chunk
        base_chunk = Chunk(
            chunk_id="d518aa4c9ce248268cd6cfffe6e09b7a",
            page_size=100,
            content="<table><tr><td>Base Table</td></tr></table>",
            label=LabelType.TABLE,
            page_num=[1],
            block=["block_1"]
        )
        base_chunk = self.add_dsts_to_chunk(base_chunk)
        
        # 创建要合并的chunk
        merge_chunk1 = Chunk(
            chunk_id="d178adaec6424f66ab78a7d33213c087",
            page_size=100,
            content="<table><tr><td>Merge Table 1</td></tr></table>",
            label=LabelType.TABLE,
            page_num=[2],
            block=["block_2"]
        )
        merge_chunk1 = self.add_dsts_to_chunk(merge_chunk1)
        
        merge_chunk2 = Chunk(
            chunk_id="0828b947625d41dfba2ab0e9096bdeaa",
            page_size=100,
            content="<table><tr><td>Merge Table 2</td></tr></table>",
            label=LabelType.TABLE,
            page_num=[3],
            block=["block_3"]
        )
        merge_chunk2 = self.add_dsts_to_chunk(merge_chunk2)
        
        chunks = [base_chunk, merge_chunk1, merge_chunk2]
        
        # 基于日志数据构造的merged_results
        merged_results = [{
            "table_info": [None, None, None],
            "merged_type": ["sub_tables_merge", "sub_tables_merge", "sub_tables_merge"],
            "content": [
                "<table><tr><td>Merged Content 1</td></tr></table>",
                "<table><tr><td>Merged Content 2</td></tr></table>",
                "<table><tr><td>Merged Content 3</td></tr></table>"
            ],
            "chunk_ids": ["d518aa4c9ce248268cd6cfffe6e09b7a", "d178adaec6424f66ab78a7d33213c087", "0828b947625d41dfba2ab0e9096bdeaa"]
        }]
        
        with patch('modules.cross_table.cross_page_merge.merge_table_by_force_v2') as mock_merge_force:
            mock_merge_force.return_value = "<table><tr><td>Force Merged Content</td></tr></table>"
            
            result = merge_chunks_with_base_v2(chunks, merged_results)
            
            # 验证结果
            assert len(result) == 1  # 应该只剩下基础chunk
            assert result[0].chunk_id == "d518aa4c9ce248268cd6cfffe6e09b7a"
            assert result[0].content == "<table><tr><td>Force Merged Content</td></tr></table>"
            assert result[0].page_num == [1, 2, 3]  # 应该包含所有页面的页码
            assert len(result[0].dsts) == 3  # 应该包含所有dsts
            assert len(result[0].block) == 3  # 应该包含所有blocks
            
            # 验证table_info和merged_type被正确设置
            assert result[0].dsts[0].table_info is None
            assert result[0].dsts[0].merged_type == "sub_tables_merge"
            assert result[0].dsts[1].table_info is None
            assert result[0].dsts[1].merged_type == "sub_tables_merge"
            assert result[0].dsts[2].table_info is None
            assert result[0].dsts[2].merged_type == "sub_tables_merge"
            
            # 验证merge_table_by_force_v2被调用
            mock_merge_force.assert_called_once_with(merged_results[0])

    def test_merge_chunks_with_base_v2_real_log_data(self):
        """基于实际日志数据测试 merge_chunks_with_base_v2"""
        # 基于日志中的实际数据构造测试用例
        base_chunk = Chunk(
            chunk_id="d518aa4c9ce248268cd6cfffe6e09b7a",
            page_size=100,
            content="<table><tr><td>临床项目及报告单位信息</td></tr></table>",
            label=LabelType.TABLE,
            page_num=[1],
            block=["block_1"]
        )
        base_chunk = self.add_dsts_to_chunk(base_chunk)
        
        merge_chunk1 = Chunk(
            chunk_id="d178adaec6424f66ab78a7d33213c087",
            page_size=100,
            content="<table><tr><td>相关Ag21-1升高</td></tr></table>",
            label=LabelType.TABLE,
            page_num=[2],
            block=["block_2"]
        )
        merge_chunk1 = self.add_dsts_to_chunk(merge_chunk1)
        
        merge_chunk2 = Chunk(
            chunk_id="0828b947625d41dfba2ab0e9096bdeaa",
            page_size=100,
            content="<table><tr><td>游离甲状膜素</td></tr></table>",
            label=LabelType.TABLE,
            page_num=[3],
            block=["block_3"]
        )
        merge_chunk2 = self.add_dsts_to_chunk(merge_chunk2)
        
        chunks = [base_chunk, merge_chunk1, merge_chunk2]
        
        # 基于日志中的实际merged_results数据
        merged_results = [{
            "table_info": [None, None, None],
            "merged_type": ["sub_tables_merge", "sub_tables_merge", "sub_tables_merge"],
            "content": [
                '<table><tr><td rowspan="1" colspan="15">临床项目及报告单位信息</td></tr><tr><td rowspan="1" colspan="2">医疗机构及专业名称</td><td rowspan="1" colspan="7">上海市第六人民医院呼吸内科</td><td rowspan="1" colspan="3">电话</td><td rowspan="1" colspan="3"></td></tr></table>',
                '<table><tr><td rowspan="1" colspan="1">相关Ag21-1升 高</td><td rowspan="1" colspan="2"></td><td rowspan="1" colspan="2"></td><td rowspan="1" colspan="2"></td><td rowspan="1" colspan="3"></td></tr></table>',
                '<table><tr><td rowspan="1" colspan="1">游离甲状膜素</td><td rowspan="1" colspan="1">2024.5.19</td><td rowspan="1" colspan="1">23.19</td><td rowspan="1" colspan="1">mg/ml</td><td rowspan="1" colspan="1">10</td><td rowspan="1" colspan="1">22</td><td rowspan="1" colspan="1"></td></tr></table>'
            ],
            "chunk_ids": ["d518aa4c9ce248268cd6cfffe6e09b7a", "d178adaec6424f66ab78a7d33213c087", "0828b947625d41dfba2ab0e9096bdeaa"]
        }]
        
        with patch('modules.cross_table.cross_page_merge.merge_table_by_force_v2') as mock_merge_force:
            # 模拟合并后的内容
            mock_merge_force.return_value = '<table><tr><td rowspan="1" colspan="15">临床项目及报告单位信息</td></tr><tr><td rowspan="1" colspan="2">医疗机构及专业名称</td><td rowspan="1" colspan="7">上海市第六人民医院呼吸内科</td><td rowspan="1" colspan="3">电话</td><td rowspan="1" colspan="3"></td></tr><tr><td rowspan="1" colspan="1">相关Ag21-1升 高</td><td rowspan="1" colspan="2"></td><td rowspan="1" colspan="2"></td><td rowspan="1" colspan="2"></td><td rowspan="1" colspan="3"></td></tr><tr><td rowspan="1" colspan="1">游离甲状膜素</td><td rowspan="1" colspan="1">2024.5.19</td><td rowspan="1" colspan="1">23.19</td><td rowspan="1" colspan="1">mg/ml</td><td rowspan="1" colspan="1">10</td><td rowspan="1" colspan="1">22</td><td rowspan="1" colspan="1"></td></tr></table>'
            
            result = merge_chunks_with_base_v2(chunks, merged_results)
            
            # 验证结果
            assert len(result) == 1
            assert result[0].chunk_id == "d518aa4c9ce248268cd6cfffe6e09b7a"
            assert "临床项目及报告单位信息" in result[0].content
            assert "上海市第六人民医院呼吸内科" in result[0].content
            assert "相关Ag21-1升 高" in result[0].content
            assert "游离甲状膜素" in result[0].content
            assert result[0].page_num == [1, 2, 3]
            assert len(result[0].dsts) == 3
            assert len(result[0].block) == 3
            
            # 验证所有dsts的merged_type都被正确设置
            for dst in result[0].dsts:
                assert dst.merged_type == "sub_tables_merge"

    def test_merge_chunks_with_base_v2_multiple_merged_groups(self):
        """测试 merge_chunks_with_base_v2 处理多个合并组的情况"""
        # 创建第一组chunks
        base_chunk1 = Chunk(
            chunk_id="chunk_1",
            page_size=100,
            content="<table><tr><td>Group 1 Base</td></tr></table>",
            label=LabelType.TABLE,
            page_num=[1],
            block=["block_1"]
        )
        base_chunk1 = self.add_dsts_to_chunk(base_chunk1)
        
        merge_chunk1 = Chunk(
            chunk_id="chunk_2",
            page_size=100,
            content="<table><tr><td>Group 1 Merge</td></tr></table>",
            label=LabelType.TABLE,
            page_num=[2],
            block=["block_2"]
        )
        merge_chunk1 = self.add_dsts_to_chunk(merge_chunk1)
        
        # 创建第二组chunks
        base_chunk2 = Chunk(
            chunk_id="chunk_3",
            page_size=100,
            content="<table><tr><td>Group 2 Base</td></tr></table>",
            label=LabelType.TABLE,
            page_num=[3],
            block=["block_3"]
        )
        base_chunk2 = self.add_dsts_to_chunk(base_chunk2)
        
        merge_chunk2 = Chunk(
            chunk_id="chunk_4",
            page_size=100,
            content="<table><tr><td>Group 2 Merge</td></tr></table>",
            label=LabelType.TABLE,
            page_num=[4],
            block=["block_4"]
        )
        merge_chunk2 = self.add_dsts_to_chunk(merge_chunk2)
        
        chunks = [base_chunk1, merge_chunk1, base_chunk2, merge_chunk2]
        
        # 两个合并组
        merged_results = [
            {
                "table_info": [None, None],
                "merged_type": ["sub_tables_merge", "sub_tables_merge"],
                "content": [
                    "<table><tr><td>Merged Group 1</td></tr></table>",
                    "<table><tr><td>Merged Group 1 Part 2</td></tr></table>"
                ],
                "chunk_ids": ["chunk_1", "chunk_2"]
            },
            {
                "table_info": [None, None],
                "merged_type": ["sub_tables_merge", "sub_tables_merge"],
                "content": [
                    "<table><tr><td>Merged Group 2</td></tr></table>",
                    "<table><tr><td>Merged Group 2 Part 2</td></tr></table>"
                ],
                "chunk_ids": ["chunk_3", "chunk_4"]
            }
        ]
        
        with patch('modules.cross_table.cross_page_merge.merge_table_by_force_v2') as mock_merge_force:
            mock_merge_force.side_effect = [
                "<table><tr><td>Final Group 1</td></tr></table>",
                "<table><tr><td>Final Group 2</td></tr></table>"
            ]
            
            result = merge_chunks_with_base_v2(chunks, merged_results)
            
            # 验证结果
            assert len(result) == 2  # 应该有两个基础chunk
            assert result[0].chunk_id == "chunk_1"
            assert result[1].chunk_id == "chunk_3"
            assert result[0].content == "<table><tr><td>Final Group 1</td></tr></table>"
            assert result[1].content == "<table><tr><td>Final Group 2</td></tr></table>"
            
            # 验证merge_table_by_force_v2被调用了两次
            assert mock_merge_force.call_count == 2

    def test_merge_chunks_with_base_v2_missing_chunk_in_map(self):
        """测试 merge_chunks_with_base_v2 处理chunk_map中缺失chunk的情况"""
        base_chunk = Chunk(
            chunk_id="chunk_1",
            page_size=100,
            content="<table><tr><td>Base Table</td></tr></table>",
            label=LabelType.TABLE,
            page_num=[1],
            block=["block_1"]
        )
        base_chunk = self.add_dsts_to_chunk(base_chunk)
        
        chunks = [base_chunk]
        
        # merged_results中引用了不存在的chunk_id
        merged_results = [{
            "table_info": [None, None],
            "merged_type": ["sub_tables_merge", "sub_tables_merge"],
            "content": [
                "<table><tr><td>Content 1</td></tr></table>",
                "<table><tr><td>Content 2</td></tr></table>"
            ],
            "chunk_ids": ["chunk_1", "non_existent_chunk"]
        }]
        
        with patch('modules.cross_table.cross_page_merge.merge_table_by_force_v2') as mock_merge_force:
            mock_merge_force.return_value = "<table><tr><td>Merged Content</td></tr></table>"
            
            result = merge_chunks_with_base_v2(chunks, merged_results)
            
            # 应该正常处理，不会因为缺失的chunk而报错
            assert len(result) == 1
            assert result[0].chunk_id == "chunk_1"
            assert result[0].content == "<table><tr><td>Merged Content</td></tr></table>"
