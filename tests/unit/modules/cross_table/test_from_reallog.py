"""
基于真实日志数据的测试用例
从 app.log 中提取的真实数据进行测试，确保功能在实际场景下正常工作
"""
import pytest
from unittest.mock import patch, MagicMock

from modules.cross_table.cross_page_merge import (
    classify_and_mark_table_indices, process_and_merge_chunks,
    merge_chunks_with_base
)
from modules.entity.chunk_entity import Chunk, LabelType
from modules.entity.dst_entity import DSTType, DST, DSTAttribute, BBox, PositionInfo


class TestRealLogData:
    """基于真实日志数据的综合测试"""

    def create_test_chunk(self, chunk_id, content, page_num, label_type=LabelType.TABLE):
        """创建具有必需字段的测试块的辅助方法"""
        # 处理负数页码，将其转换为有效的页码
        actual_page = page_num[0] if isinstance(page_num, list) else page_num
        if actual_page < 0:
            actual_page = 0  # 将负数页码转换为0
        
        # 创建一个测试用的DST对象
        test_dst = DST(
            id=f"dst_{chunk_id}",
            parent="root",
            order=0,
            dst_type=DSTType.TABLE if label_type == LabelType.TABLE else (DSTType.TEXT if label_type == LabelType.TEXT else DSTType.IMAGE),
            attributes=DSTAttribute(
                level=0,
                position=PositionInfo(bbox=BBox(x1=0, y1=0, x2=100, y2=100)),
                page=actual_page,
                hash="a" * 32
            ),
            content=[content],
            mark=None
        )
        
        chunk = Chunk(
            chunk_id=chunk_id,
            page_size=6,
            content=content,
            label=label_type,
            page_num=[actual_page],
            block=[f"block_{chunk_id}"],
            dsts=[test_dst]
        )
        
        return chunk

    def get_all_14_chunks_from_log(self):
        """返回日志中完整的14个chunks - 提取复用"""
        return [
            # 1. TEXT chunk on page 0
            self.create_test_chunk("04367f973d7743788083e3a49a1cfa12", 
                '严重不良事件(SAE)报告表 \\n\\n  新药临床批准文号： 报告时问：2024.05.24 报告类型：  □首次 ☑随访 ☑总结', 
                [0], LabelType.TEXT),
            
            # 2. TABLE chunk on page 0 
            self.create_test_chunk("cfe5fc6321be4ecf83d5d9dbcb50e009", 
                '<table><tr><td rowspan="1" colspan="15">临床项目及报告单位信息</td></tr><tr><td rowspan="1" colspan="2">医疗机构及专业名称</td><td rowspan="1" colspan="7">上海市第六人民医院呼吸内科</td><td rowspan="1" colspan="3">电话</td><td rowspan="1" colspan="3"></td></tr><tr><td rowspan="1" colspan="2">中报单位名称</td><td rowspan="1" colspan="7">上海市第六人民医院</td><td rowspan="1" colspan="3">电话</td><td rowspan="1" colspan="3"></td></tr><tr><td rowspan="1" colspan="2">临床研究方案名称</td><td rowspan="1" colspan="13">探索人脐带问充质干细胞注射液湘 驶性及初步有效性 的开放性临床研究</td></tr><tr><td rowspan="1" colspan="2">临床研究方案号</td><td rowspan="1" colspan="13">1</td></tr><tr><td rowspan="1" colspan="2">临床适应症</td><td rowspan="1" colspan="13">质纤维化</td></tr><tr><td rowspan="1" colspan="2">临床研究分类</td><td rowspan="1" colspan="13">DI期 冈Ⅱ期  □Ⅲ期    □IV期   □生物等效性试验  □验证类临床试验</td></tr><tr><td rowspan="1" colspan="2">试验盲态情况</td><td rowspan="1" colspan="13">凶非盲态  口盲态( 口未破盲   □已破盲-破盲时间： 年 月 日)</td></tr><tr><td rowspan="1" colspan="15">报告者信息</td></tr><tr><td rowspan="1" colspan="2">报告者姓名</td><td rowspan="1" colspan="7"></td><td rowspan="1" colspan="3">所在国家</td><td rowspan="1" colspan="3">中国</td></tr><tr><td rowspan="1" colspan="2">职业</td><td rowspan="1" colspan="7">呼吸科医师</td><td rowspan="1" colspan="3">电话</td><td rowspan="1" colspan="3"></td></tr><tr><td rowspan="1" colspan="2">获知SAE时问</td><td rowspan="1" colspan="13"> □首次获知时问 ___年___月__日 冈随访信息获知时问2024_年  05月23日19:25</td></tr><tr><td rowspan="1" colspan="15">受试者信息</td></tr><tr><td rowspan="1" colspan="1">姓名缩写</td><td rowspan="1" colspan="3"></td><td rowspan="1" colspan="2">性别</td><td rowspan="1" colspan="1">男</td><td rowspan="1" colspan="3">出生日期</td><td rowspan="1" colspan="2">1965年3 月25日</td><td rowspan="1" colspan="2">发生SAE时的年龄</td><td rowspan="1" colspan="1">59</td></tr><tr><td rowspan="1" colspan="1">受试者编号</td><td rowspan="1" colspan="3"></td><td rowspan="1" colspan="2">民族</td><td rowspan="1" colspan="1">汉</td><td rowspan="1" colspan="3">身高(cm)</td><td rowspan="1" colspan="2">170</td><td rowspan="1" colspan="2">体重(kg)</td><td rowspan="1" colspan="1">87</td></tr><tr><td rowspan="2" colspan="1">患者死亡</td><td rowspan="1" colspan="14"> □否</td></tr><tr><td rowspan="1" colspan="2">区是</td><td rowspan="1" colspan="1">死亡 日期</td><td rowspan="1" colspan="3">2024年5月23 日</td><td rowspan="1" colspan="1">死亡 原因</td><td rowspan="1" colspan="3">急性肝衰竭</td><td rowspan="1" colspan="2">是否 尸检</td><td rowspan="1" colspan="2"> ☑否  □是尸检结果______</td></tr><tr><td rowspan="1" colspan="15">现病史(试验用药适应症以外，SAE发生时未恢复的疾病)</td></tr><tr><td rowspan="1" colspan="1">疾病名称</td><td rowspan="1" colspan="4">开始日期</td><td rowspan="1" colspan="3">结束日期</td><td rowspan="1" colspan="3">是否为家族史</td><td rowspan="1" colspan="4">备注</td></tr><tr><td rowspan="1" colspan="1">脂肪所</td><td rowspan="1" colspan="4">2022-01-17</td><td rowspan="1" colspan="3">/</td><td rowspan="1" colspan="3">NA</td><td rowspan="1" colspan="4"></td></tr><tr><td rowspan="1" colspan="1">肝囊肿</td><td rowspan="1" colspan="4">2024-04-03</td><td rowspan="1" colspan="3">/</td><td rowspan="1" colspan="3">NA</td><td rowspan="1" colspan="4"></td></tr><tr><td rowspan="1" colspan="1">胆囊肌腺症</td><td rowspan="1" colspan="4">2022-01-13</td><td rowspan="1" colspan="3"></td><td rowspan="1" colspan="3">NA</td><td rowspan="1" colspan="4"></td></tr><tr><td rowspan="1" colspan="1">脾肿大</td><td rowspan="1" colspan="4">2024-04-03</td><td rowspan="1" colspan="3">/</td><td rowspan="1" colspan="3">NA</td><td rowspan="1" colspan="4"></td></tr><tr><td rowspan="1" colspan="1">I]型糖尿病</td><td rowspan="1" colspan="4">2022-01-UK</td><td rowspan="1" colspan="3"></td><td rowspan="1" colspan="3">NA</td><td rowspan="1" colspan="4"></td></tr><tr><td rowspan="1" colspan="1">高血压</td><td rowspan="1" colspan="4">2022-UK-UK</td><td rowspan="1" colspan="3"></td><td rowspan="1" colspan="3">NA</td><td rowspan="1" colspan="4"></td></tr><tr><td rowspan="1" colspan="1">癌胚抗原升高</td><td rowspan="1" colspan="4">2024-03-29</td><td rowspan="1" colspan="3"></td><td rowspan="1" colspan="3">NA</td><td rowspan="1" colspan="4"></td></tr><tr><td rowspan="1" colspan="1">糖类抗原升高</td><td rowspan="1" colspan="4">2024-03-29</td><td rowspan="1" colspan="3">/</td><td rowspan="1" colspan="3">NA</td><td rowspan="1" colspan="4"></td></tr><tr><td rowspan="1" colspan="1">神经元特异烯 醇化酶升高</td><td rowspan="1" colspan="4">2024-03-29</td><td rowspan="1" colspan="3">/</td><td rowspan="1" colspan="3">NA</td><td rowspan="1" colspan="4"></td></tr><tr><td rowspan="1" colspan="1">非小细胞肺癌</td><td rowspan="1" colspan="4">2024-03-29</td><td rowspan="1" colspan="3">/</td><td rowspan="1" colspan="3">NA</td><td rowspan="1" colspan="4"></td></tr></table>', 
                [0], LabelType.TABLE),
            
            # 3. TABLE chunk on page 1 (corrected page)
            self.create_test_chunk("b91ea66fcee04f7bb3a5a58605b6e405", 
                '<table><tr><td rowspan="1" colspan="1">相关Ag21-1升 高</td><td rowspan="1" colspan="2"></td><td rowspan="1" colspan="2"></td><td rowspan="1" colspan="2"></td><td rowspan="1" colspan="3"></td></tr><tr><td rowspan="1" colspan="1">肝功能不全</td><td rowspan="1" colspan="2">2022-01-12</td><td rowspan="1" colspan="2">/</td><td rowspan="1" colspan="2">NA</td><td rowspan="1" colspan="3"></td></tr><tr><td rowspan="1" colspan="1">高尿酸血症</td><td rowspan="1" colspan="2">2022-01-12</td><td rowspan="1" colspan="2">/</td><td rowspan="1" colspan="2">NA</td><td rowspan="1" colspan="3"></td></tr><tr><td rowspan="1" colspan="1">肌陵激酶同工 隐升离</td><td rowspan="1" colspan="2">2024-04-03</td><td rowspan="1" colspan="2">1.</td><td rowspan="1" colspan="2">NA</td><td rowspan="1" colspan="3"></td></tr><tr><td rowspan="1" colspan="1">乳酸脱氢酶升 高</td><td rowspan="1" colspan="2">2024-04-03</td><td rowspan="1" colspan="2">/</td><td rowspan="1" colspan="2">NA</td><td rowspan="1" colspan="3"></td></tr><tr><td rowspan="1" colspan="1">尿红细胞升高</td><td rowspan="1" colspan="2">2024-04-03</td><td rowspan="1" colspan="2">2024.04.17</td><td rowspan="1" colspan="2">NA</td><td rowspan="1" colspan="3"></td></tr><tr><td rowspan="1" colspan="1">心律失常</td><td rowspan="1" colspan="2">2022-0112</td><td rowspan="1" colspan="2">/</td><td rowspan="1" colspan="2">NA</td><td rowspan="1" colspan="3"></td></tr><tr><td rowspan="1" colspan="1">肺曲霉病</td><td rowspan="1" colspan="2">2024-03-29</td><td rowspan="1" colspan="2">/</td><td rowspan="1" colspan="2">NA</td><td rowspan="1" colspan="3"></td></tr><tr><td rowspan="1" colspan="1">两下肢动脉硬 化班块形成</td><td rowspan="1" colspan="2">2022-01-13</td><td rowspan="1" colspan="2">/</td><td rowspan="1" colspan="2">NA</td><td rowspan="1" colspan="3"></td></tr><tr><td rowspan="1" colspan="1">左肺上叶小结 节</td><td rowspan="1" colspan="2">2024-03-25</td><td rowspan="1" colspan="2">/</td><td rowspan="1" colspan="2">NA</td><td rowspan="1" colspan="3"></td></tr><tr><td rowspan="1" colspan="1">前上纵隔结节</td><td rowspan="1" colspan="2">2024-03-25</td><td rowspan="1" colspan="2">/</td><td rowspan="1" colspan="2">NA</td><td rowspan="1" colspan="3"></td></tr><tr><td rowspan="1" colspan="1">右侧第8后肋陈 旧性骨折</td><td rowspan="1" colspan="2">2024-03-25</td><td rowspan="1" colspan="2">/</td><td rowspan="1" colspan="2">NA</td><td rowspan="1" colspan="3"></td></tr><tr><td rowspan="1" colspan="1">颅内多发缺血 灶</td><td rowspan="1" colspan="2">2024-03-30</td><td rowspan="1" colspan="2">/</td><td rowspan="1" colspan="2">N</td><td rowspan="1" colspan="3"></td></tr><tr><td rowspan="1" colspan="1">左侧颡枕叶班 片陈旧性病史</td><td rowspan="1" colspan="2">2024-03-30</td><td rowspan="1" colspan="2">/</td><td rowspan="1" colspan="2">NA</td><td rowspan="1" colspan="3"></td></tr><tr><td rowspan="1" colspan="1">冠心病</td><td rowspan="1" colspan="2">2024-03-27</td><td rowspan="1" colspan="2">/</td><td rowspan="1" colspan="2">NA</td><td rowspan="1" colspan="3"></td></tr><tr><td rowspan="1" colspan="1">肺动脉高压中 度</td><td rowspan="1" colspan="2">2024-03-29</td><td rowspan="1" colspan="2">/</td><td rowspan="1" colspan="2">NA</td><td rowspan="1" colspan="3"></td></tr><tr><td rowspan="1" colspan="1">及下胶水肿</td><td rowspan="1" colspan="2">2024.05.UK</td><td rowspan="1" colspan="2">/</td><td rowspan="1" colspan="2">NA</td><td rowspan="1" colspan="3"></td></tr><tr><td rowspan="1" colspan="10">既往病史(SAE发生前已恢复的疾病)</td></tr><tr><td rowspan="1" colspan="1">族病名称</td><td rowspan="1" colspan="2">开始日期</td><td rowspan="1" colspan="2">结束日期</td><td rowspan="1" colspan="2">是否为家族史</td><td rowspan="1" colspan="3">备注</td></tr><tr><td rowspan="1" colspan="1">肺炎</td><td rowspan="1" colspan="2">2024.04.30</td><td rowspan="1" colspan="2">2024.05.07</td><td rowspan="1" colspan="2">NA</td><td rowspan="1" colspan="3"></td></tr><tr><td rowspan="1" colspan="1"></td><td rowspan="1" colspan="2"></td><td rowspan="1" colspan="2"></td><td rowspan="1" colspan="2"></td><td rowspan="1" colspan="3"></td></tr><tr><td rowspan="1" colspan="10">过敏史：  ☑无  口有 (过敏原： )</td></tr><tr><td rowspan="1" colspan="10">饮酒史：  □无  ☑有 2-3两/日，约40余年</td></tr><tr><td rowspan="1" colspan="10">吸烟史：  □无 区有 2包/日，约40余年</td></tr><tr><td rowspan="1" colspan="10">其他：  ☑无  □有</td></tr><tr><td rowspan="1" colspan="10">相关实验室检查(如适用，选填)</td></tr><tr><td rowspan="1" colspan="1">检查项</td><td rowspan="1" colspan="1">检查日期</td><td rowspan="1" colspan="2">检查结果</td><td rowspan="1" colspan="2">单位</td><td rowspan="1" colspan="2">正常值下限</td><td rowspan="1" colspan="1">正常值上限</td><td rowspan="1" colspan="1">备注</td></tr><tr><td rowspan="1" colspan="1">透明质酸</td><td rowspan="1" colspan="1">2024.5.19</td><td rowspan="1" colspan="2">2437.16</td><td rowspan="1" colspan="2">Mg/ml</td><td rowspan="1" colspan="2">0</td><td rowspan="1" colspan="1">120</td><td rowspan="1" colspan="1"></td></tr><tr><td rowspan="1" colspan="1">层粘连蛋白</td><td rowspan="1" colspan="1">2024.5.19</td><td rowspan="1" colspan="2">652.73</td><td rowspan="1" colspan="2">mg/ml</td><td rowspan="1" colspan="2">0</td><td rowspan="1" colspan="1">130</td><td rowspan="1" colspan="1"></td></tr><tr><td rowspan="1" colspan="1">III型前胶原N 端肚</td><td rowspan="1" colspan="1">2024.5.19</td><td rowspan="1" colspan="2">96.46</td><td rowspan="1" colspan="2">mg/ml</td><td rowspan="1" colspan="2">0</td><td rowspan="1" colspan="1">15</td><td rowspan="1" colspan="1"></td></tr><tr><td rowspan="1" colspan="1">IV型胶原</td><td rowspan="1" colspan="1">2024.5.19</td><td rowspan="1" colspan="2">839</td><td rowspan="1" colspan="2">mg/ml</td><td rowspan="1" colspan="2">0</td><td rowspan="1" colspan="1">95</td><td rowspan="1" colspan="1"></td></tr></table>', 
                [1], LabelType.TABLE),
            
            # 4. TEXT chunk on page 1 
            self.create_test_chunk("b2c7948bcb9b48bd971c40540dd1f769", 
                '1-', 
                [1], LabelType.TEXT),
            
            # 5-14: 其余10个chunks的简化版本（使用简化内容以节省空间）
            # 5. TABLE chunk on page 2
            self.create_test_chunk("8d6c8fbabf12413da30e2988d3736b72", 
                '<table><tr><td rowspan="1" colspan="1">游离甲状膜素</td><td rowspan="1" colspan="1">2024.5.19</td><td rowspan="1" colspan="1">23.19</td><td rowspan="1" colspan="1">mg/ml</td><td rowspan="1" colspan="1">10</td><td rowspan="1" colspan="1">22</td><td rowspan="1" colspan="1"></td></tr><tr><td rowspan="1" colspan="1">三碘甲状腺原 氨酸</td><td rowspan="1" colspan="1">2024.5.19</td><td rowspan="1" colspan="1">0.75</td><td rowspan="1" colspan="1">mg/ml</td><td rowspan="1" colspan="1">0.8</td><td rowspan="1" colspan="1">1.9</td><td rowspan="1" colspan="1"></td></tr><tr><td rowspan="1" colspan="1">甲状隙责</td><td rowspan="1" colspan="1">2024.5.19</td><td rowspan="1" colspan="1">13.34</td><td rowspan="1" colspan="1">mg/ml</td><td rowspan="1" colspan="1">5</td><td rowspan="1" colspan="1">13</td><td rowspan="1" colspan="1"></td></tr><tr><td rowspan="1" colspan="1">慈愿杭原</td><td rowspan="1" colspan="1">2024.5.19</td><td rowspan="1" colspan="1">276.02</td><td rowspan="1" colspan="1">mg/ml</td><td rowspan="1" colspan="1">0</td><td rowspan="1" colspan="1">5</td><td rowspan="1" colspan="1"></td></tr><tr><td rowspan="1" colspan="1">缔类抗原CA125</td><td rowspan="1" colspan="1">2024.5.19</td><td rowspan="1" colspan="1">341.59</td><td rowspan="1" colspan="1">U/ml</td><td rowspan="1" colspan="1">0</td><td rowspan="1" colspan="1">35</td><td rowspan="1" colspan="1"></td></tr><tr><td rowspan="1" colspan="1">缔类抗原CA50</td><td rowspan="1" colspan="1">2024.5.19</td><td rowspan="1" colspan="1">153.47</td><td rowspan="1" colspan="1">U/ml</td><td rowspan="1" colspan="1">0</td><td rowspan="1" colspan="1">25</td><td rowspan="1" colspan="1"></td></tr><tr><td rowspan="1" colspan="1">糖类抗原CA15 3</td><td rowspan="1" colspan="1">2024.5.19</td><td rowspan="1" colspan="1">313.7</td><td rowspan="1" colspan="1">U/ml</td><td rowspan="1" colspan="1">0</td><td rowspan="1" colspan="1">35</td><td rowspan="1" colspan="1"></td></tr><tr><td rowspan="1" colspan="1">糖类抗原CA24 2</td><td rowspan="1" colspan="1">2024.5.19</td><td rowspan="1" colspan="1">27.25</td><td rowspan="1" colspan="1">U/ml</td><td rowspan="1" colspan="1">0</td><td rowspan="1" colspan="1">25</td><td rowspan="1" colspan="1"></td></tr><tr><td rowspan="1" colspan="1">筛类抗原199</td><td rowspan="1" colspan="1">2024.5.19</td><td rowspan="1" colspan="1">136.24</td><td rowspan="1" colspan="1">U/ml</td><td rowspan="1" colspan="1">0</td><td rowspan="1" colspan="1">35</td><td rowspan="1" colspan="1"></td></tr><tr><td rowspan="1" colspan="1">谷丙转氛跨</td><td rowspan="1" colspan="1">2024.5.19</td><td rowspan="1" colspan="1">153.8</td><td rowspan="1" colspan="1">U/L</td><td rowspan="1" colspan="1">0</td><td rowspan="1" colspan="1">65</td><td rowspan="1" colspan="1"></td></tr><tr><td rowspan="1" colspan="1">谷草转氮脚</td><td rowspan="1" colspan="1">2024.5.19</td><td rowspan="1" colspan="1">147.9</td><td rowspan="1" colspan="1">U/L</td><td rowspan="1" colspan="1">0</td><td rowspan="1" colspan="1">60</td><td rowspan="1" colspan="1"></td></tr><tr><td rowspan="1" colspan="1">谷草转氛酶同 工晦</td><td rowspan="1" colspan="1">2024.5.19</td><td rowspan="1" colspan="1">29.9</td><td rowspan="1" colspan="1">U/L</td><td rowspan="1" colspan="1">0</td><td rowspan="1" colspan="1">18</td><td rowspan="1" colspan="1"></td></tr><tr><td rowspan="1" colspan="1">总恩红素</td><td rowspan="1" colspan="1">2024.5.19</td><td rowspan="1" colspan="1">159.5</td><td rowspan="1" colspan="1">Umol/L</td><td rowspan="1" colspan="1">0</td><td rowspan="1" colspan="1">25</td><td rowspan="1" colspan="1"></td></tr><tr><td rowspan="1" colspan="1">直接胆红素</td><td rowspan="1" colspan="1">2024.5.19</td><td rowspan="1" colspan="1">118.43</td><td rowspan="1" colspan="1">Umol/L</td><td rowspan="1" colspan="1">0</td><td rowspan="1" colspan="1">6.84</td><td rowspan="1" colspan="1"></td></tr><tr><td rowspan="1" colspan="1">总蛋白</td><td rowspan="1" colspan="1">2024.5.19</td><td rowspan="1" colspan="1">80.5</td><td rowspan="1" colspan="1">8/L</td><td rowspan="1" colspan="1">62</td><td rowspan="1" colspan="1">85</td><td rowspan="1" colspan="1"></td></tr><tr><td rowspan="1" colspan="1">白蛋白</td><td rowspan="1" colspan="1">2024.5.19</td><td rowspan="1" colspan="1">27.9</td><td rowspan="1" colspan="1">g/L</td><td rowspan="1" colspan="1">35</td><td rowspan="1" colspan="1">55</td><td rowspan="1" colspan="1"></td></tr><tr><td rowspan="1" colspan="1">碱性磷酸酶</td><td rowspan="1" colspan="1">2024.5.19</td><td rowspan="1" colspan="1">330.4</td><td rowspan="1" colspan="1">U/L</td><td rowspan="1" colspan="1">45</td><td rowspan="1" colspan="1">150</td><td rowspan="1" colspan="1"></td></tr><tr><td rowspan="1" colspan="1">Y-谷氨酰转肽 酶</td><td rowspan="1" colspan="1">2024.5.19</td><td rowspan="1" colspan="1">1349.4</td><td rowspan="1" colspan="1">U/L</td><td rowspan="1" colspan="1">11</td><td rowspan="1" colspan="1">49</td><td rowspan="1" colspan="1"></td></tr><tr><td rowspan="1" colspan="1">总胆汁酸</td><td rowspan="1" colspan="1">2024.5.19</td><td rowspan="1" colspan="1">93.7</td><td rowspan="1" colspan="1">Umol/L</td><td rowspan="1" colspan="1">0</td><td rowspan="1" colspan="1">12</td><td rowspan="1" colspan="1"></td></tr><tr><td rowspan="1" colspan="1">甘胆暧CG</td><td rowspan="1" colspan="1">2024.5.19</td><td rowspan="1" colspan="1">66.49</td><td rowspan="1" colspan="1">Mg/L</td><td rowspan="1" colspan="1">0.4</td><td rowspan="1" colspan="1">2.78</td><td rowspan="1" colspan="1"></td></tr><tr><td rowspan="1" colspan="1">谷胱甘肽还原 酶</td><td rowspan="1" colspan="1">2024.5.19</td><td rowspan="1" colspan="1">145.6</td><td rowspan="1" colspan="1">U/L</td><td rowspan="1" colspan="1">33</td><td rowspan="1" colspan="1">73</td><td rowspan="1" colspan="1"></td></tr><tr><td rowspan="1" colspan="1">葡萄糖空腹</td><td rowspan="1" colspan="1">2024.5.19</td><td rowspan="1" colspan="1">8.93</td><td rowspan="1" colspan="1">Mmol/L</td><td rowspan="1" colspan="1">3.9</td><td rowspan="1" colspan="1">5.8</td><td rowspan="1" colspan="1"></td></tr><tr><td rowspan="1" colspan="1">肌酐</td><td rowspan="1" colspan="1">2024.5.19</td><td rowspan="1" colspan="1">146.3</td><td rowspan="1" colspan="1">Umol/L</td><td rowspan="1" colspan="1">17.7</td><td rowspan="1" colspan="1">107</td><td rowspan="1" colspan="1"></td></tr><tr><td rowspan="1" colspan="1">尿酸</td><td rowspan="1" colspan="1">2024.5.19</td><td rowspan="1" colspan="1">706.3</td><td rowspan="1" colspan="1">Umol/L</td><td rowspan="1" colspan="1">200</td><td rowspan="1" colspan="1">415</td><td rowspan="1" colspan="1"></td></tr><tr><td rowspan="1" colspan="1">钠</td><td rowspan="1" colspan="1">2024.5.19</td><td rowspan="1" colspan="1">131.6</td><td rowspan="1" colspan="1">Mmol/L</td><td rowspan="1" colspan="1">136</td><td rowspan="1" colspan="1">144</td><td rowspan="1" colspan="1"></td></tr><tr><td rowspan="1" colspan="1">乳酸脱氢酶</td><td rowspan="1" colspan="1">2024.5.19</td><td rowspan="1" colspan="1">340.6</td><td rowspan="1" colspan="1">U/L</td><td rowspan="1" colspan="1">155</td><td rowspan="1" colspan="1">300</td><td rowspan="1" colspan="1"></td></tr><tr><td rowspan="1" colspan="1">肌酸激酶同工 酶</td><td rowspan="1" colspan="1">2024.5.19</td><td rowspan="1" colspan="1">140</td><td rowspan="1" colspan="1">U/L</td><td rowspan="1" colspan="1">0</td><td rowspan="1" colspan="1">24</td><td rowspan="1" colspan="1"></td></tr><tr><td rowspan="1" colspan="1">降钙素原</td><td rowspan="1" colspan="1">2024.5.19</td><td rowspan="1" colspan="1">1.26</td><td rowspan="1" colspan="1">mg/ml</td><td rowspan="1" colspan="1">0</td><td rowspan="1" colspan="1">0.5</td><td rowspan="1" colspan="1"></td></tr><tr><td rowspan="1" colspan="1">血清淀粉样蛋 白A</td><td rowspan="1" colspan="1">2024.5.19</td><td rowspan="1" colspan="1">19.6</td><td rowspan="1" colspan="1">Mg/L</td><td rowspan="1" colspan="1">0</td><td rowspan="1" colspan="1">10</td><td rowspan="1" colspan="1"></td></tr><tr><td rowspan="1" colspan="1">铁蛋白</td><td rowspan="1" colspan="1">2024.5.19</td><td rowspan="1" colspan="1">1724.7</td><td rowspan="1" colspan="1">mg/ml</td><td rowspan="1" colspan="1">50</td><td rowspan="1" colspan="1">300</td><td rowspan="1" colspan="1"></td></tr><tr><td rowspan="1" colspan="1">庞脂肪酶</td><td rowspan="1" colspan="1">2024.5.19</td><td rowspan="1" colspan="1">287.6</td><td rowspan="1" colspan="1">U/L</td><td rowspan="1" colspan="1">5.6</td><td rowspan="1" colspan="1">51.3</td><td rowspan="1" colspan="1"></td></tr><tr><td rowspan="1" colspan="1">a-淀粉酶</td><td rowspan="1" colspan="1">2024.5.19</td><td rowspan="1" colspan="1">277.5</td><td rowspan="1" colspan="1">U/L</td><td rowspan="1" colspan="1">0</td><td rowspan="1" colspan="1">82</td><td rowspan="1" colspan="1"></td></tr></table>', 
                [2], LabelType.TABLE),
            
            # 6. IMAGE chunk on page 2  
            self.create_test_chunk("215ff33295e24a0ca79dbb6138dc9bed", 
                '', 
                [2], LabelType.IMAGE),
            
            # 7-9: TABLE chunks on page 3 (with real content from log)
            self.create_test_chunk("47ee491c3dd64e00ae2e1cd1c44044be", 
                '<table><tr><td rowspan="10" colspan="1"></td><td rowspan="1" colspan="4">试验用药信息 □未使用试验药品 图已使用试验药品 (如有多个试验用药。请复制此表格添加。如果是盲态试验：1.朱破盲：试验用药品中文名称、英文名称请填写"试验用药/盲态药 物"名称2.巳破盲：试验用药品巾文名称、英文名称请填写具体)。</td></tr><tr><td rowspan="1" colspan="1">药物中文名称</td><td rowspan="1" colspan="1">人脐带间充质干细胞注射液</td><td rowspan="1" colspan="1">药物英文名称</td><td rowspan="1" colspan="1">NA</td></tr><tr><td rowspan="1" colspan="1">活性成分</td><td rowspan="1" colspan="1">人脐带问充质干细胞</td><td rowspan="1" colspan="1">剂型</td><td rowspan="1" colspan="1">注射液</td></tr><tr><td rowspan="1" colspan="1">给药剂量</td><td rowspan="1" colspan="1">10ml</td><td rowspan="1" colspan="1">给药途径</td><td rowspan="1" colspan="1">支气管镜下给药</td></tr><tr><td rowspan="1" colspan="1">给药频次</td><td rowspan="1" colspan="1">1次</td><td rowspan="1" colspan="1">累计总剂量</td><td rowspan="1" colspan="1">6.0×10⁷个细胞</td></tr><tr><td rowspan="1" colspan="1">药物编号</td><td rowspan="1" colspan="1">F010</td><td rowspan="1" colspan="1">规格</td><td rowspan="1" colspan="1">6.0×10⁷个细胞/10mL/袋</td></tr><tr><td rowspan="1" colspan="1">生产厂家</td><td rowspan="1" colspan="1"></td><td rowspan="1" colspan="1">临床试验适应症</td><td rowspan="1" colspan="1">纤维化</td></tr><tr><td rowspan="1" colspan="1">药品分类</td><td rowspan="1" colspan="3"> □中药 □化药   ☑治疗用生物制品  □预防用生物制品</td></tr><tr><td rowspan="1" colspan="1">用药时问</td><td rowspan="1" colspan="3">2024年_4月9__日-_2024 ___年  4__月  9  日</td></tr><tr><td rowspan="1" colspan="1">用药处置</td><td rowspan="1" colspan="3"> □不详   □停止用药  □减少剂量  □增加剂量   □剂量不变 ☑不适用</td></tr><tr><td rowspan="1" colspan="2">药物中文名称</td><td rowspan="1" colspan="1">人脐带间充质干细胞注射液</td><td rowspan="1" colspan="1">药物英文名称</td><td rowspan="1" colspan="1">NA</td></tr><tr><td rowspan="1" colspan="2">活性成分</td><td rowspan="1" colspan="1">人脐带间充质干细胞</td><td rowspan="1" colspan="1">剂型</td><td rowspan="1" colspan="1">注射液</td></tr><tr><td rowspan="1" colspan="2">给药剂量</td><td rowspan="1" colspan="1">10ml</td><td rowspan="1" colspan="1">给药途径</td><td rowspan="1" colspan="1">支气管镜下给药</td></tr><tr><td rowspan="1" colspan="2">给药频次</td><td rowspan="1" colspan="1">1次</td><td rowspan="1" colspan="1">累计总剂量</td><td rowspan="1" colspan="1">6.0×10⁷个细胞</td></tr><tr><td rowspan="1" colspan="2">药物编号</td><td rowspan="1" colspan="1">F011</td><td rowspan="1" colspan="1">规格</td><td rowspan="1" colspan="1">6.0×107个细胞/10mL/袋</td></tr><tr><td rowspan="1" colspan="2">生产厂家</td><td rowspan="1" colspan="1"></td><td rowspan="1" colspan="1">临床试验适应症</td><td rowspan="1" colspan="1">化</td></tr><tr><td rowspan="1" colspan="2">药品分类</td><td rowspan="1" colspan="3"> □中药 □化药   ☑治疗用生物制品  口预防用生物制品</td></tr><tr><td rowspan="1" colspan="2">用药时问</td><td rowspan="1" colspan="3">2024年5月9 _日-2024 年5月9  日</td></tr><tr><td rowspan="1" colspan="2">用药处置</td><td rowspan="1" colspan="3"> □不详  口停止用药  口减少剂量  □增加剂量  □剂量不变 ☑不适用</td></tr></table>', 
                [3], LabelType.TABLE),
            self.create_test_chunk("c82270e5edeb4412914012215fe0aa3d", 
                '<table><tr><td rowspan="1" colspan="5">既往用药信息(既往用药收集既往使用且在严重不良事件发生前已停用的药物，至少收集最早严重不良事件发生日期至 14天前使用的药品/疫苗，无法明确得知使用期间的药品也记录在此)</td></tr><tr><td rowspan="1" colspan="1">药物通用名称</td><td rowspan="1" colspan="1">使用原因</td><td rowspan="1" colspan="1">剂量</td><td rowspan="1" colspan="1">开始日期</td><td rowspan="1" colspan="1">结束日期</td></tr><tr><td rowspan="1" colspan="1">伊曲康唑胶囊</td><td rowspan="1" colspan="1">治疗肺曲霉病</td><td rowspan="1" colspan="1">0.2g/bid</td><td rowspan="1" colspan="1">2024.04.01</td><td rowspan="1" colspan="1">2024.04.22</td></tr><tr><td rowspan="1" colspan="1">苯磺酸氦氯地平片</td><td rowspan="1" colspan="1">治疗高血压</td><td rowspan="1" colspan="1">5mg/qd</td><td rowspan="1" colspan="1">2023.uk.uk</td><td rowspan="1" colspan="1">2024.04.21</td></tr><tr><td rowspan="1" colspan="1">注射用头孢他啶</td><td rowspan="1" colspan="1">治疗肺炎</td><td rowspan="1" colspan="1">3g/bid</td><td rowspan="1" colspan="1">2024.05.02</td><td rowspan="1" colspan="1">2024.05.02</td></tr><tr><td rowspan="1" colspan="1">注射用盐酸环丙沙星</td><td rowspan="1" colspan="1">治疗肺炎</td><td rowspan="1" colspan="1">0.2g/bid</td><td rowspan="1" colspan="1">2024.05.02</td><td rowspan="1" colspan="1">2024.05.02</td></tr></table>', 
                [3], LabelType.TABLE),
            self.create_test_chunk("5ff2cbe57eda4fa2b8ae5421aa0c472b", 
                '<table><tr><td rowspan="1" colspan="5">合并用药信息(合并用药收集严重不良事件发生前开始使用且发生时正在使用的药品/疫苗；针对SAE的治疗用药和 SAE发生后的其他用药，请填写在最后的"SAE发生及处理的详细情况"栏。)</td></tr><tr><td rowspan="1" colspan="1">药物通用名称</td><td rowspan="1" colspan="1">使用原因</td><td rowspan="1" colspan="1">剂量</td><td rowspan="1" colspan="1">开始日期</td><td rowspan="1" colspan="1">结束日期</td></tr><tr><td rowspan="1" colspan="1">磷酸西格列汀片</td><td rowspan="1" colspan="1">治疗糖尿病</td><td rowspan="1" colspan="1">100mg/ qd</td><td rowspan="1" colspan="1">2022.01.uk</td><td rowspan="1" colspan="1">/</td></tr><tr><td rowspan="1" colspan="1">达格列净片</td><td rowspan="1" colspan="1">治疗糖尿病</td><td rowspan="1" colspan="1">10mg/q d</td><td rowspan="1" colspan="1">2024.04.01</td><td rowspan="1" colspan="1">/</td></tr><tr><td rowspan="1" colspan="1">拉西地平片</td><td rowspan="1" colspan="1">治疗高血压</td><td rowspan="1" colspan="1">4mg/qd</td><td rowspan="1" colspan="1">2023.04.02</td><td rowspan="1" colspan="1">/</td></tr><tr><td rowspan="1" colspan="1">乙磺酸尼达尼布软胶囊</td><td rowspan="1" colspan="1">治疗特发性肺间质肺纤维化</td><td rowspan="1" colspan="1">150mg/ bid</td><td rowspan="1" colspan="1">2024.04.02</td><td rowspan="1" colspan="1">/</td></tr></table>', 
                [3], LabelType.TABLE),
                
            # 10-11: TABLE chunks on page 4 (with real content from log)
            self.create_test_chunk("c98914c3506c4322a13386cd98cd35a2", 
                '<table><tr><td rowspan="1" colspan="5">严重不良事件信息(如同时发生多个严重不良事件，请复制此表格添加)</td></tr><tr><td rowspan="1" colspan="1">不良事件名称(诊断)</td><td rowspan="1" colspan="4">肝损伤</td></tr><tr><td rowspan="1" colspan="1">开始时问</td><td rowspan="1" colspan="2">2024年5月18日</td><td rowspan="1" colspan="1">若痊愈/座愈伴有 后遗症或死亡，结 束时间</td><td rowspan="1" colspan="1">2024  年5_月23日</td></tr><tr><td rowspan="1" colspan="1">持续时问</td><td rowspan="1" colspan="2">6天</td><td rowspan="1" colspan="1">严重程度分级</td><td rowspan="1" colspan="1">V级</td></tr><tr><td rowspan="1" colspan="1">严重性标准</td><td rowspan="1" colspan="2">图导致死亡  口功能丧失/致残  □危及生命  ☑导致住院/延长住院时间  □导致先天性异常/出生缺陷  □其他重要医学事件</td><td rowspan="1" colspan="1">转归</td><td rowspan="1" colspan="1"> 口痊愈  口好转/级解  □未好转/未级解/持续  □痊愈伴有后遗症  ☑致死  □未知</td></tr><tr><td rowspan="1" colspan="1">国内SAE报道</td><td rowspan="1" colspan="2"> □有 冈无  □不详</td><td rowspan="1" colspan="1">国外SAE报道</td><td rowspan="1" colspan="1"> □有  口无  ☑不详</td></tr><tr><td rowspan="1" colspan="5">相关性评价(盲态试验未破盲时，名称填写试验用药；若为联合用药，对不同成分分别进行评价)</td></tr><tr><td rowspan="1" colspan="1">药品名</td><td rowspan="1" colspan="4">人脐带间充质干细胞注射液</td></tr><tr><td rowspan="1" colspan="1">相关性评价</td><td rowspan="1" colspan="4"> □肯定相关  □很可能相关  □可能相关 冈可能无关  □不相关</td></tr><tr><td rowspan="1" colspan="2">停药或减量后，反应是否消失或减轻?</td><td rowspan="1" colspan="3"> □是  □否  □不详  ☑不适用</td></tr><tr><td rowspan="1" colspan="2">再次使用试验药品后，是否再次出现同样反应?</td><td rowspan="1" colspan="3"> □是  □否  □不详  ☑不适用(未重新给药)</td></tr></table>', 
                [4], LabelType.TABLE),
            self.create_test_chunk("c3b9b02d9927468cb729ad4e2ccf3317", 
                '<table><tr><td rowspan="1" colspan="1">SAE发生及处理的详细情况(如同时发生多个严重不良事件，可统一记录在此处)</td></tr><tr><td rowspan="1" colspan="1">[基本信息] 受试者 男，汉族，年龄：59岁，受试者编号   ，因治疗 组本临床试验，于2024年04月02 日签署知情同意书"探索儿 研究" (方案编号为： 101,版本号：2.0版，版本日期：2023年06月16日)、完善筛选期检查，于2024.04.09第一次给予药物编号 为 液10ml,于2024.05.09第二次给予F011人脐带间充质干细胞注射液10ml. 【相关病史：合并疾病和既往病史] 受试者的合并疾病，既往病史，台并用药情况己于上表合并用药部分详细表述。 [不良事件情况] 思者家属于2024.05.20晚上19:35告知患者于2024.05.18因人意识不清楚，大小便失禁在上海市奉贤区古华医院办理入院， 2024.05.21追问患者家属，告知人清醒，反馈部分检查报告并获知诊断为肝损伤，入院后的相关检查具体异常值见表格。 患者家属于2024.05.23晚19:25告知患者去世，2024.05.24早上家属微信反馈患者死亡小结，报告见附件。患者入院时主要症状及体 征：患者因"乏力、纳差1月余"入院，查体：神志清晰，呼吸平稳，全身皮肤粘膜轻度黄染，全身浅表淋巴结无肿大，双巩膜明显 黄染，颈软，无颈静脉充盈，无颈静脉怒张，两肺呼吸音粗，未闻及明显干湿啰音，心率90次/分，律齐，无杂音，腹部略膨隆，腹 围97厘米，腹软，上腹部轻压痛，无腹部反跳痛，肝脏未触及，颈静脉回流征阴性，移动性浊音阳性，双下肢无浮肿。 [不良事件的处理] 截至SAE上报时家属未提供住院病历信息。2024.05.24早上家属微信反馈患者死亡小结，患者入院后完善检查，2024-05-20 CT检查 报告：检查结果：1两局部肺通气不良：两肺局限性肺气肿：两肺间质纤维化：右肺门部淋巴结钙化：心影饱满：肺动脉干内径增宽：主动脉壁 及冠脉局部钙化：两侧局部胸膜增厚、黏连；请结合临床及相关检查，随访。附见：奇静脉、半奇静脉扩张。2、肝脾肿大、肝硬化：脂肪 肝，腹腔少量积液，餐后胆囊?肝胃间隙及腹膜后多发淋巴结及肿大：胃窦部、十二指肠局部肠壁增厚：请结合腹部增强检查。2024-05- 21彩超检查报告：检查结果：肝脏部分显示肝呈慢性肝病改变(提示早期肝硬化)肝内结节形成中量腹水胆囊显示不清胰腺显示不清(建议 消胀后复查)脾略大膀胱充盈差前列腺显示不清、给予保肝：注射用还原型谷胱甘肽、水飞蓟兵葡甲胺片；利胆退黄：丁二磺酸腺甘蛋氨 酸、苦黄、熊去氧胆酸胶囊；护胃，保护胃黏膜：注射用法莫替丁：抗感染：头孢美唑；比阿培南：利尿：呋塞米、螺内酯：通便，乳果糖：营养 支持，人血白蛋白；氨茶碱解痉平喘：肾衰竭对症活血补气，通腑泻浊；经治疗患者病情未见好转，请公共卫生临床中心钱志平主任会诊 后建议加强抗感染，抗生素升级，保护肾功能，注意生命体征变化。与家属沟通后，告知病情，目前患者病情重，随时可出现生命危 险，建议上级医院进一步诊治，患者表示拒绝，仍坚持住本院治疗，病程中患者测血压示80/30mmHG,呼吸明显急促，予以洛贝林及 尼可刹米注射液、多巴胺及向羟胺注射液：升压及呼吸兴奋治疗，19:00左右患者家属即刻诉患者心电监护出现报警，心电监护仪呈一 直线。即刻监测患者血压测不出，呼吸、脉搏、心率为0,血氧饱和度为0,大动脉搏动消失，双瞳孔散大固定，对光反射消失，测 心电图示一直线。于2024.5.23,19:06宜告临床死亡。治疗结果：死亡 [试验用药使用情况J 受试者的试验药物使用情况己于上表试验用药部分详细表述。</td></tr></table>', 
                [4], LabelType.TABLE),
                
            # 12: IMAGE chunk on page 5
            self.create_test_chunk("435563ba0f924c8b9bf174a583b7af03", 
                '', 
                [5], LabelType.IMAGE),
            
            # 13: TABLE chunk on page 5 (with real content from log)
            self.create_test_chunk("3f23cdfd0d4b4601b4490d2cdee57b77", 
                '<table><tr><td rowspan="1" colspan="1">[对可疑产品采取的措施] 不适用 [不良事件转归 至此报告时，患者有腹水，人清醒，仍住院，转归持续。 2024.05.24早上家属微信反馈患者治疗结果，死亡。转归：死亡。 [研究者相关性评价] 此次SAE与研究药物人脐带间充质干细胞注射液可能无关，追溯病史，由于总者长期有腐肋肝病史，筛选期轻度肝功能不全，  不 除患者二次给药后合并用药引起的肝功能损伤。总者本次住院痛胚抗原指标异常升高，不排除肿瘤可能。待患者反馈病历及报告后再 避一步评价相关性。</td></tr><tr><td rowspan="1" colspan="1">2024.05.24结合患者家属提供死亡小结报告，根据患者检查结果及患者基础疾病，判定患者死亡与肝功能衰竭、肝硬化失代偿期、酒 辅性肝病、胆汁淤积、自发性腹膜炎、肾功能不全、高血压、糖尿病、便秘、肺气肿、消化道肿瘤?均可能有关，与研究药物人脐带 间充质干细胞注射液可能无关。 人</td></tr></table>', 
                [5], LabelType.TABLE),
            
            # 14: TEXT chunk on page 5
            self.create_test_chunk("1aabdf0f7cfc4acf97fe3e44621489a3", 
                ' 20ip.5.2报告者签名： 日期：', 
                [5], LabelType.TEXT),
        ]

    def get_expected_table_indices_from_log(self):
        """返回基于日志数据的预期table_indices结果 - 基于真实日志数据修正"""
        return {
            0: {
                "first_table_index": 1,
                "first_chunk_id": "cfe5fc6321be4ecf83d5d9dbcb50e009",
                "from_last_table_index": 1,
                "last_chunk_id": "cfe5fc6321be4ecf83d5d9dbcb50e009",
            },
            1: {
                "first_table_index": 0,
                "first_chunk_id": "b91ea66fcee04f7bb3a5a58605b6e405",
                "from_last_table_index": 2,
                "last_chunk_id": "b91ea66fcee04f7bb3a5a58605b6e405",
            },
            2: {
                "first_table_index": 0,
                "first_chunk_id": "8d6c8fbabf12413da30e2988d3736b72",
                "from_last_table_index": 2,
                "last_chunk_id": "8d6c8fbabf12413da30e2988d3736b72",
            },
            3: {
                "first_table_index": 0,
                "first_chunk_id": "47ee491c3dd64e00ae2e1cd1c44044be",
                "from_last_table_index": 1,
                "last_chunk_id": "5ff2cbe57eda4fa2b8ae5421aa0c472b",
            },
            4: {
                "first_table_index": 0,
                "first_chunk_id": "c98914c3506c4322a13386cd98cd35a2",
                "from_last_table_index": 1,
                "last_chunk_id": "c3b9b02d9927468cb729ad4e2ccf3317",
            },
            5: {
                "first_table_index": 1,
                "first_chunk_id": "3f23cdfd0d4b4601b4490d2cdee57b77",
                "from_last_table_index": 2,
                "last_chunk_id": "3f23cdfd0d4b4601b4490d2cdee57b77",
            }
        }
        
    def test_classify_and_mark_table_indices_real_log_data(self):
        """测试 classify_and_mark_table_indices 基于真实日志数据的情况 - 完整14个chunks"""
        # 使用完整的14个chunks
        chunks = self.get_all_14_chunks_from_log()
        expected_table_indices = self.get_expected_table_indices_from_log()
        
        result = classify_and_mark_table_indices(chunks)
        
        # 验证结果结构
        assert isinstance(result, dict)
        
        # 验证包含所有期待的页面
        expected_pages = [0, 1, 2, 3, 4, 5]
        for page in expected_pages:
            assert page in result
            
        # 具体验证每个页面的table_indices配置 - 基于真实日志数据的断言
        for page_num, expected_indices in expected_table_indices.items():
            assert page_num in result, f"Missing page {page_num} in result"
            actual_indices = result[page_num]
            
            # 断言具体的字段值
            assert actual_indices["first_table_index"] == expected_indices["first_table_index"], \
                f"Page {page_num}: first_table_index mismatch"
            assert actual_indices["first_chunk_id"] == expected_indices["first_chunk_id"], \
                f"Page {page_num}: first_chunk_id mismatch"  
            assert actual_indices["from_last_table_index"] == expected_indices["from_last_table_index"], \
                f"Page {page_num}: from_last_table_index mismatch"
            assert actual_indices["last_chunk_id"] == expected_indices["last_chunk_id"], \
                f"Page {page_num}: last_chunk_id mismatch"
            
        print(f"classify_and_mark_table_indices result verification PASSED: {result}")  # 调试输出
        
    @patch('modules.cross_table.cross_page_merge.cross_page_merge_by_html')
    def test_process_and_merge_chunks_real_log_data(self, mock_merge):
        """测试 process_and_merge_chunks 基于真实日志数据的情况 - 完整14个chunks"""
        # 使用完整的14个chunks和提取的table_indices
        chunks = self.get_all_14_chunks_from_log()
        table_indices = self.get_expected_table_indices_from_log()
        
        mock_merge.return_value = (True, ["<table><tr><td>Merged Content</td></tr></table>"])
        
        result = process_and_merge_chunks(chunks, table_indices)
        
        # 验证结果
        assert isinstance(result, list)
        print(f"process_and_merge_chunks result: {result}")  # 调试输出

    def test_merge_chunks_with_base_real_log_data(self):
        """测试 merge_chunks_with_base 基于真实日志数据的情况"""
        # 基于日志构造关键chunks
        chunks = [
            self.create_test_chunk("cfe5fc6321be4ecf83d5d9dbcb50e009", 
                '<table><tr><td rowspan="1" colspan="15">临床项目及报告单位信息</td></tr></table>', 
                [0], LabelType.TABLE),
            self.create_test_chunk("c3b9b02d9927468cb729ad4e2ccf3317", 
                '<table><tr><td rowspan="1" colspan="1">SAE发生及处理的详细情况</td></tr></table>', 
                [4], LabelType.TABLE),
            self.create_test_chunk("3f23cdfd0d4b4601b4490d2cdee57b77", 
                '<table><tr><td rowspan="1" colspan="1">[对可疑产品采取的措施] 不适用</td></tr></table>', 
                [5], LabelType.TABLE)
        ]
        
        # 基于真实日志中merged_results构造数据  
        merged_results = [
            {
                'content': ['<table><tr><td rowspan="1" colspan="15">临床项目及报告单位信息</td></tr></table>', '<table><tr><td rowspan="1" colspan="1">游离甲状膜素</td></tr></table>'],
                'chunk_ids': ['cfe5fc6321be4ecf83d5d9dbcb50e009', 'b91ea66fcee04f7bb3a5a58605b6e405', '8d6c8fbabf12413da30e2988d3736b72']
            },
            {
                'content': ['<table><tr><td rowspan="1" colspan="1">SAE发生及处理的详细情况</td></tr></table>', '<table><tr><td rowspan="1" colspan="1">[对可疑产品采取的措施] 不适用</td></tr></table>'],
                'chunk_ids': ['c3b9b02d9927468cb729ad4e2ccf3317', '3f23cdfd0d4b4601b4490d2cdee57b77']
            }
        ]

        result = merge_chunks_with_base(chunks, merged_results)

        assert len(result) >= 1  # 应该有合并后的结果
        print(f"merge_chunks_with_base result length: {len(result)}")  # 调试输出

    @patch('modules.cross_table.cross_page_merge.cross_page_merge_by_html')
    def test_process_and_merge_chunks_real_log_data_with_expected_results(self, mock_merge):
        """测试 process_and_merge_chunks 基于真实日志数据并验证预期结果"""
        # 使用完整的14个chunks和提取的table_indices
        chunks = self.get_all_14_chunks_from_log()
        table_indices = self.get_expected_table_indices_from_log()
        
        # mock 合并函数返回基于真实数据的合并结果  
        mock_merge.return_value = (True, ["<table><tr><td>Merged Result from Real Log</td></tr></table>"])
        
        result = process_and_merge_chunks(chunks, table_indices)
        
        # 验证结果
        assert isinstance(result, list)
        print(f"process_and_merge_chunks with expected results: {result}")  # 调试输出